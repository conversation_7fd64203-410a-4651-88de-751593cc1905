version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: system-service-mysql
    ports:
      - '3306:3306'
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: system_service
    volumes:
      - mysql_data:/var/lib/mysql
    healthcheck:
      test: ['CMD', 'mysqladmin', 'ping', '-h', 'localhost']
      timeout: 20s
      retries: 10

volumes:
  mysql_data:
    driver: local
