# RBAC 权限系统使用指南

## 概述

本文档详细介绍如何使用已实现的 RBAC（基于角色的访问控制）权限系统。该系统支持多层级角色体系、细粒度权限控制、部门级权限隔离等企业级功能。

## 系统架构

### 核心组件

1. **服务层 (Services)**

   - `AuthorizationService` - 权限检查核心逻辑
   - `RBACUserRoleService` - 用户角色管理
   - `RBACDepartmentService` - 部门管理
   - `DepartmentMemberService` - 部门成员管理
   - `DataScopeService` - 数据权限范围管理
   - `MenuPermissionService` - 菜单权限管理
   - `CacheService` - 权限缓存管理

2. **中间件 (Middlewares)**

   - `requirePermissions` - 权限检查
   - `requireRoles` - 角色检查
   - `requireDataScope` - 数据权限检查
   - `requireSystemAdmin` - 系统管理员检查
   - `requireDepartmentAdmin` - 部门管理员检查

3. **API 路由 (Routes)**
   - `/api/system/rbac/*` - RBAC 相关 API
   - `/api/system/roles/*` - 角色管理 API
   - `/api/system/permissions/*` - 权限管理 API
   - `/api/system/menus/*` - 菜单管理 API

## 快速开始

### 1. 初始化系统

首先运行种子数据初始化基础权限和角色：

```bash
cd backends/system
pnpm run seed
```

这将创建：

- 基础权限（系统管理、数据权限等）
- 基础角色（超级管理员、部门管理员、普通用户）
- 示例部门结构

### 2. 基础使用示例

#### 2.1 权限检查中间件

```typescript
import { requirePermissions, requireRoles, requireDataScope } from '@/middlewares/rbac'

// 基础权限检查
app.get('/users', requirePermissions('system:user:list'), async (c) => {
  // 只有拥有 system:user:list 权限的用户才能访问
  const users = await userService.getUsers()
  return c.json({ data: users })
})

// 多权限检查（需要所有权限）
app.post('/users', requirePermissions(['system:user:create', 'data:scope:department'], 'ALL'), async (c) => {
  // 需要同时拥有创建用户权限和部门数据权限
  const userData = await c.req.json()
  const user = await userService.createUser(userData)
  return c.json({ data: user })
})

// 多权限检查（任一权限即可）
app.get('/reports', requirePermissions(['business:project:list', 'business:task:list'], 'ANY'), async (c) => {
  // 拥有项目查看权限或任务查看权限即可访问
  const reports = await reportService.getReports()
  return c.json({ data: reports })
})

// 角色检查
app.get('/admin/dashboard', requireRoles(['SUPER_ADMIN', 'DEPT_ADMIN'], 'ANY'), async (c) => {
  // 只有超级管理员或部门管理员可以访问
  const dashboardData = await adminService.getDashboard()
  return c.json({ data: dashboardData })
})

// 数据权限检查
app.get(
  '/departments/:deptId/members',
  requirePermissions('system:department:list'),
  requireDataScope('department', 'read'),
  async (c) => {
    const deptId = c.req.param('deptId')
    // 自动检查用户是否有权限访问该部门的数据
    const members = await departmentService.getMembers(deptId)
    return c.json({ data: members })
  }
)
```

#### 2.2 服务层使用

```typescript
import { authorizationService } from '@/services/authorization.service'
import { rbacUserRoleService } from '@/services/rbac-user-role.service'

// 检查用户权限
const hasPermission = await authorizationService.checkPermission(userId, 'system:user:create')

// 检查多个权限
const hasAllPermissions = await authorizationService.checkPermissions(
  userId,
  ['system:user:create', 'system:user:update'],
  'ALL'
)

// 检查角色
const isAdmin = await authorizationService.checkRole(userId, 'SUPER_ADMIN', 'GLOBAL')

// 获取用户所有权限
const userPermissions = await authorizationService.getUserEffectivePermissions(userId)

// 获取用户角色
const userRoles = await rbacUserRoleService.getUserEffectiveRoles(userId)

// 分配角色给用户
await rbacUserRoleService.assignGlobalRoles(userId, ['DEVELOPER', 'TESTER'], auditFields)
```

## 权限系统详解

### 权限格式

权限采用三段式格式：`模块:资源:操作`

```
system:user:list     - 查看用户列表
system:user:create   - 创建用户
system:user:update   - 更新用户
system:user:delete   - 删除用户
system:user:*        - 用户管理所有权限
system:*             - 系统管理所有权限
*                    - 超级权限（所有权限）
```

### 权限继承

权限支持通配符继承：

- `*` - 拥有所有权限
- `system:*` - 拥有所有系统管理权限
- `system:user:*` - 拥有所有用户管理权限

### 数据权限范围

- `data:scope:all` - 全部数据权限
- `data:scope:department` - 本部门数据权限
- `data:scope:subordinate` - 下级部门数据权限
- `data:scope:self` - 仅个人数据权限

## 角色管理

### 角色类型

1. **系统角色** (`type: 0`) - 系统预定义，不可删除

   - `SUPER_ADMIN` - 超级管理员
   - `DEPT_ADMIN` - 部门管理员
   - `SUB_DEPT_ADMIN` - 子部门管理员

2. **业务角色** (`type: 1`) - 可自定义配置
   - `BUSINESS_ADMIN` - 业务系统管理员
   - `DEVELOPER` - 开发人员
   - `TESTER` - 测试人员
   - `NORMAL_USER` - 普通用户

### 角色分配

```typescript
// 分配全局角色
await rbacUserRoleService.assignGlobalRoles(userId, ['DEVELOPER'], auditFields)

// 分配部门角色
await departmentMemberService.addMember(deptId, userId, 'MEMBER', auditFields)
// 然后为部门成员分配角色
await db.departmentMemberRole.create({
  data: {
    userId,
    roleId,
    departmentId: deptId,
    ...auditFields,
  },
})
```

## 部门权限管理

### 部门层级结构

部门支持无限级嵌套，使用 `treePath` 字段维护层级关系：

```
总公司 (treePath: "COMPANY")
├── 技术部 (treePath: "COMPANY.TECH")
│   ├── 前端组 (treePath: "COMPANY.TECH.FE")
│   └── 后端组 (treePath: "COMPANY.TECH.BE")
└── 市场部 (treePath: "COMPANY.MARKET")
```

### 部门成员类型

- `OWNER` - 部门负责人
- `MANAGER` - 部门管理员
- `MEMBER` - 普通成员

### 部门权限检查

```typescript
import { dataScopeService } from '@/services/data-scope.service'

// 检查部门数据访问权限
const canAccess = await dataScopeService.checkDepartmentDataAccess(userId, targetDeptId)

// 检查下级部门管理权限
const canManage = await dataScopeService.checkSubordinateDataAccess(userId, targetDeptId)

// 获取用户可访问的部门列表
const accessibleDepts = await dataScopeService.getUserAccessibleDepartments(userId)
```

## 菜单权限

### 菜单权限分配

```typescript
import { menuPermissionService } from '@/services/menu-permission.service'

// 为角色分配菜单权限
await menuPermissionService.assignMenusToRole(roleId, menuIds)

// 获取用户可访问的菜单
const userMenus = await menuPermissionService.getUserMenus(userId)

// 检查菜单访问权限
const hasMenuAccess = await menuPermissionService.checkMenuAccess(userId, '/admin/users')
```

### 前端菜单渲染

```typescript
// 获取用户菜单
const response = await fetch('/api/system/rbac/me/menus')
const { data: menus } = await response.json()

// 根据菜单权限动态渲染导航
function renderNavigation(menus) {
  return menus.map((menu) => ({
    key: menu.id,
    label: menu.name,
    icon: menu.icon,
    path: menu.path,
    children: menu.children?.length > 0 ? renderNavigation(menu.children) : undefined,
  }))
}
```

## API 使用示例

### 获取当前用户权限信息

```typescript
// GET /api/system/rbac/me/permissions
{
  "data": {
    "userId": "user123",
    "permissions": [
      "system:user:list",
      "system:user:create",
      "business:project:list"
    ],
    "dataScope": ["department", "self"],
    "roles": {
      "globalRoles": [
        { "id": "role1", "code": "DEVELOPER", "name": "开发人员" }
      ],
      "departmentRoles": [
        {
          "departmentId": "dept1",
          "departmentName": "技术部",
          "roles": [
            { "id": "role2", "code": "TECH_LEAD", "name": "技术负责人" }
          ]
        }
      ]
    }
  }
}
```

### 权限检查 API

```typescript
// POST /api/system/rbac/check-permissions
{
  "permissions": ["system:user:create", "system:user:update"]
}

// Response
{
  "data": {
    "system:user:create": true,
    "system:user:update": false
  }
}
```

### 菜单权限检查

```typescript
// POST /api/system/rbac/check-menu-access
{
  "menuIds": ["menu1", "menu2", "menu3"]
}

// Response
{
  "data": {
    "menu1": true,
    "menu2": true,
    "menu3": false
  }
}
```

## 最佳实践

### 1. 权限设计原则

- **最小权限原则**：用户默认无权限，需显式授权
- **职责分离**：不同角色拥有不同的权限范围
- **权限继承**：合理使用通配符权限，避免权限冗余

### 2. 角色设计建议

- 系统角色用于基础权限管理，不要随意修改
- 业务角色根据实际业务需求灵活配置
- 避免为单个用户创建专用角色

### 3. 部门权限管理

- 部门管理员只能管理自己拥有的角色
- 父级部门管理员可以管理子级部门
- 合理设置数据权限范围，避免权限泄露

### 4. 性能优化

- 权限检查结果会被缓存，避免频繁数据库查询
- 批量权限检查比单个检查更高效
- 定期清理无效的权限缓存

### 5. 安全考虑

- 权限变更后及时清除相关缓存
- 敏感操作需要二次验证
- 定期审计权限分配情况

## 故障排除

### 常见问题

1. **权限检查失败**

   - 检查用户是否有对应的角色
   - 确认角色是否有对应的权限
   - 检查权限格式是否正确

2. **部门权限问题**

   - 确认用户是否是部门成员
   - 检查部门层级关系是否正确
   - 验证数据权限范围设置

3. **菜单不显示**
   - 检查角色是否有菜单权限
   - 确认菜单是否启用
   - 验证菜单层级关系

### 调试工具

```typescript
// 获取用户详细权限信息
const userPermissions = await authorizationService.getUserEffectivePermissions(userId)
console.log('用户权限:', Array.from(userPermissions))

// 获取用户角色信息
const userRoles = await rbacUserRoleService.getUserEffectiveRoles(userId)
console.log('用户角色:', userRoles)

// 检查具体权限
const hasPermission = await authorizationService.checkPermission(userId, 'system:user:list')
console.log('是否有权限:', hasPermission)
```

## 扩展开发

### 添加新权限

1. 在种子数据中添加新权限定义
2. 为相关角色分配新权限
3. 在路由中使用权限检查中间件

### 自定义权限检查

```typescript
import { requireCustomPermission } from '@/middlewares/rbac'

app.get(
  '/custom-resource',
  requireCustomPermission(async (userId, context) => {
    // 自定义权限检查逻辑
    const user = await db.user.findUnique({ where: { id: userId } })
    return user?.level >= 5 // 例如：用户等级大于等于5
  }, '用户等级不足'),
  async (c) => {
    // 业务逻辑
  }
)
```

### 权限缓存扩展

```typescript
// 自定义缓存策略
class CustomCacheService extends CacheService {
  async setUserPermissionsCache(userId: string, permissions: string[], ttl = 600) {
    // 自定义缓存逻辑，例如使用 Redis
    await redis.setex(`user:permissions:${userId}`, ttl, JSON.stringify(permissions))
  }
}
```

## 总结

本 RBAC 权限系统提供了完整的企业级权限管理功能，支持：

- ✅ 多层级角色体系
- ✅ 细粒度权限控制
- ✅ 部门级权限隔离
- ✅ 菜单权限管理
- ✅ 数据权限范围控制
- ✅ 权限缓存优化
- ✅ 完整的 API 接口
- ✅ 灵活的中间件支持

通过合理使用这些功能，可以构建安全、高效的权限管理系统，满足复杂的企业级应用需求。
