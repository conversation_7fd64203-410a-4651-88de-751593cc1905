# RBAC 企业级权限控制系统设计方案

## 1. 概述

### 1.1 目标

在 `backends/system` 中构建一套企业级的基于角色的访问控制（RBAC）系统，支持：

- 多层级角色体系（全局角色 + 部门角色）
- 细粒度权限控制（功能权限 + 数据权限）
- 灵活的菜单权限管理
- 部门级权限隔离
- 可扩展的权限模型

### 1.2 核心原则

- **最小权限原则**：用户默认无权限，需显式授权
- **权限继承**：子角色可继承父角色权限
- **权限合并**：用户最终权限 = 全局角色权限 ∪ 部门成员角色权限
- **数据隔离**：部门数据权限严格隔离
- **审计追踪**：所有权限变更可追溯

### 1.3 技术栈集成

- **认证层**：`better-auth` 负责用户认证、会话管理（路由：`/api/auth/*`）
- **授权层**：本 RBAC 系统负责权限控制（路由：`/api/system/*`）
- **数据层**：基于 Prisma Schema 的完整权限模型

## 2. 核心概念与模型

### 2.1 权限模型层次

```
用户 (User)
├── 全局角色 (UserRole) → 角色 (Role) → 权限 (Permission)
└── 部门成员身份 (DepartmentMember)
    └── 部门角色 (DepartmentMemberRole) → 角色 (Role) → 权限 (Permission)
```

### 2.2 权限类型分类

#### 2.2.1 菜单权限 (Menu Permissions)

控制用户可以访问的前端界面范围，包括：

- **微应用权限**：控制用户可以访问哪些微应用
- **页面菜单权限**：控制用户可以看到哪些导航菜单
- **功能按钮权限**：控制页面内的操作按钮显隐

通过角色-菜单关联 (`RoleMenu`) 实现，前端根据用户菜单权限动态渲染界面。

#### 2.2.2 数据权限 (Data Permissions)

控制用户可以访问的数据范围，采用权限键 (Permission Code) 进行控制：

**权限键格式**：`模块:资源:操作`

- `system:user:list` - 查看用户列表
- `system:user:create` - 创建用户
- `system:user:update` - 更新用户信息
- `system:user:delete` - 删除用户
- `system:role:manage` - 角色管理
- `business:project:create` - 创建项目
- `business:project:update` - 更新项目

**权限继承关系**：

- 父级权限自动包含所有子级权限
- `system:user:*` 包含 `system:user:list`、`system:user:create`、`system:user:update`、`system:user:delete`
- `system:*` 包含所有 `system:` 开头的权限
- `*` 表示超级权限，包含所有权限

**数据范围控制**：

- `data:scope:all` - 全部数据权限
- `data:scope:department` - 本部门数据权限
- `data:scope:self` - 仅个人数据权限
- `data:scope:subordinate` - 下级部门数据权限

### 2.3 角色体系设计

#### 2.3.1 角色类型

- **系统角色** (`type: 0`)：系统预定义，不可删除

  - `SUPER_ADMIN` - 超级管理员
  - `DEPT_ADMIN` - 部门管理员
  - `SUB_DEPT_ADMIN` - 子部门管理员

- **业务角色** (`type: 1`)：业务自定义，可灵活配置
  - `BUSINESS_ADMIN` - 业务系统管理员
  - `DEVELOPER` - 开发人员
  - `TESTER` - 测试人员
  - `NORMAL_USER` - 普通用户

#### 2.3.2 角色层级与继承

角色支持层级结构，子角色自动继承父角色的所有权限：

```
SUPER_ADMIN (超级管理员 - 根角色)
├── DEPT_ADMIN (部门管理员)
│   └── SUB_DEPT_ADMIN (子部门管理员)
└── BUSINESS_ADMIN (业务系统管理员)
    ├── DEVELOPER (开发人员)
    ├── TESTER (测试人员)
    └── (其他业务特殊角色)
```

**角色分配原则**：

- 用户默认无任何角色，需要管理员显式分配
- `NORMAL_USER` 作为业务角色，可根据实际需要分配给普通员工
- 角色继承仅影响权限，不影响角色分配关系
- 用户可以同时拥有多个不同层级的角色

### 2.4 部门权限模型

#### 2.4.1 多级部门管理机制

- **部门层级结构**：支持无限级部门嵌套
- **父级部门权限**：父级部门管理员可以管理所有子级部门
- **权限范围控制**：
  - `data:scope:all` - 全部数据权限（超级管理员）
  - `data:scope:department` - 本部门数据权限
  - `data:scope:subordinate` - 下级部门数据权限（包含所有子级）
  - `data:scope:self` - 仅个人数据权限

#### 2.4.2 部门成员角色机制

- 用户通过 `DepartmentMember` 加入部门
- 部门管理员可为部门成员分配**自己拥有的角色**
- **父级部门管理员可以管理子级部门的成员角色**
- 部门成员角色仅在该部门内有效
- 用户离开部门时，自动失去该部门的所有角色

#### 2.4.3 权限合并规则

用户最终权限 = 全局角色权限 ∪ 所有部门成员角色权限

```typescript
// 伪代码示例
function getUserEffectivePermissions(userId: string): Set<string> {
  const globalPermissions = getUserGlobalRolePermissions(userId)
  const departmentPermissions = getUserDepartmentRolePermissions(userId)
  return new Set([...globalPermissions, ...departmentPermissions])
}

// 部门数据权限检查示例
function checkDepartmentDataAccess(userId: string, targetDeptId: string): boolean {
  const userDepartments = getUserDepartments(userId)

  for (const dept of userDepartments) {
    // 检查是否是目标部门的父级部门
    if (isParentDepartment(dept.id, targetDeptId)) {
      return true
    }
    // 检查是否是同一部门
    if (dept.id === targetDeptId) {
      return true
    }
  }

  return false
}
```

## 3. 数据模型分析

基于现有 `schema.prisma`，核心模型关系：

### 3.1 用户权限获取路径

**路径1：全局角色权限**

```
User → UserRole → Role → RolePermission → Permission
```

**路径2：部门成员角色权限**

```
User → DepartmentMember → DepartmentMemberRole → Role → RolePermission → Permission
```

### 3.2 关键字段说明

#### Role 模型增强建议

```prisma
model Role {
  // ... 现有字段

  // 建议增加字段
  dataScope     Int?    @default(0) @map("data_scope") @db.TinyInt /// 数据权限范围 0:全部 1:本部门 2:仅自己 3:下级部门
  isSystem      Int     @default(0) @map("is_system") @db.TinyInt /// 是否系统角色 0:否 1:是
  maxMembers    Int?    @map("max_members") /// 最大成员数限制

  // ... 其他字段
}
```

#### Permission 模型增强建议

```prisma
model Permission {
  // ... 现有字段

  // 建议增加字段
  category      String? /// 权限分类 system/business/data/menu
  resource      String? /// 资源标识
  action        String? /// 操作标识
  description   String? /// 权限描述

  // ... 其他字段
}
```

### 3.3 Service 层架构设计

#### 3.3.1 核心服务职责

```typescript
// 角色管理服务
interface RoleService {
  // 角色 CRUD
  createRole(data: CreateRoleRequest): Promise<Role>
  updateRole(id: string, data: UpdateRoleRequest): Promise<Role>
  deleteRole(id: string): Promise<void>
  getRoleById(id: string): Promise<Role | null>
  getRoles(query: RoleQueryParams): Promise<PaginatedResult<Role>>

  // 角色层级管理
  getRoleTree(): Promise<RoleTreeNode[]>
  getRoleChildren(parentId: string): Promise<Role[]>
  getRoleAncestors(roleId: string): Promise<Role[]>

  // 角色权限管理
  assignPermissions(roleId: string, permissionIds: string[]): Promise<void>
  removePermissions(roleId: string, permissionIds: string[]): Promise<void>
  getRolePermissions(roleId: string): Promise<Permission[]>
}

// 权限管理服务
interface PermissionService {
  // 权限 CRUD
  createPermission(data: CreatePermissionRequest): Promise<Permission>
  updatePermission(id: string, data: UpdatePermissionRequest): Promise<Permission>
  deletePermission(id: string): Promise<void>
  getPermissionById(id: string): Promise<Permission | null>
  getPermissions(query: PermissionQueryParams): Promise<PaginatedResult<Permission>>

  // 权限分类管理
  getPermissionsByCategory(category: string): Promise<Permission[]>
  getPermissionTree(): Promise<PermissionTreeNode[]>
}

// 用户角色关联服务
interface UserRoleService {
  // 全局角色管理
  assignGlobalRoles(userId: string, roleIds: string[]): Promise<void>
  removeGlobalRoles(userId: string, roleIds: string[]): Promise<void>
  getUserGlobalRoles(userId: string): Promise<Role[]>

  // 用户权限查询
  getUserEffectiveRoles(userId: string): Promise<UserEffectiveRoles>
  getUserEffectivePermissions(userId: string): Promise<Set<string>>
}

// 权限检查服务
interface AuthorizationService {
  // 权限检查
  checkPermission(userId: string, permission: string): Promise<boolean>
  checkPermissions(userId: string, permissions: string[], mode: 'ALL' | 'ANY'): Promise<boolean>
  checkRole(userId: string, role: string, scope?: 'GLOBAL' | 'DEPARTMENT'): Promise<boolean>

  // 数据权限检查
  checkDataPermission(userId: string, resource: string, action: string, context: any): Promise<boolean>
  getUserDataScope(userId: string): Promise<string>
}

// 部门管理服务
interface DepartmentService {
  // 部门 CRUD
  createDepartment(data: CreateDepartmentRequest): Promise<Department>
  updateDepartment(id: string, data: UpdateDepartmentRequest): Promise<Department>
  deleteDepartment(id: string): Promise<void>
  getDepartmentById(id: string): Promise<Department | null>
  getDepartments(query: DepartmentQueryParams): Promise<PaginatedResult<Department>>

  // 部门层级管理
  getDepartmentTree(): Promise<DepartmentTreeNode[]>
  getDepartmentChildren(parentId: string): Promise<Department[]>
  getDepartmentDescendants(parentId: string): Promise<Department[]>
  getManageableDepartments(userId: string): Promise<Department[]>
}

// 部门成员管理服务
interface DepartmentMemberService {
  // 成员管理
  addMember(deptId: string, userId: string, type: DepartmentMemberType): Promise<void>
  removeMember(deptId: string, userId: string): Promise<void>
  updateMemberType(deptId: string, userId: string, type: DepartmentMemberType): Promise<void>
  getDepartmentMembers(deptId: string): Promise<DepartmentMember[]>
  getUserDepartments(userId: string): Promise<Department[]>
}

// 部门角色管理服务
interface DepartmentRoleService {
  // 部门角色分配
  assignDepartmentRoles(deptId: string, userId: string, roleIds: string[]): Promise<void>
  removeDepartmentRoles(deptId: string, userId: string, roleIds: string[]): Promise<void>
  getUserDepartmentRoles(userId: string, deptId: string): Promise<Role[]>
  canAssignRoles(managerId: string, deptId: string, roleIds: string[]): Promise<boolean>
}

// 数据权限范围服务
interface DataScopeService {
  // 数据权限检查
  checkDepartmentDataAccess(userId: string, targetDeptId: string): Promise<boolean>
  checkSubordinateDataAccess(userId: string, targetDeptId: string): Promise<boolean>
  checkSelfDataAccess(userId: string, targetUserId: string): Promise<boolean>

  // 权限范围查询
  getUserAccessibleDepartments(userId: string): Promise<string[]>
  getUserAccessibleUsers(userId: string): Promise<string[]>
}

// 菜单权限服务
interface MenuPermissionService {
  // 菜单权限管理
  assignMenusToRole(roleId: string, menuIds: string[]): Promise<void>
  removeMenusFromRole(roleId: string, menuIds: string[]): Promise<void>
  getRoleMenus(roleId: string): Promise<Menu[]>

  // 用户菜单查询
  getUserMenus(userId: string): Promise<MenuTreeNode[]>
  checkMenuAccess(userId: string, menuPath: string): Promise<boolean>
}

// 缓存管理服务
interface CacheService {
  // 权限缓存
  getUserPermissionsCache(userId: string): Promise<Set<string> | null>
  setUserPermissionsCache(userId: string, permissions: string[], ttl?: number): Promise<void>
  invalidateUserCache(userId: string): Promise<void>

  // 角色缓存
  getRolePermissionsCache(roleId: string): Promise<Permission[] | null>
  setRolePermissionsCache(roleId: string, permissions: Permission[], ttl?: number): Promise<void>
  invalidateRoleCache(roleId: string): Promise<void>

  // 批量缓存操作
  invalidateUsersCaches(userIds: string[]): Promise<void>
  invalidateRolesCaches(roleIds: string[]): Promise<void>
}
```

#### 3.3.2 服务依赖关系

```typescript
// 服务依赖图
AuthorizationService
├── UserRoleService
├── DepartmentRoleService
├── DataScopeService
├── CacheService
└── PermissionService

UserRoleService
├── RoleService
└── CacheService

DepartmentRoleService
├── RoleService
├── DepartmentMemberService
└── AuthorizationService

DataScopeService
├── DepartmentService
├── DepartmentMemberService
└── UserRoleService

MenuPermissionService
├── RoleService
├── UserRoleService
└── DepartmentRoleService
```

#### 3.3.3 服务实现示例

```typescript
// src/services/authorization.service.ts
export class AuthorizationService {
  constructor(
    private userRoleService: UserRoleService,
    private departmentRoleService: DepartmentRoleService,
    private dataScopeService: DataScopeService,
    private cacheService: CacheService
  ) {}

  async checkPermission(userId: string, permission: string): Promise<boolean> {
    const userPermissions = await this.getUserEffectivePermissions(userId)
    return this.checkPermissionMatch(userPermissions, permission)
  }

  async getUserEffectivePermissions(userId: string): Promise<Set<string>> {
    // 1. 尝试从缓存获取
    const cached = await this.cacheService.getUserPermissionsCache(userId)
    if (cached) return cached

    // 2. 合并全局权限和部门权限
    const globalPermissions = await this.userRoleService.getUserGlobalPermissions(userId)
    const departmentPermissions = await this.departmentRoleService.getUserDepartmentPermissions(userId)

    const allPermissions = new Set([...globalPermissions, ...departmentPermissions])

    // 3. 缓存结果
    await this.cacheService.setUserPermissionsCache(userId, Array.from(allPermissions))

    return allPermissions
  }

  private checkPermissionMatch(userPermissions: Set<string>, requiredPermission: string): boolean {
    // 权限匹配逻辑（支持通配符）
    for (const permission of userPermissions) {
      if (permission === requiredPermission) return true
      if (permission === '*') return true
      if (permission.endsWith(':*') && requiredPermission.startsWith(permission.slice(0, -1))) {
        return true
      }
    }
    return false
  }
}
```

## 4. API 设计规范

### 4.1 路由结构

```
/api/system/
├── roles/                    # 角色管理
├── permissions/              # 权限管理
├── users/                    # 用户管理
│   └── {userId}/roles/       # 用户角色分配
├── departments/              # 部门管理
│   └── {deptId}/
│       ├── members/          # 部门成员管理
│       └── roles/            # 部门角色管理
├── menus/                    # 菜单管理
└── auth/
    └── me/                   # 当前用户信息
        ├── permissions/      # 我的权限
        ├── roles/           # 我的角色
        └── menus/           # 我的菜单
```

### 4.2 核心 API 端点

#### 4.2.1 角色管理 API

```typescript
// 角色 CRUD
POST / api / system / roles // 创建角色
GET / api / system / roles // 角色列表（支持分页、筛选）
GET / api / system / roles / { roleId } // 角色详情
PUT / api / system / roles / { roleId } // 更新角色
DELETE / api / system / roles / { roleId } // 删除角色

// 角色权限管理
GET / api / system / roles / { roleId } / permissions // 获取角色权限
PUT / api / system / roles / { roleId } / permissions // 设置角色权限
POST / api / system / roles / { roleId } / permissions // 添加角色权限
DELETE / api / system / roles / { roleId } / permissions // 移除角色权限

// 角色菜单管理
GET / api / system / roles / { roleId } / menus // 获取角色菜单
PUT / api / system / roles / { roleId } / menus // 设置角色菜单

// 角色层级管理
GET / api / system / roles / tree // 角色树结构
POST / api / system / roles / { roleId } / children // 添加子角色
```

#### 4.2.2 用户角色管理 API

```typescript
// 全局角色管理
GET / api / system / users / { userId } / roles // 获取用户全局角色
POST / api / system / users / { userId } / roles // 分配全局角色
DELETE / api / system / users / { userId } / roles // 移除全局角色

// 用户权限查询
GET / api / system / users / { userId } / permissions // 获取用户所有权限
GET / api / system / users / { userId } / effective - roles // 获取用户有效角色（全局+部门）
```

#### 4.2.3 部门权限管理 API

```typescript
// 部门成员管理
GET / api / system / departments / { deptId } / members // 部门成员列表
POST / api / system / departments / { deptId } / members // 添加部门成员
DELETE / api / system / departments / { deptId } / members / { userId } // 移除部门成员

// 部门成员角色管理
GET / api / system / departments / { deptId } / members / { userId } / roles // 获取成员部门角色
POST / api / system / departments / { deptId } / members / { userId } / roles // 分配部门角色
DELETE / api / system / departments / { deptId } / members / { userId } / roles // 移除部门角色

// 部门可分配角色查询
GET / api / system / departments / { deptId } / assignable - roles // 获取可分配的角色列表

// 多级部门管理
GET / api / system / departments / { deptId } / children // 获取子部门列表
GET / api / system / departments / { deptId } / descendants // 获取所有下级部门（递归）
GET / api / system / departments / { deptId } / manageable // 获取当前用户可管理的部门列表

// 部门权限范围查询
GET / api / system / departments / { deptId } / data - scope // 获取部门数据权限范围
POST / api / system / departments / { deptId } / validate - access // 验证对部门的访问权限
```

#### 4.2.4 当前用户权限 API

```typescript
GET / api / system / auth / me / profile // 当前用户信息
GET / api / system / auth / me / permissions // 当前用户所有权限
GET / api / system / auth / me / roles // 当前用户所有角色
GET / api / system / auth / me / menus // 当前用户可访问菜单
GET / api / system / auth / me / departments // 当前用户所属部门
```

### 4.3 请求/响应格式

#### 4.3.1 创建角色请求

```typescript
interface CreateRoleRequest {
  code: string // 角色编码
  name: string // 角色名称
  type?: number // 角色类型 0:系统 1:业务
  parentId?: string // 父角色ID
  dataScope?: number // 数据权限范围
  enabled?: number // 是否启用
  description?: string // 角色描述
  permissionIds?: string[] // 权限ID列表
  menuIds?: string[] // 菜单ID列表
}
```

#### 4.3.2 用户权限响应

```typescript
interface UserPermissionsResponse {
  userId: string
  globalRoles: Role[] // 全局角色
  departmentRoles: {
    // 部门角色
    departmentId: string
    departmentName: string
    roles: Role[]
  }[]
  permissions: Permission[] // 所有有效权限
  dataScope: string[] // 数据权限范围
}
```

## 5. 权限中间件设计

### 5.1 核心中间件

```typescript
// src/middlewares/rbac.ts

/**
 * 权限检查中间件
 */
export const requirePermissions = (permissions: string | string[], mode: 'ALL' | 'ANY' = 'ALL') => {
  return createMiddleware(async (c, next) => {
    const user = c.get('user')
    if (!user?.id) {
      throw new UnauthorizedError('Authentication required')
    }

    const requiredPerms = Array.isArray(permissions) ? permissions : [permissions]

    // 使用 AuthorizationService 进行权限检查
    const authService = c.get('authorizationService') as AuthorizationService
    const hasPermission = await authService.checkPermissions(user.id, requiredPerms, mode)

    if (!hasPermission) {
      throw new ForbiddenError(`Missing required permissions: ${requiredPerms.join(', ')}`)
    }

    await next()
  })
}

/**
 * 角色检查中间件
 */
export const requireRoles = (roles: string | string[], scope: 'GLOBAL' | 'DEPARTMENT' | 'ANY' = 'ANY') => {
  return createMiddleware(async (c, next) => {
    const user = c.get('user')
    if (!user?.id) {
      throw new UnauthorizedError('Authentication required')
    }

    const authService = c.get('authorizationService') as AuthorizationService
    const roleArray = Array.isArray(roles) ? roles : [roles]

    let hasRole = false
    for (const role of roleArray) {
      if (await authService.checkRole(user.id, role, scope)) {
        hasRole = true
        break
      }
    }

    if (!hasRole) {
      throw new ForbiddenError(`Missing required roles: ${roleArray.join(', ')}`)
    }

    await next()
  })
}

/**
 * 数据权限检查中间件
 */
export const requireDataScope = (resource: string, action: string = 'read') => {
  return createMiddleware(async (c, next) => {
    const user = c.get('user')
    if (!user?.id) {
      throw new UnauthorizedError('Authentication required')
    }

    const authService = c.get('authorizationService') as AuthorizationService
    const context = {
      departmentId: c.req.param('deptId'),
      userId: c.req.param('userId'),
      req: c.req,
    }

    const hasDataAccess = await authService.checkDataPermission(user.id, resource, action, context)
    if (!hasDataAccess) {
      throw new ForbiddenError(`Insufficient data access permissions for ${resource}`)
    }

    await next()
  })
}

/**
 * 菜单权限检查中间件（用于API路由保护）
 */
export const requireMenuAccess = (menuPath: string) => {
  return createMiddleware(async (c, next) => {
    const user = c.get('user')
    if (!user?.id) {
      throw new UnauthorizedError('Authentication required')
    }

    const menuService = c.get('menuPermissionService') as MenuPermissionService
    const hasMenuAccess = await menuService.checkMenuAccess(user.id, menuPath)
    if (!hasMenuAccess) {
      throw new ForbiddenError(`Access denied to menu: ${menuPath}`)
    }

    await next()
  })
}
```

### 5.2 权限查询优化

基于 Service 层架构，权限查询逻辑分布在各个专门的服务中：

```typescript
// src/services/authorization.service.ts - 权限检查核心逻辑

/**
 * 检查权限是否匹配（支持通配符和继承）
 */
private checkPermissionMatch(userPermission: string, requiredPermission: string): boolean {
  // 1. 完全匹配
  if (userPermission === requiredPermission) {
    return true;
  }

  // 2. 超级权限
  if (userPermission === '*') {
    return true;
  }

  // 3. 通配符匹配
  if (userPermission.endsWith(':*')) {
    const prefix = userPermission.slice(0, -1); // 移除 '*'
    return requiredPermission.startsWith(prefix);
  }

  return false;
}

/**
 * 检查用户是否拥有指定权限（支持继承）
 */
async checkPermission(userId: string, requiredPermission: string): Promise<boolean> {
  const userPermissions = await this.getUserEffectivePermissions(userId);

  // 检查是否有匹配的权限（包括继承）
  for (const permission of userPermissions) {
    if (this.checkPermissionMatch(permission, requiredPermission)) {
      return true;
    }
  }

  return false;
}
```

```typescript
// src/services/user-role.service.ts - 用户角色权限管理

/**
 * 获取用户全局角色权限
 */
async getUserGlobalPermissions(userId: string): Promise<string[]> {
  const globalRoles = await db.userRole.findMany({
    where: {
      userId,
      role: { enabled: 1 }
    },
    include: {
      role: {
        include: {
          rolePermissions: {
            include: { permission: true }
          }
        }
      }
    }
  });

  const permissions: string[] = [];
  globalRoles.forEach(userRole => {
    userRole.role?.rolePermissions.forEach(rp => {
      if (rp.permission) {
        permissions.push(rp.permission.code);
      }
    });
  });

  return permissions;
}
```

```typescript
// src/services/department-role.service.ts - 部门角色权限管理

/**
 * 获取用户部门角色权限
 */
async getUserDepartmentPermissions(userId: string): Promise<string[]> {
  const departmentRoles = await db.departmentMemberRole.findMany({
    where: {
      userId,
      role: { enabled: 1 }
    },
    include: {
      role: {
        include: {
          rolePermissions: {
            include: { permission: true }
          }
        }
      }
    }
  });

  const permissions: string[] = [];
  departmentRoles.forEach(deptRole => {
    deptRole.role?.rolePermissions.forEach(rp => {
      if (rp.permission) {
        permissions.push(rp.permission.code);
      }
    });
  });

  return permissions;
}
```

```typescript
// src/services/data-scope.service.ts - 数据权限范围检查

/**
 * 检查用户是否有访问目标部门数据的权限
 */
async checkDepartmentDataAccess(userId: string, targetDeptId: string): Promise<boolean> {
  if (!targetDeptId) return true; // 如果没有指定部门，允许访问

  // 获取用户所属的所有部门
  const userDepartments = await db.departmentMember.findMany({
    where: {
      userId,
    },
    include: {
      department: {
        select: { id: true, treePath: true }
      }
    }
  });

  // 获取目标部门信息
  const targetDept = await db.department.findUnique({
    where: { id: targetDeptId },
    select: { treePath: true }
  });

  if (!targetDept) return false;

  // 检查用户是否有权限访问目标部门
  for (const userDept of userDepartments) {
    const userDeptPath = userDept.department.treePath;
    const targetDeptPath = targetDept.treePath;

    // 检查是否是同一部门
    if (userDept.department.id === targetDeptId) {
      return true;
    }

    // 检查是否是父级部门（用户部门路径是目标部门路径的前缀）
    if (targetDeptPath.startsWith(userDeptPath + '.') || targetDeptPath === userDeptPath) {
      return true;
    }
  }

  return false;
}

/**
 * 检查下级部门数据访问权限
 */
async checkSubordinateDataAccess(userId: string, targetDeptId: string): Promise<boolean> {
  if (!targetDeptId) return true;

  // 获取用户管理的部门
  const userManagedDepts = await db.departmentMember.findMany({
    where: {
      userId,
      type: { in: ['OWNER', 'MANAGER'] } // 只有部门负责人和管理员可以管理下级
    },
    include: {
      department: {
        select: { id: true, treePath: true }
      }
    }
  });

  // 获取目标部门信息
  const targetDept = await db.department.findUnique({
    where: { id: targetDeptId },
    select: { treePath: true }
  });

  if (!targetDept) return false;

  // 检查是否可以管理目标部门
  for (const managedDept of userManagedDepts) {
    const managedDeptPath = managedDept.department.treePath;
    const targetDeptPath = targetDept.treePath;

    // 检查目标部门是否是用户管理部门的下级
    if (targetDeptPath.startsWith(managedDeptPath + '.') || targetDeptPath === managedDeptPath) {
      return true;
    }
  }

  return false;
}

/**
 * 检查个人数据访问权限
 */
async checkSelfDataAccess(userId: string, targetUserId: string): Promise<boolean> {
  // 如果没有指定用户ID，或者就是当前用户，允许访问
  return !targetUserId || targetUserId === userId;
}
```

```typescript
// src/services/cache.service.ts - 权限缓存管理

/**
 * 权限缓存服务
 */
export class CacheService {
  private redis: Redis // 假设使用 Redis 作为缓存

  async getUserPermissionsCache(userId: string): Promise<Set<string> | null> {
    const cached = await this.redis.get(`user:permissions:${userId}`)
    return cached ? new Set(JSON.parse(cached)) : null
  }

  async setUserPermissionsCache(userId: string, permissions: string[], ttl: number = 300): Promise<void> {
    await this.redis.setex(`user:permissions:${userId}`, ttl, JSON.stringify(permissions))
  }

  async invalidateUserCache(userId: string): Promise<void> {
    await this.redis.del(`user:permissions:${userId}`)
  }

  async invalidateUsersCaches(userIds: string[]): Promise<void> {
    const keys = userIds.map((id) => `user:permissions:${id}`)
    if (keys.length > 0) {
      await this.redis.del(...keys)
    }
  }
}
```

## 6. 菜单权限控制

### 6.1 菜单权限 API

```typescript
// GET /api/system/auth/me/menus
export async function getUserMenus(userId: string) {
  // 1. 获取用户所有角色
  const userRoles = await getUserAllRoles(userId)
  const roleIds = userRoles.map((role) => role.id)

  // 2. 查询角色关联的菜单
  const menus = await db.menu.findMany({
    where: {
      enabled: 1,
      hiddenInMenu: 0,
      roleMenus: {
        some: {
          roleId: { in: roleIds },
        },
      },
    },
    include: {
      microApp: true,
      children: {
        where: { enabled: 1 },
        orderBy: { sort: 'asc' },
      },
    },
    orderBy: { sort: 'asc' },
  })

  // 3. 构建菜单树
  return buildMenuTree(menus)
}

/**
 * 检查用户菜单访问权限
 */
export async function checkUserMenuAccess(userId: string, menuPath: string): Promise<boolean> {
  const userMenus = await getUserMenus(userId)

  // 递归检查菜单路径
  function hasMenuPath(menus: Menu[], path: string): boolean {
    return menus.some((menu) => {
      if (menu.path === path) return true
      if (menu.children && menu.children.length > 0) {
        return hasMenuPath(menu.children, path)
      }
      return false
    })
  }

  return hasMenuPath(userMenus, menuPath)
}
```

### 6.2 前端菜单渲染

```typescript
// 前端使用示例
const { data: menus } = await fetch('/api/system/auth/me/menus')

// 根据菜单权限动态渲染导航
function renderNavigation(menus: Menu[]) {
  return menus.map((menu) => ({
    key: menu.id,
    label: menu.name,
    icon: menu.icon,
    path: menu.path,
    children: menu.children?.map((child) => renderNavigation([child])),
  }))
}
```

### 6.3 实际使用示例

```typescript
// src/routes/user/index.ts
import { Hono } from 'hono'
import { requirePermissions, requireRoles, requireDataScope, requireMenuAccess } from '../middlewares/rbac'

const app = new Hono()

// 示例1：基础权限检查
app.get(
  '/',
  requirePermissions('system:user:list'), // 需要用户列表权限
  async (c) => {
    // 获取用户列表的业务逻辑
    const users = await userService.getUsers()
    return c.json({ data: users })
  }
)

// 示例2：创建用户（需要多个权限）
app.post(
  '/',
  requirePermissions(['system:user:create', 'data:scope:department'], 'ALL'), // 需要创建权限和部门数据权限
  async (c) => {
    const userData = await c.req.json()
    const user = await userService.createUser(userData)
    return c.json({ data: user })
  }
)

// 示例3：角色检查
app.post(
  '/assign-admin-role',
  requireRoles('SUPER_ADMIN', 'GLOBAL'), // 只有全局超级管理员可以操作
  async (c) => {
    // 分配管理员角色的业务逻辑
  }
)

// 示例4：数据权限检查
app.get(
  '/:userId',
  requireDataScope('system:user', 'read'), // 检查用户数据读取权限
  async (c) => {
    const userId = c.req.param('userId')
    const user = await userService.getUserById(userId)
    return c.json({ data: user })
  }
)

// 示例5：菜单权限检查（保护特定页面的API）
app.get(
  '/admin-dashboard',
  requireMenuAccess('/admin/dashboard'), // 检查是否有访问管理后台菜单的权限
  async (c) => {
    const dashboardData = await adminService.getDashboardData()
    return c.json({ data: dashboardData })
  }
)

// 示例6：权限继承示例
app.delete(
  '/:userId',
  requirePermissions('system:user:delete'), // 如果用户有 system:user:* 或 system:* 权限也会通过
  async (c) => {
    const userId = c.req.param('userId')
    await userService.deleteUser(userId)
    return c.json({ message: 'User deleted successfully' })
  }
)

// 示例7：组合使用多个中间件
app.put(
  '/:userId/role',
  requirePermissions('system:user:update'), // 基础权限检查
  requireDataScope('system:user', 'update'), // 数据权限检查
  requireRoles(['SYSTEM_ADMIN', 'DEPT_ADMIN'], 'ANY'), // 角色检查（任一即可）
  async (c) => {
    const userId = c.req.param('userId')
    const { roleIds } = await c.req.json()
    await userService.updateUserRoles(userId, roleIds)
    return c.json({ message: 'User roles updated successfully' })
  }
)

export default app
```

```typescript
// src/routes/department/index.ts
import { Hono } from 'hono'
import { requirePermissions, requireDataScope } from '../middlewares/rbac'

const app = new Hono()

// 部门成员管理示例
app.get(
  '/:deptId/members',
  requirePermissions('system:department:list'),
  requireDataScope('department', 'read'), // 检查部门数据访问权限
  async (c) => {
    const deptId = c.req.param('deptId')
    const members = await departmentService.getDepartmentMembers(deptId)
    return c.json({ data: members })
  }
)

// 分配部门角色（部门管理员只能分配自己拥有的角色）
app.post(
  '/:deptId/members/:userId/roles',
  requirePermissions('system:department:update'),
  requireDataScope('department', 'update'),
  async (c) => {
    const { deptId, userId } = c.req.params()
    const { roleIds } = await c.req.json()
    const currentUser = c.get('user')

    // 检查当前用户是否可以分配这些角色
    const canAssignRoles = await departmentService.canAssignRoles(currentUser.id, deptId, roleIds)

    if (!canAssignRoles) {
      throw new ForbiddenError('Cannot assign roles that you do not possess')
    }

    await departmentService.assignMemberRoles(deptId, userId, roleIds)
    return c.json({ message: 'Roles assigned successfully' })
  }
)

// 获取子部门列表（支持多级部门管理）
app.get(
  '/:deptId/children',
  requirePermissions('system:department:list'),
  requireDataScope('department', 'read'),
  async (c) => {
    const deptId = c.req.param('deptId')
    const currentUser = c.get('user')

    // 检查用户是否有权限查看该部门的子部门
    const hasAccess = await checkDepartmentDataAccess(currentUser.id, { departmentId: deptId })
    if (!hasAccess) {
      throw new ForbiddenError('No permission to access this department')
    }

    const children = await departmentService.getChildDepartments(deptId)
    return c.json({ data: children })
  }
)

// 获取用户可管理的所有部门（包括下级部门）
app.get('/manageable', requirePermissions('system:department:list'), async (c) => {
  const currentUser = c.get('user')
  const manageableDepts = await departmentService.getManageableDepartments(currentUser.id)
  return c.json({ data: manageableDepts })
})

// 跨部门数据访问示例（父级部门管理员访问子部门数据）
app.get('/:deptId/projects', requirePermissions('business:project:list'), requireDataScope('department', 'read'), async (c) => {
  const deptId = c.req.param('deptId')
  const currentUser = c.get('user')

  // 这里会自动检查用户是否有权限访问该部门的项目数据
  // 如果是父级部门管理员，可以访问子部门的项目
  const projects = await projectService.getDepartmentProjects(deptId)
  return c.json({ data: projects })
})

// 部门角色继承示例
app.get('/:deptId/effective-permissions', requirePermissions('system:department:list'), async (c) => {
  const deptId = c.req.param('deptId')
  const { userId } = c.req.query()

  if (!userId) {
    throw new BadRequestError('userId is required')
  }

  // 获取用户在该部门的有效权限（包括继承的权限）
  const effectivePermissions = await rbacService.getUserDepartmentPermissions(userId, deptId)
  return c.json({ data: effectivePermissions })
})

export default app
```

## 7. 数据库种子数据

### 7.1 初始权限定义

```typescript
// prisma/seeds/permissions.ts
export const INITIAL_PERMISSIONS = [
  // 系统管理权限
  { code: 'system:user:list', name: '查看用户列表', category: 'system' },
  { code: 'system:user:create', name: '创建用户', category: 'system' },
  { code: 'system:user:update', name: '更新用户', category: 'system' },
  { code: 'system:user:delete', name: '删除用户', category: 'system' },
  { code: 'system:user:*', name: '用户管理（全部）', category: 'system' },

  { code: 'system:role:list', name: '查看角色列表', category: 'system' },
  { code: 'system:role:create', name: '创建角色', category: 'system' },
  { code: 'system:role:update', name: '更新角色', category: 'system' },
  { code: 'system:role:delete', name: '删除角色', category: 'system' },
  { code: 'system:role:*', name: '角色管理（全部）', category: 'system' },

  { code: 'system:permission:list', name: '查看权限列表', category: 'system' },
  { code: 'system:permission:create', name: '创建权限', category: 'system' },
  { code: 'system:permission:update', name: '更新权限', category: 'system' },
  { code: 'system:permission:delete', name: '删除权限', category: 'system' },
  { code: 'system:permission:*', name: '权限管理（全部）', category: 'system' },

  { code: 'system:department:list', name: '查看部门列表', category: 'system' },
  { code: 'system:department:create', name: '创建部门', category: 'system' },
  { code: 'system:department:update', name: '更新部门', category: 'system' },
  { code: 'system:department:delete', name: '删除部门', category: 'system' },
  { code: 'system:department:*', name: '部门管理（全部）', category: 'system' },

  { code: 'system:menu:list', name: '查看菜单列表', category: 'system' },
  { code: 'system:menu:create', name: '创建菜单', category: 'system' },
  { code: 'system:menu:update', name: '更新菜单', category: 'system' },
  { code: 'system:menu:delete', name: '删除菜单', category: 'system' },
  { code: 'system:menu:*', name: '菜单管理（全部）', category: 'system' },

  { code: 'system:*', name: '系统管理（全部）', category: 'system' },

  // 数据权限
  { code: 'data:scope:all', name: '全部数据权限', category: 'data' },
  { code: 'data:scope:department', name: '本部门数据权限', category: 'data' },
  { code: 'data:scope:self', name: '个人数据权限', category: 'data' },
  { code: 'data:scope:subordinate', name: '下级部门数据权限', category: 'data' },

  // 业务权限（示例）
  { code: 'business:project:list', name: '查看项目列表', category: 'business' },
  { code: 'business:project:create', name: '创建项目', category: 'business' },
  { code: 'business:project:update', name: '更新项目', category: 'business' },
  { code: 'business:project:delete', name: '删除项目', category: 'business' },
  { code: 'business:project:*', name: '项目管理（全部）', category: 'business' },

  { code: 'business:task:list', name: '查看任务列表', category: 'business' },
  { code: 'business:task:create', name: '创建任务', category: 'business' },
  { code: 'business:task:update', name: '更新任务', category: 'business' },
  { code: 'business:task:assign', name: '分配任务', category: 'business' },
  { code: 'business:task:*', name: '任务管理（全部）', category: 'business' },

  { code: 'business:*', name: '业务管理（全部）', category: 'business' },

  // 超级权限
  { code: '*', name: '超级权限（全部）', category: 'system' },
]
```

### 7.2 初始角色定义

```typescript
// prisma/seeds/roles.ts
export const INITIAL_ROLES = [
  {
    code: 'SUPER_ADMIN',
    name: '超级管理员',
    type: 0, // 系统角色
    dataScope: 0, // 全部数据
    parentId: null, // 根角色
    permissions: ['*'], // 所有权限
  },
  {
    code: 'DEPT_ADMIN',
    name: '部门管理员',
    type: 0,
    dataScope: 3, // 下级部门数据权限
    parentCode: 'SUPER_ADMIN',
    permissions: [
      'system:user:list',
      'system:user:update',
      'system:department:list',
      'system:department:update',
      'business:*', // 业务管理全部权限
      'data:scope:subordinate',
    ],
  },
  {
    code: 'SUB_DEPT_ADMIN',
    name: '子部门管理员',
    type: 0,
    dataScope: 1, // 本部门数据权限
    parentCode: 'DEPT_ADMIN',
    permissions: [
      'system:user:list',
      'system:user:update',
      'system:department:list',
      'business:project:*',
      'business:task:*',
      'data:scope:department',
    ],
  },
  {
    code: 'BUSINESS_ADMIN',
    name: '业务系统管理员',
    type: 1, // 业务角色
    dataScope: 0, // 全部数据
    parentCode: 'SUPER_ADMIN',
    permissions: [
      'business:*', // 业务管理全部权限
      'system:user:list',
      'data:scope:all',
    ],
  },
  {
    code: 'DEVELOPER',
    name: '开发人员',
    type: 1,
    dataScope: 2, // 仅个人数据
    parentCode: 'BUSINESS_ADMIN',
    permissions: [
      'business:project:list',
      'business:project:update',
      'business:task:list',
      'business:task:update',
      'data:scope:self',
    ],
  },
  {
    code: 'TESTER',
    name: '测试人员',
    type: 1,
    dataScope: 2, // 仅个人数据
    parentCode: 'BUSINESS_ADMIN',
    permissions: ['business:project:list', 'business:task:list', 'business:task:update', 'data:scope:self'],
  },
  {
    code: 'NORMAL_USER',
    name: '普通用户',
    type: 1, // 业务角色
    dataScope: 2, // 仅个人数据
    parentId: null, // 独立角色，不继承其他角色
    permissions: ['business:project:list', 'business:task:list', 'data:scope:self'],
  },
]
```

## 8. 实施步骤

### 8.1 第一阶段：基础 RBAC

1. **Service 层开发**

   - `RoleService` - 角色管理（CRUD、角色层级、权限分配）
   - `PermissionService` - 权限管理（CRUD、权限分类）
   - `UserRoleService` - 用户角色关联管理
   - `AuthorizationService` - 权限检查核心逻辑
   - `CacheService` - 权限缓存管理

2. **API 开发**

   - 角色管理 API
   - 权限管理 API
   - 用户角色分配 API

3. **中间件开发**
   - `requirePermissions` 中间件
   - `requireRoles` 中间件

### 8.2 第二阶段：部门权限

1. **部门权限 Service**

   - `DepartmentService` - 部门管理（CRUD、层级关系）
   - `DepartmentMemberService` - 部门成员管理
   - `DepartmentRoleService` - 部门角色分配管理
   - `DataScopeService` - 数据权限范围检查

2. **部门权限 API**

   - 部门成员管理 API
   - 部门角色分配 API
   - 多级部门管理 API

3. **权限合并逻辑**
   - 全局权限 + 部门权限合并
   - 权限缓存优化

### 8.3 第三阶段：高级特性

1. **菜单权限**

   - `MenuService` - 菜单管理（与角色关联）
   - `MenuPermissionService` - 菜单权限检查
   - 用户菜单 API
   - 前端菜单权限集成

2. **数据权限**

   - `requireDataScope` 中间件
   - 数据权限检查逻辑优化

3. **性能优化**
   - 权限缓存机制完善
   - 批量权限检查
   - 权限预加载

### 8.4 第四阶段：监控与审计

1. **权限审计**

   - `AuditService` - 权限变更日志
   - `OperationLogService` - 用户操作审计

2. **监控告警**
   - `PermissionMonitorService` - 权限异常监控
   - 性能监控

## 9. 安全考虑

### 9.1 权限提升防护

- 用户不能分配超过自己权限的角色
- 部门管理员只能分配自己拥有的角色
- 系统角色不允许普通用户修改

### 9.2 数据安全

- 严格的数据权限检查
- SQL 注入防护
- 敏感操作二次验证

### 9.3 会话安全

- 权限变更后强制重新登录
- 权限缓存及时失效
- 异常登录检测

## 10. 性能优化

### 10.1 缓存策略

- 用户权限缓存（5分钟）
- 角色权限缓存（30分钟）
- 菜单权限缓存（1小时）

### 10.2 查询优化

- 权限查询索引优化
- 批量权限检查
- 懒加载权限数据

### 10.3 监控指标

- 权限检查响应时间
- 缓存命中率
- 数据库查询性能

---

## 总结

本 RBAC 企业级权限控制系统设计方案具备以下核心特性：

### 🏢 多级部门管理

- **无限级部门嵌套**：支持复杂的组织架构
- **父级部门权限**：父级部门管理员可管理所有子级部门
- **权限范围控制**：精确控制数据访问范围（全部/下级部门/本部门/个人）
- **部门角色隔离**：部门成员角色仅在该部门内有效

### 🔐 灵活的权限模型

- **权限继承**：通配符权限支持（`*`、`system:*`、`system:user:*`）
- **双重权限控制**：菜单权限 + 数据权限
- **角色层级**：子角色自动继承父角色权限
- **权限合并**：全局角色权限 ∪ 部门成员角色权限

### 🛡️ 企业级安全

- **最小权限原则**：用户默认无权限，需显式授权
- **权限提升防护**：用户不能分配超过自己权限的角色
- **数据隔离**：严格的部门数据权限检查
- **审计追踪**：所有权限变更可追溯

### ⚡ 高性能设计

- **权限缓存**：多层级缓存策略
- **批量检查**：支持批量权限验证
- **懒加载**：按需加载权限数据
- **索引优化**：数据库查询性能优化

### 🏗️ 优秀的架构设计

- **Service 层分离**：职责明确的服务架构
  - `AuthorizationService` - 权限检查核心逻辑
  - `RoleService` - 角色管理
  - `PermissionService` - 权限管理
  - `UserRoleService` - 用户角色关联
  - `DepartmentService` - 部门管理
  - `DepartmentRoleService` - 部门角色管理
  - `DataScopeService` - 数据权限范围检查
  - `MenuPermissionService` - 菜单权限管理
  - `CacheService` - 权限缓存管理
- **单一职责原则**：每个服务专注于特定功能领域
- **依赖注入**：服务间通过依赖注入实现解耦
- **易于测试**：清晰的服务边界便于单元测试

### 🔧 开发友好

- **中间件支持**：多种权限检查中间件
- **类型安全**：完整的 TypeScript 类型定义
- **易于扩展**：模块化设计，便于功能扩展
- **丰富示例**：详细的使用示例和最佳实践

### 📊 适用场景

- **大型企业**：复杂组织架构的权限管理
- **多租户系统**：部门级数据隔离
- **微服务架构**：分布式权限控制
- **SaaS 平台**：灵活的权限配置

### 🎯 架构优势

- **可维护性**：清晰的服务分层，便于维护和调试
- **可扩展性**：新增权限类型或检查逻辑只需扩展对应服务
- **可测试性**：每个服务可独立测试，提高代码质量
- **可重用性**：服务可在不同模块间复用
- **解耦性**：服务间通过接口交互，降低耦合度

**注意**：本设计方案基于现有的 Prisma Schema，充分利用了已有的模型结构。在实施过程中，可根据具体业务需求进行适当调整和扩展。建议按照文档中的四个阶段逐步实施，确保系统稳定性和可维护性。Service 层的合理拆分是本方案的核心优势，确保了代码的可维护性和可扩展性。
