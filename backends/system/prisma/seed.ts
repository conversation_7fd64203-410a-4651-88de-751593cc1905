import { PrismaClient } from '@prisma/client'
import { seedMenu } from './seeds/menu'
import { seedRBAC } from './seeds/rbac'

import roleSeed from './seeds/role'
import userSeed from './seeds/user'

const prisma = new PrismaClient()

async function main() {
  try {
    console.log('开始执行数据库种子脚本...')

    // 执行 RBAC 初始化
    await seedRBAC()

    // 执行菜单初始化
    await seedMenu()

    await roleSeed(prisma)
    await userSeed(prisma)

    // 创建基础权限
    const permissions = [
      // 用户管理权限
      { code: 'system:user:list', name: '查看用户列表' },
      { code: 'system:user:create', name: '创建用户' },
      { code: 'system:user:read', name: '查看用户详情' },
      { code: 'system:user:update', name: '更新用户' },
      { code: 'system:user:delete', name: '删除用户' },

      // 角色管理权限
      { code: 'system:role:list', name: '查看角色列表' },
      { code: 'system:role:create', name: '创建角色' },
      { code: 'system:role:read', name: '查看角色详情' },
      { code: 'system:role:update', name: '更新角色' },
      { code: 'system:role:delete', name: '删除角色' },

      // 权限管理权限
      { code: 'system:permission:list', name: '查看权限列表' },
      { code: 'system:permission:create', name: '创建权限' },
      { code: 'system:permission:read', name: '查看权限详情' },
      { code: 'system:permission:update', name: '更新权限' },
      { code: 'system:permission:delete', name: '删除权限' },

      // 菜单管理权限
      { code: 'system:menu:list', name: '查看菜单列表' },
      { code: 'system:menu:create', name: '创建菜单' },
      { code: 'system:menu:read', name: '查看菜单详情' },
      { code: 'system:menu:update', name: '更新菜单' },
      { code: 'system:menu:delete', name: '删除菜单' },
    ]

    // 批量创建权限
    const createdPermissions = await Promise.all(
      permissions.map(async (permission) => {
        return prisma.permission.upsert({
          where: {
            code_deletedAt: {
              code: permission.code,
              deletedAt: -1,
            },
          },
          update: permission,
          create: {
            ...permission,
            createdBy: 'system',
            creatorName: 'System',
            updatedBy: 'system',
            updaterName: 'System',
          },
        })
      })
    )

    // 创建超级管理员角色
    const superAdminRole = await prisma.role.upsert({
      where: {
        code_deletedAt: {
          code: 'SUPER_ADMIN',
          deletedAt: -1,
        },
      },
      update: {
        name: '超级管理员',
        type: 0,
        enabled: 1,
        treePath: '/',
      },
      create: {
        code: 'SUPER_ADMIN',
        name: '超级管理员',
        type: 0,
        enabled: 1,
        treePath: '/',
        createdBy: 'system',
        creatorName: 'System',
        updatedBy: 'system',
        updaterName: 'System',
      },
    })

    // 为超级管理员角色分配所有权限
    await prisma.rolePermission.createMany({
      data: createdPermissions.map((permission) => ({
        roleId: superAdminRole.id,
        permissionId: permission.id,
        createdBy: 'system',
        creatorName: 'System',
        updatedBy: 'system',
        updaterName: 'System',
      })),
      skipDuplicates: true,
    })

    console.log('数据库种子脚本执行完成！')
  } catch (error) {
    console.error('数据库种子脚本执行失败：', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

main().catch((e) => {
  console.error(e)
  process.exit(1)
})
