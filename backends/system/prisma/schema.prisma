generator zod {
  provider                  = "zod-prisma-types"
  output                    = "../src/gen/zod"
  createRelationValuesTypes = true
  writeBarrelFiles          = true
  useMultipleFiles          = true
  addInputTypeValidation    = true
  validateWhereUniqueInput  = true
}

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// ====================================================
// ================== Authentication ==================
// ====================================================

model User {
  id String @id @default(nanoid()) @db.VarChar(21)

  // 核心业务字段
  name          String /// 姓名
  email         String /// 邮箱
  emailVerified Boolean @map("email_verified") /// 邮箱是否验证
  image         String? @db.Text /// 头像
  telephone     String? @db.VarChar(20) /// 手机号
  lang          String? @default("zh-CN") /// 语言
  // CVTE OAuth2 带来用户信息
  account       String? /// 域账号
  gender        Int?    @db.TinyInt /// 性别，1: 男；以 it 为准

  // 关联字段
  departmentId String? @map("department_id")

  // 状态标记
  enabled Int @default(1) @db.TinyInt /// 是否启用  0: 否； 1：是

  // 审计字段
  createdBy   String?  @default("system") @map("created_by") /// 创建者账号，当前表默认为系统自动创建
  creatorName String?  @default("system") @map("creator_name") /// 创建者姓名，当前表默认为系统自动创建
  createdAt   DateTime @default(now()) @map(name: "created_at") /// 创建时间
  updatedBy   String?  @default("system") @map("updated_by") /// 更新者账号，当前表默认为系统自动创建
  updaterName String?  @default("system") @map("updater_name") /// 更新者姓名，当前表默认为系统自动创建
  updatedAt   DateTime @updatedAt @map(name: "updated_at") /// 更新时间
  deletedAt   Int?     @default(-1) @map("deleted_at") /// 删除标记, -1 表示未删除，数字表示时间戳

  sessions    Session[]
  accounts    Account[]
  department  Department?        @relation(fields: [departmentId], references: [id], map: "sys_user_department_fkey")
  userRoles   UserRole[]
  memberships DepartmentMember[]

  @@unique([email, deletedAt])
  @@map("sys_user")
}

model Session {
  id String @id @default(nanoid()) @db.VarChar(21)

  // 核心业务字段
  userId    String
  token     String
  expiresAt DateTime @map("expires_at")
  ipAddress String?  @map("ip_address") @db.Text
  userAgent String?  @map("user_agent") @db.Text

  // 审计字段
  createdAt DateTime @default(now()) @map(name: "created_at")
  updatedAt DateTime @updatedAt @map(name: "updated_at")

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([token])
  @@map("sys_session")
}

model Account {
  id String @id @default(nanoid()) @db.VarChar(21)

  // 核心业务字段
  userId                String    @map("user_id")
  accountId             String    @map("account_id") @db.Text
  providerId            String    @map("provider_id") @db.Text
  accessToken           String?   @map("access_token") @db.Text
  refreshToken          String?   @map("refresh_token") @db.Text
  idToken               String?   @map("id_token") @db.Text
  accessTokenExpiresAt  DateTime? @map("access_token_expires_at")
  refreshTokenExpiresAt DateTime? @map("refresh_token_expires_at")
  scope                 String?   @db.Text
  password              String?   @db.Text
  profile               Json?     @db.Json

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  // 审计字段
  createdAt DateTime @default(now()) @map(name: "created_at")
  updatedAt DateTime @updatedAt @map(name: "updated_at")

  @@map("sys_account")
}

model Verification {
  id String @id @default(nanoid()) @db.VarChar(22)

  // 核心业务字段
  identifier String   @db.Text
  value      String   @db.Text
  expiresAt  DateTime @map("expires_at")

  // 审计字段
  createdAt DateTime @default(now()) @map(name: "created_at")
  updatedAt DateTime @updatedAt @map(name: "updated_at")

  @@map("sys_verification")
}

// ===================================================
// ================== Authorization ==================
// ===================================================

model UserRole {
  // 基础标识
  id String @id @default(nanoid()) @db.VarChar(21)

  // 核心业务字段
  userId String @map("user_id")
  roleId String @map("role_id")

  // 关联字段
  user User @relation(fields: [userId], references: [id])
  role Role @relation(fields: [roleId], references: [id])

  // 审计字段
  createdBy   String   @map("created_by") /// 创建者账号
  creatorName String   @map("creator_name") /// 创建者姓名
  createdAt   DateTime @default(now()) @map(name: "created_at") /// 创建时间
  updatedBy   String   @map("updated_by") /// 更新者账号
  updaterName String   @map("updater_name") /// 更新者姓名
  updatedAt   DateTime @updatedAt @map(name: "updated_at") /// 更新时间
  deletedAt   Int?     @default(-1) @map("deleted_at") /// 删除标记, -1 表示未删除，数字表示时间戳

  @@map("sys_user_role")
}

model Role {
  // 基础标识
  id String @id @default(nanoid()) @db.VarChar(21)

  // 核心业务字段
  code        String /// 角色编码
  name        String /// 角色名称
  type        Int     @default(0) @db.TinyInt /// 0: 系统角色 1: 业务角色
  dataScope   Int?    @default(0) @map("data_scope") @db.TinyInt /// 数据权限范围 0:全部 1:本部门 2:仅自己 3:下级部门
  isSystem    Int     @default(0) @map("is_system") @db.TinyInt /// 是否系统角色 0:否 1:是
  maxMembers  Int?    @map("max_members") /// 最大成员数限制
  description String? /// 角色描述

  // 关联字段
  parentId              String?                @map("parent_id") /// 父级ID
  treePath              String                 @map("tree_path") @db.VarChar(255) /// 树状路径
  parent                Role?                  @relation("RoleHierarchy", fields: [parentId], references: [id]) /// 父级
  children              Role[]                 @relation("RoleHierarchy") /// 子级
  userRoles             UserRole[] /// 用户角色关联
  rolePermissions       RolePermission[] /// 角色权限关联，控制数据权限
  roleMenus             RoleMenu[] /// 角色菜单关联，控制菜单权限
  departmentMemberRoles DepartmentMemberRole[] /// 部门成员角色关联

  // 状态标记
  enabled Int @default(1) @db.TinyInt /// 是否启用  0: 否； 1：是

  // 审计字段
  createdBy   String   @map("created_by") /// 创建者账号
  creatorName String   @map("creator_name") /// 创建者姓名
  createdAt   DateTime @default(now()) @map(name: "created_at") /// 创建时间
  updatedBy   String   @map("updated_by") /// 更新者账号
  updaterName String   @map("updater_name") /// 更新者姓名
  updatedAt   DateTime @updatedAt @map(name: "updated_at") /// 更新时间
  deletedAt   Int?     @default(-1) @map("deleted_at") /// 删除标记, -1 表示未删除，数字表示时间戳

  @@unique([code, deletedAt])
  @@unique([treePath, deletedAt])
  @@index([parentId, treePath, deletedAt])
  @@map("sys_role")
}

model RolePermission {
  id String @id @default(nanoid()) @db.VarChar(21)

  roleId       String @map("role_id")
  permissionId String @map("permission_id")

  role       Role       @relation(fields: [roleId], references: [id])
  permission Permission @relation(fields: [permissionId], references: [id])

  // 审计字段
  createdBy   String   @map("created_by") /// 创建者账号
  creatorName String   @map("creator_name") /// 创建者姓名
  createdAt   DateTime @default(now()) @map(name: "created_at") /// 创建时间
  updatedBy   String   @map("updated_by") /// 更新者账号
  updaterName String   @map("updater_name") /// 更新者姓名
  updatedAt   DateTime @updatedAt @map(name: "updated_at") /// 更新时间
  deletedAt   Int?     @default(-1) @map("deleted_at") /// 删除标记, -1 表示未删除，数字表示时间戳

  @@map("sys_role_permission")
}

model Permission {
  // 基础标识
  id String @id @default(nanoid()) @db.VarChar(21)

  // 核心业务字段
  code        String /// 权限编码
  name        String /// 权限名称
  category    String? /// 权限分类 system/business/data/menu
  resource    String? /// 资源标识
  action      String? /// 操作标识
  description String? /// 权限描述

  // 关联字段
  rolePermissions RolePermission[] /// 角色权限关联

  // 审计字段
  createdBy   String   @map("created_by") /// 创建者账号
  creatorName String   @map("creator_name") /// 创建者姓名
  createdAt   DateTime @default(now()) @map(name: "created_at") /// 创建时间
  updatedBy   String   @map("updated_by") /// 更新者账号
  updaterName String   @map("updater_name") /// 更新者姓名
  updatedAt   DateTime @updatedAt @map(name: "updated_at") /// 更新时间
  deletedAt   Int?     @default(-1) @map("deleted_at") /// 删除标记, -1 表示未删除，数字表示时间戳

  @@unique([code, deletedAt])
  @@map("sys_permission")
}

model RoleMenu {
  id String @id @default(nanoid()) @db.VarChar(21)

  roleId String @map("role_id")
  menuId String @map("menu_id")

  role Role @relation(fields: [roleId], references: [id])
  menu Menu @relation(fields: [menuId], references: [id])

  // 审计字段
  createdBy   String   @map("created_by") /// 创建者账号
  creatorName String   @map("creator_name") /// 创建者姓名
  createdAt   DateTime @default(now()) @map(name: "created_at") /// 创建时间
  updatedBy   String   @map("updated_by") /// 更新者账号
  updaterName String   @map("updater_name") /// 更新者姓名
  updatedAt   DateTime @updatedAt @map(name: "updated_at") /// 更新时间
  deletedAt   Int?     @default(-1) @map("deleted_at") /// 删除标记, -1 表示未删除，数字表示时间戳

  @@map("sys_role_menu")
}

model Menu {
  // 基础标识
  id String @id @default(nanoid()) @db.VarChar(21)

  // 核心业务字段
  name         String /// 菜单名称
  icon         String? /// 菜单图标
  type         Int     @default(0) @db.TinyInt /// 菜单类型 0: 菜单 1: 按钮
  path         String /// 访问路径（主应用路径）
  target       Int     @default(0) @db.TinyInt /// 打开方式 0: 内部打开 1: 外部打开
  query        String? /// 查询参数 a=1&b=2&..., 使用 {{token}} 动态替换变量
  sort         Int?    @default(0) /// 排序
  hiddenInMenu Int     @default(0) @map("hidden_in_menu") @db.TinyInt /// 在菜单中隐藏 0: 否 1: 是
  enabled      Int     @default(1) @db.TinyInt /// 是否启用  0: 否； 1：是
  treePath     String  @map("tree_path") @db.VarChar(255) /// 树状路径

  // 关联字段
  microId   String?    @map("micro_id") /// 关联应用 id
  microApp  MicroApp?  @relation(fields: [microId], references: [id]) /// 关联应用，一对一
  parentId  String?    @map("parent_id") /// 父级 ID
  parent    Menu?      @relation("MenuHierarchy", fields: [parentId], references: [id]) /// 父级
  children  Menu[]     @relation("MenuHierarchy") /// 子级
  roleMenus RoleMenu[] /// 角色菜单关联

  // 审计字段
  createdBy   String   @map("created_by") /// 创建者账号
  creatorName String   @map("creator_name") /// 创建者姓名
  createdAt   DateTime @default(now()) @map(name: "created_at") /// 创建时间
  updatedBy   String   @map("updated_by") /// 更新者账号
  updaterName String   @map("updater_name") /// 更新者姓名
  updatedAt   DateTime @updatedAt @map(name: "updated_at") /// 更新时间
  deletedAt   Int?     @default(-1) @map("deleted_at") /// 删除标记, -1 表示未删除，数字表示时间戳

  @@map("sys_menu")
}

model Department {
  // 基础标识
  id String @id @default(nanoid()) @db.VarChar(21)

  // 核心业务字段
  name     String /// 部门名称
  code     String /// 部门编码
  treePath String @map("tree_path") @db.VarChar(255) /// 树状路径
  enabled  Int    @default(1) @db.TinyInt /// 是否启用  0: 否； 1：是

  // 关联字段
  parentId String?            @map("parent_id") /// 父级ID
  parent   Department?        @relation("DepartmentHierarchy", fields: [parentId], references: [id], map: "sys_department_parent_fkey") /// 父级
  children Department[]       @relation("DepartmentHierarchy") /// 子级
  members  DepartmentMember[]
  users    User[]

  // 审计字段
  createdBy   String   @map("created_by") /// 创建者账号
  creatorName String   @map("creator_name") /// 创建者姓名
  createdAt   DateTime @default(now()) @map(name: "created_at") /// 创建时间
  updatedBy   String   @map("updated_by") /// 更新者账号
  updaterName String   @map("updater_name") /// 更新者姓名
  updatedAt   DateTime @updatedAt @map(name: "updated_at") /// 更新时间
  deletedAt   Int?     @default(-1) @map("deleted_at") /// 删除标记, -1 表示未删除，数字表示时间戳

  @@unique([code, deletedAt])
  @@map("sys_department")
}

enum DepartmentMemberTypeEnum {
  OWNER
  MANAGER
  MEMBER

  @@map("sys_department_member_type")
}

model DepartmentMember {
  // 基础标识
  id String @id @default(nanoid()) @db.VarChar(21)

  // 核心业务字段
  departmentId String                   @map("department_id") /// 部门ID
  userId       String                   @map("user_id") /// 用户ID
  type         DepartmentMemberTypeEnum @default(MEMBER) /// 成员类型

  // 关联字段
  department            Department             @relation(fields: [departmentId], references: [id], map: "sys_department_member_department_fkey")
  user                  User                   @relation(fields: [userId], references: [id], map: "sys_department_member_user_fkey")
  departmentMemberRoles DepartmentMemberRole[] /// 成员角色关联

  // 审计字段
  createdBy   String   @map("created_by") /// 创建者账号
  creatorName String   @map("creator_name") /// 创建者姓名
  createdAt   DateTime @default(now()) @map(name: "created_at") /// 创建时间
  updatedBy   String   @map("updated_by") /// 更新者账号
  updaterName String   @map("updater_name") /// 更新者姓名
  updatedAt   DateTime @updatedAt @map(name: "updated_at") /// 更新时间
  deletedAt   Int?     @default(-1) @map("deleted_at") /// 删除标记, -1 表示未删除，数字表示时间戳

  @@unique([userId, deletedAt])
  @@map("sys_department_member")
}

model DepartmentMemberRole {
  // 基础标识
  id String @id @default(nanoid()) @db.VarChar(21)

  // 核心业务字段
  departmentId String @map("department_id") /// 冗余字段，部门ID
  userId       String @map("user_id") /// 冗余字段，用户ID
  memberId     String @map("member_id") /// 部门成员ID
  roleId       String @map("role_id") /// 角色ID

  // 关联字段
  member DepartmentMember @relation(fields: [memberId], references: [id])
  role   Role             @relation(fields: [roleId], references: [id])

  // 审计字段
  createdBy   String   @map("created_by") /// 创建者账号
  creatorName String   @map("creator_name") /// 创建者姓名
  createdAt   DateTime @default(now()) @map(name: "created_at") /// 创建时间
  updatedBy   String   @map("updated_by") /// 更新者账号
  updaterName String   @map("updater_name") /// 更新者姓名
  updatedAt   DateTime @updatedAt @map(name: "updated_at") /// 更新时间
  deletedAt   Int?     @default(-1) @map("deleted_at") /// 删除标记, -1 表示未删除，数字表示时间戳

  @@unique([memberId, roleId, deletedAt])
  @@map("sys_department_member_role")
}

enum AuthTypeEnum {
  TICKET
  JWT
  PARENT

  @@map("sys_micro_app_auth_type")
}

model MicroApp {
  // 基础标识
  id String @id @default(nanoid()) @db.VarChar(21) /// 应用ID

  // 核心业务字段
  code        String /// 应用编码,限英文、下划线、中划线
  name        String /// 应用名称
  url         String /// 应用资源地址
  query       String? /// 查询参数 a=1&b=2&..., 使用 {{token}} 动态替换变量
  icon        String? /// 应用图标
  description String? /// 应用描述
  loader      Int          @default(0) @db.TinyInt /// 应用加载器 0: micro-app; 1: iframe; 2: wujie; 3: qiankun
  config      Json         @default("{}") /// 应用配置
  isSSR       Int          @default(0) @db.TinyInt /// 是否 SSR 应用 0: 否； 1：是
  authType    AuthTypeEnum @default(TICKET) @map("auth_type") /// 授权方式 'TICKET' | 'JWT' | 'PARENT'
  sort        Int?         @default(0) /// 排序
  version     String       @default("1.0.0") /// 应用版本
  enabled     Int          @default(1) @db.TinyInt /// 是否启用  0: 否； 1：是

  // 关联字段
  menus Menu[]

  // 审计字段
  createdBy   String   @map("created_by") /// 创建者账号
  creatorName String   @map("creator_name") /// 创建者姓名
  createdAt   DateTime @default(now()) @map(name: "created_at") /// 创建时间
  updatedBy   String   @map("updated_by") /// 更新者账号
  updaterName String   @map("updater_name") /// 更新者姓名
  updatedAt   DateTime @updatedAt @map(name: "updated_at") /// 更新时间
  deletedAt   Int?     @default(-1) @map("deleted_at") /// 删除标记, -1 表示未删除，数字表示时间戳

  @@unique([code, deletedAt])
  @@unique([url, deletedAt])
  @@map("sys_micro_app")
}
