-- CreateTable
CREATE TABLE `sys_user` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `uid` VARCHAR(191) NOT NULL,
    `account` VARCHAR(191) NOT NULL,
    `email` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `telephone` VARCHAR(20) NOT NULL,
    `gender` TINYINT NOT NULL,
    `simUid` VARCHAR(191) NULL,
    `password` VARCHAR(128) NULL,
    `enabled` TINYINT NOT NULL DEFAULT 1,
    `created_by` VARCHAR(191) NOT NULL,
    `creator_name` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `updated_by` VARCHAR(191) NOT NULL,
    `updater_name` VARCHAR(191) NOT NULL,
    `deleted_at` INTEGER NULL DEFAULT -1,

    UNIQUE INDEX `sys_user_uid_deleted_at_key`(`uid`, `deleted_at`),
    UNIQUE INDEX `sys_user_account_deleted_at_key`(`account`, `deleted_at`),
    UNIQUE INDEX `sys_user_email_deleted_at_key`(`email`, `deleted_at`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `sys_role` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `code` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `parent_id` BIGINT UNSIGNED NULL,
    `path` VARCHAR(191) NOT NULL,
    `type` TINYINT NOT NULL DEFAULT 0,
    `enabled` TINYINT NOT NULL DEFAULT 1,
    `created_by` VARCHAR(191) NOT NULL,
    `creator_name` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `updated_by` VARCHAR(191) NOT NULL,
    `updater_name` VARCHAR(191) NOT NULL,
    `deleted_at` INTEGER NULL DEFAULT -1,

    INDEX `sys_role_parent_id_path_idx`(`parent_id`, `path`),
    UNIQUE INDEX `sys_role_code_deleted_at_key`(`code`, `deleted_at`),
    UNIQUE INDEX `sys_role_path_deleted_at_key`(`path`, `deleted_at`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `sys_permission` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `code` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `created_by` VARCHAR(191) NOT NULL,
    `creator_name` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `updated_by` VARCHAR(191) NOT NULL,
    `updater_name` VARCHAR(191) NOT NULL,
    `deleted_at` INTEGER NULL DEFAULT -1,

    UNIQUE INDEX `sys_permission_code_deleted_at_key`(`code`, `deleted_at`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `sys_api_resources` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `method` VARCHAR(191) NOT NULL,
    `path` VARCHAR(191) NOT NULL,
    `authorized` TINYINT NOT NULL DEFAULT 1,
    `deletable` TINYINT NOT NULL DEFAULT 1,
    `editable` TINYINT NOT NULL DEFAULT 1,
    `created_by` VARCHAR(191) NOT NULL,
    `creator_name` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `updated_by` VARCHAR(191) NOT NULL,
    `updater_name` VARCHAR(191) NOT NULL,
    `deleted_at` INTEGER NULL DEFAULT -1,
    `permission_id` BIGINT UNSIGNED NULL,

    UNIQUE INDEX `sys_api_resources_permission_id_key`(`permission_id`),
    UNIQUE INDEX `sys_api_resources_method_path_deleted_at_key`(`method`, `path`, `deleted_at`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `sys_menu` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `icon` VARCHAR(191) NULL,
    `type` TINYINT NOT NULL DEFAULT 0,
    `path` VARCHAR(191) NOT NULL,
    `target` TINYINT NOT NULL DEFAULT 0,
    `query` VARCHAR(191) NULL,
    `sort` INTEGER NULL DEFAULT 0,
    `hidden_in_menu` TINYINT NOT NULL DEFAULT 0,
    `enabled` TINYINT NOT NULL DEFAULT 1,
    `micro_id` BIGINT NULL,
    `parent_id` BIGINT UNSIGNED NULL,
    `created_by` VARCHAR(191) NOT NULL,
    `creator_name` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `updated_by` VARCHAR(191) NOT NULL,
    `updater_name` VARCHAR(191) NOT NULL,
    `deleted_at` INTEGER NULL DEFAULT -1,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `sys_micro_app` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `code` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `url` VARCHAR(191) NOT NULL,
    `query` VARCHAR(191) NULL,
    `icon` VARCHAR(191) NULL,
    `description` VARCHAR(191) NULL,
    `loader` TINYINT NOT NULL DEFAULT 0,
    `config` JSON NOT NULL,
    `isSSR` TINYINT NOT NULL DEFAULT 0,
    `auth_type` VARCHAR(191) NOT NULL DEFAULT 'ticket',
    `sort` INTEGER NULL DEFAULT 0,
    `version` VARCHAR(191) NOT NULL DEFAULT '1.0.0',
    `created_by` VARCHAR(191) NOT NULL,
    `creator_name` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `updated_by` VARCHAR(191) NOT NULL,
    `updater_name` VARCHAR(191) NOT NULL,
    `enabled` TINYINT NOT NULL DEFAULT 1,
    `deleted_at` INTEGER NULL DEFAULT -1,

    UNIQUE INDEX `sys_micro_app_code_deleted_at_key`(`code`, `deleted_at`),
    UNIQUE INDEX `sys_micro_app_url_deleted_at_key`(`url`, `deleted_at`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `_UserToRole` (
    `A` BIGINT UNSIGNED NOT NULL,
    `B` BIGINT UNSIGNED NOT NULL,

    UNIQUE INDEX `_UserToRole_AB_unique`(`A`, `B`),
    INDEX `_UserToRole_B_index`(`B`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `_MenuToPermission` (
    `A` BIGINT UNSIGNED NOT NULL,
    `B` BIGINT UNSIGNED NOT NULL,

    UNIQUE INDEX `_MenuToPermission_AB_unique`(`A`, `B`),
    INDEX `_MenuToPermission_B_index`(`B`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `sys_role` ADD CONSTRAINT `sys_role_parent_id_fkey` FOREIGN KEY (`parent_id`) REFERENCES `sys_role`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `sys_api_resources` ADD CONSTRAINT `sys_api_resources_permission_id_fkey` FOREIGN KEY (`permission_id`) REFERENCES `sys_permission`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `sys_menu` ADD CONSTRAINT `sys_menu_micro_id_fkey` FOREIGN KEY (`micro_id`) REFERENCES `sys_micro_app`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `sys_menu` ADD CONSTRAINT `sys_menu_parent_id_fkey` FOREIGN KEY (`parent_id`) REFERENCES `sys_menu`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `_UserToRole` ADD CONSTRAINT `_UserToRole_A_fkey` FOREIGN KEY (`A`) REFERENCES `sys_role`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `_UserToRole` ADD CONSTRAINT `_UserToRole_B_fkey` FOREIGN KEY (`B`) REFERENCES `sys_user`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `_MenuToPermission` ADD CONSTRAINT `_MenuToPermission_A_fkey` FOREIGN KEY (`A`) REFERENCES `sys_menu`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `_MenuToPermission` ADD CONSTRAINT `_MenuToPermission_B_fkey` FOREIGN KEY (`B`) REFERENCES `sys_permission`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
