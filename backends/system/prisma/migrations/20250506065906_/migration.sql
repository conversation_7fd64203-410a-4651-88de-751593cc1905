/*
  Warnings:

  - You are about to drop the `sys_api_resources` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE `sys_api_resources` DROP FOREIGN KEY `sys_api_resources_permission_id_fkey`;

-- AlterTable
ALTER TABLE `sys_user` ADD COLUMN `department_id` BIGINT UNSIGNED NULL;

-- DropTable
DROP TABLE `sys_api_resources`;

-- CreateTable
CREATE TABLE `sys_api_resource` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `method` VARCHAR(191) NOT NULL,
    `path` VARCHAR(191) NOT NULL,
    `authorized` TINYINT NOT NULL DEFAULT 1,
    `deletable` TINYINT NOT NULL DEFAULT 1,
    `editable` TINYINT NOT NULL DEFAULT 1,
    `created_by` VARCHAR(191) NOT NULL,
    `creator_name` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `updated_by` VARCHAR(191) NOT NULL,
    `updater_name` VARCHAR(191) NOT NULL,
    `deleted_at` INTEGER NULL DEFAULT -1,
    `permission_id` BIGINT UNSIGNED NULL,

    UNIQUE INDEX `sys_api_resource_permission_id_key`(`permission_id`),
    UNIQUE INDEX `sys_api_resource_method_path_deleted_at_key`(`method`, `path`, `deleted_at`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `sys_department` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `code` VARCHAR(191) NOT NULL,
    `parent_id` BIGINT UNSIGNED NULL,
    `leader_account` BIGINT UNSIGNED NULL,
    `leader_name` VARCHAR(191) NULL,
    `leader_telephone` VARCHAR(191) NULL,
    `leader_email` VARCHAR(191) NULL,
    `created_by` VARCHAR(191) NOT NULL,
    `creator_name` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `updated_by` VARCHAR(191) NOT NULL,
    `updater_name` VARCHAR(191) NOT NULL,
    `enabled` TINYINT NOT NULL DEFAULT 1,
    `deleted_at` INTEGER NULL DEFAULT -1,

    UNIQUE INDEX `sys_department_code_deleted_at_key`(`code`, `deleted_at`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `_DepartmentToRole` (
    `A` BIGINT UNSIGNED NOT NULL,
    `B` BIGINT UNSIGNED NOT NULL,

    UNIQUE INDEX `_DepartmentToRole_AB_unique`(`A`, `B`),
    INDEX `_DepartmentToRole_B_index`(`B`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `sys_user` ADD CONSTRAINT `sys_user_department_id_fkey` FOREIGN KEY (`department_id`) REFERENCES `sys_department`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `sys_api_resource` ADD CONSTRAINT `sys_api_resource_permission_id_fkey` FOREIGN KEY (`permission_id`) REFERENCES `sys_permission`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `sys_department` ADD CONSTRAINT `sys_department_parent_id_fkey` FOREIGN KEY (`parent_id`) REFERENCES `sys_department`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `_DepartmentToRole` ADD CONSTRAINT `_DepartmentToRole_A_fkey` FOREIGN KEY (`A`) REFERENCES `sys_department`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `_DepartmentToRole` ADD CONSTRAINT `_DepartmentToRole_B_fkey` FOREIGN KEY (`B`) REFERENCES `sys_role`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
