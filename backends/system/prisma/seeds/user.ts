import type { Prisma, PrismaClient } from '@prisma/client'
import { nanoid } from 'nanoid'
import { adminRole, superAdminRole, userRole } from './role'

export const systemUser: Prisma.UserCreateInput = {
  name: 'system',
  enabled: 1,
  account: 'system',
  email: '<EMAIL>',
  emailVerified: true,
  telephone: '***********',
  gender: 0,
  lang: 'zh-CN',
  createdBy: 'seed',
  creatorName: 'seed',
  updatedBy: 'seed',
  updaterName: 'seed',
}

export const superAdminUser: Prisma.UserCreateInput = {
  name: '超级管理员',
  enabled: 1,
  account: 'super-admin',
  email: '<EMAIL>',
  emailVerified: true,
  telephone: '***********',
  gender: 0,
  lang: 'zh-CN',
  createdBy: 'seed',
  creatorName: 'seed',
  updatedBy: 'seed',
  updaterName: 'seed',
}

export const adminUser: Prisma.UserCreateInput = {
  name: '系统管理员',
  enabled: 1,
  account: 'admin',
  email: '<EMAIL>',
  emailVerified: true,
  telephone: '***********',
  gender: 0,
  lang: 'zh-CN',
  createdBy: 'seed',
  creatorName: 'seed',
  updatedBy: 'seed',
  updaterName: 'seed',
}

export const userUser: Prisma.UserCreateInput = {
  name: '普通用户',
  enabled: 1,
  account: 'user',
  email: '<EMAIL>',
  emailVerified: true,
  telephone: '***********',
  gender: 0,
  lang: 'zh-CN',
  createdBy: 'seed',
  creatorName: 'seed',
  updatedBy: 'seed',
  updaterName: 'seed',
}

export default async function seed(prisma: PrismaClient) {
  const superAdminRoleEntity = await prisma.role.findFirst({ where: { code: superAdminRole.code } })
  const adminRoleEntity = await prisma.role.findFirst({ where: { code: adminRole.code } })
  const userRoleEntity = await prisma.role.findFirst({ where: { code: userRole.code } })

  if (!superAdminRoleEntity || !adminRoleEntity || !userRoleEntity) {
    throw new Error('角色不存在')
  }

  const systemUserExists = await prisma.user.findFirst({ where: { email: systemUser.email } })
  console.log('🚀 ~ seed ~ systemUserExists:', systemUserExists)
  if (!systemUserExists) {
    const user = await prisma.user.create({ data: systemUser })
    await prisma.userRole.create({
      data: {
        id: nanoid(),
        userId: user.id,
        roleId: superAdminRoleEntity.id,
        createdBy: 'seed',
        creatorName: 'seed',
        updatedBy: 'seed',
        updaterName: 'seed',
      },
    })
  }

  const superAdminUserExists = await prisma.user.findFirst({ where: { email: superAdminUser.email } })
  if (!superAdminUserExists) {
    const user = await prisma.user.create({ data: superAdminUser })
    await prisma.userRole.create({
      data: {
        id: nanoid(),
        userId: user.id,
        roleId: superAdminRoleEntity.id,
        createdBy: 'seed',
        creatorName: 'seed',
        updatedBy: 'seed',
        updaterName: 'seed',
      },
    })
  }

  const adminUserExists = await prisma.user.findFirst({ where: { email: adminUser.email } })
  if (!adminUserExists) {
    const user = await prisma.user.create({ data: adminUser })
    await prisma.userRole.create({
      data: {
        id: nanoid(),
        userId: user.id,
        roleId: adminRoleEntity.id,
        createdBy: 'seed',
        creatorName: 'seed',
        updatedBy: 'seed',
        updaterName: 'seed',
      },
    })
  }

  const userUserExists = await prisma.user.findFirst({ where: { email: userUser.email } })
  if (!userUserExists) {
    const user = await prisma.user.create({ data: userUser })
    await prisma.userRole.create({
      data: {
        id: nanoid(),
        userId: user.id,
        roleId: userRoleEntity.id,
        createdBy: 'seed',
        creatorName: 'seed',
        updatedBy: 'seed',
        updaterName: 'seed',
      },
    })
  }
}
