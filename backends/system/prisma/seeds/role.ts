import type { Prisma, PrismaClient } from '@prisma/client'
import { nanoid } from 'nanoid'

const superAdminRoleId = nanoid()
const adminRoleId = nanoid()
const userRoleId = nanoid()

export const superAdminRole: Prisma.RoleCreateInput = {
  name: '超级管理员',
  code: 'super-admin',
  type: 0,
  enabled: 1,
  treePath: superAdminRoleId,
  createdBy: 'seed',
  creatorName: 'seed',
  updatedBy: 'seed',
  updaterName: 'seed',
}

export const adminRole: Prisma.RoleCreateInput = {
  name: '系统管理员',
  code: 'admin',
  type: 0,
  enabled: 1,
  treePath: `${superAdminRoleId}/${adminRoleId}`,
  createdBy: 'seed',
  creatorName: 'seed',
  updatedBy: 'seed',
  updaterName: 'seed',
}

export const userRole: Prisma.RoleCreateInput = {
  name: '普通用户',
  code: 'user',
  type: 0,
  enabled: 1,
  treePath: `${superAdminRoleId}/${adminRoleId}/${userRoleId}`,
  createdBy: 'seed',
  creatorName: 'seed',
  updatedBy: 'seed',
  updaterName: 'seed',
}

export default async function seed(prisma: PrismaClient) {
  let superAdminRoleEntity = await prisma.role.findFirst({ where: { code: superAdminRole.code } })
  if (!superAdminRoleEntity) {
    superAdminRoleEntity = await prisma.role.create({ data: superAdminRole })
  }

  let adminRoleEntity = await prisma.role.findFirst({ where: { code: adminRole.code } })
  if (!adminRoleEntity) {
    adminRoleEntity = await prisma.role.create({
      data: {
        ...adminRole,
        parent: { connect: { id: superAdminRoleEntity.id } },
        treePath: `${superAdminRoleEntity.treePath}/${adminRoleId}`,
      },
    })
  }

  let userRoleEntity = await prisma.role.findFirst({ where: { code: userRole.code } })
  if (!userRoleEntity) {
    userRoleEntity = await prisma.role.create({
      data: {
        ...userRole,
        parent: { connect: { id: adminRoleEntity.id } },
        treePath: `${adminRoleEntity.treePath}/${userRoleId}`,
      },
    })
  }
}
