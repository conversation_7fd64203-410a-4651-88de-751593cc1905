// RBAC 系统初始化种子数据

import { PrismaClient } from '@prisma/client'
import { PERMISSIONS, SYSTEM_ROLES } from '../../src/types/rbac'

const prisma = new PrismaClient()

// 初始权限定义
const INITIAL_PERMISSIONS = [
  // 系统管理权限
  { code: PERMISSIONS.SYSTEM_USER_LIST, name: '查看用户列表', category: 'system', resource: 'user', action: 'list' },
  { code: PERMISSIONS.SYSTEM_USER_CREATE, name: '创建用户', category: 'system', resource: 'user', action: 'create' },
  { code: PERMISSIONS.SYSTEM_USER_UPDATE, name: '更新用户', category: 'system', resource: 'user', action: 'update' },
  { code: PERMISSIONS.SYSTEM_USER_DELETE, name: '删除用户', category: 'system', resource: 'user', action: 'delete' },
  { code: PERMISSIONS.SYSTEM_USER_ALL, name: '用户管理（全部）', category: 'system', resource: 'user', action: '*' },

  { code: PERMISSIONS.SYSTEM_ROLE_LIST, name: '查看角色列表', category: 'system', resource: 'role', action: 'list' },
  { code: PERMISSIONS.SYSTEM_ROLE_CREATE, name: '创建角色', category: 'system', resource: 'role', action: 'create' },
  { code: PERMISSIONS.SYSTEM_ROLE_UPDATE, name: '更新角色', category: 'system', resource: 'role', action: 'update' },
  { code: PERMISSIONS.SYSTEM_ROLE_DELETE, name: '删除角色', category: 'system', resource: 'role', action: 'delete' },
  { code: PERMISSIONS.SYSTEM_ROLE_ALL, name: '角色管理（全部）', category: 'system', resource: 'role', action: '*' },

  {
    code: PERMISSIONS.SYSTEM_PERMISSION_LIST,
    name: '查看权限列表',
    category: 'system',
    resource: 'permission',
    action: 'list',
  },
  {
    code: PERMISSIONS.SYSTEM_PERMISSION_CREATE,
    name: '创建权限',
    category: 'system',
    resource: 'permission',
    action: 'create',
  },
  {
    code: PERMISSIONS.SYSTEM_PERMISSION_UPDATE,
    name: '更新权限',
    category: 'system',
    resource: 'permission',
    action: 'update',
  },
  {
    code: PERMISSIONS.SYSTEM_PERMISSION_DELETE,
    name: '删除权限',
    category: 'system',
    resource: 'permission',
    action: 'delete',
  },
  {
    code: PERMISSIONS.SYSTEM_PERMISSION_ALL,
    name: '权限管理（全部）',
    category: 'system',
    resource: 'permission',
    action: '*',
  },

  {
    code: PERMISSIONS.SYSTEM_DEPARTMENT_LIST,
    name: '查看部门列表',
    category: 'system',
    resource: 'department',
    action: 'list',
  },
  {
    code: PERMISSIONS.SYSTEM_DEPARTMENT_CREATE,
    name: '创建部门',
    category: 'system',
    resource: 'department',
    action: 'create',
  },
  {
    code: PERMISSIONS.SYSTEM_DEPARTMENT_UPDATE,
    name: '更新部门',
    category: 'system',
    resource: 'department',
    action: 'update',
  },
  {
    code: PERMISSIONS.SYSTEM_DEPARTMENT_DELETE,
    name: '删除部门',
    category: 'system',
    resource: 'department',
    action: 'delete',
  },
  {
    code: PERMISSIONS.SYSTEM_DEPARTMENT_ALL,
    name: '部门管理（全部）',
    category: 'system',
    resource: 'department',
    action: '*',
  },

  { code: PERMISSIONS.SYSTEM_MENU_LIST, name: '查看菜单列表', category: 'system', resource: 'menu', action: 'list' },
  { code: PERMISSIONS.SYSTEM_MENU_CREATE, name: '创建菜单', category: 'system', resource: 'menu', action: 'create' },
  { code: PERMISSIONS.SYSTEM_MENU_UPDATE, name: '更新菜单', category: 'system', resource: 'menu', action: 'update' },
  { code: PERMISSIONS.SYSTEM_MENU_DELETE, name: '删除菜单', category: 'system', resource: 'menu', action: 'delete' },
  { code: PERMISSIONS.SYSTEM_MENU_ALL, name: '菜单管理（全部）', category: 'system', resource: 'menu', action: '*' },

  { code: PERMISSIONS.SYSTEM_ALL, name: '系统管理（全部）', category: 'system', resource: '*', action: '*' },

  // 数据权限
  { code: PERMISSIONS.DATA_SCOPE_ALL, name: '全部数据权限', category: 'data', resource: 'scope', action: 'all' },
  {
    code: PERMISSIONS.DATA_SCOPE_DEPARTMENT,
    name: '本部门数据权限',
    category: 'data',
    resource: 'scope',
    action: 'department',
  },
  { code: PERMISSIONS.DATA_SCOPE_SELF, name: '个人数据权限', category: 'data', resource: 'scope', action: 'self' },
  {
    code: PERMISSIONS.DATA_SCOPE_SUBORDINATE,
    name: '下级部门数据权限',
    category: 'data',
    resource: 'scope',
    action: 'subordinate',
  },

  // 业务权限（示例）
  { code: PERMISSIONS.BUSINESS_PROJECT_LIST, name: '查看项目列表', category: 'business', resource: 'project', action: 'list' },
  { code: PERMISSIONS.BUSINESS_PROJECT_CREATE, name: '创建项目', category: 'business', resource: 'project', action: 'create' },
  { code: PERMISSIONS.BUSINESS_PROJECT_UPDATE, name: '更新项目', category: 'business', resource: 'project', action: 'update' },
  { code: PERMISSIONS.BUSINESS_PROJECT_DELETE, name: '删除项目', category: 'business', resource: 'project', action: 'delete' },
  { code: PERMISSIONS.BUSINESS_PROJECT_ALL, name: '项目管理（全部）', category: 'business', resource: 'project', action: '*' },

  { code: PERMISSIONS.BUSINESS_TASK_LIST, name: '查看任务列表', category: 'business', resource: 'task', action: 'list' },
  { code: PERMISSIONS.BUSINESS_TASK_CREATE, name: '创建任务', category: 'business', resource: 'task', action: 'create' },
  { code: PERMISSIONS.BUSINESS_TASK_UPDATE, name: '更新任务', category: 'business', resource: 'task', action: 'update' },
  { code: PERMISSIONS.BUSINESS_TASK_ASSIGN, name: '分配任务', category: 'business', resource: 'task', action: 'assign' },
  { code: PERMISSIONS.BUSINESS_TASK_ALL, name: '任务管理（全部）', category: 'business', resource: 'task', action: '*' },

  { code: PERMISSIONS.BUSINESS_ALL, name: '业务管理（全部）', category: 'business', resource: '*', action: '*' },

  // 超级权限
  { code: PERMISSIONS.SUPER, name: '超级权限（全部）', category: 'system', resource: '*', action: '*' },
]

// 初始角色定义
const INITIAL_ROLES = [
  {
    code: SYSTEM_ROLES.SUPER_ADMIN,
    name: '超级管理员',
    type: 0, // 系统角色
    dataScope: 0, // 全部数据
    isSystem: 1,
    parentId: null, // 根角色
    permissions: [PERMISSIONS.SUPER], // 所有权限
  },
  {
    code: SYSTEM_ROLES.DEPT_ADMIN,
    name: '部门管理员',
    type: 0,
    dataScope: 3, // 下级部门数据权限
    isSystem: 1,
    parentCode: SYSTEM_ROLES.SUPER_ADMIN,
    permissions: [
      PERMISSIONS.SYSTEM_USER_LIST,
      PERMISSIONS.SYSTEM_USER_UPDATE,
      PERMISSIONS.SYSTEM_DEPARTMENT_LIST,
      PERMISSIONS.SYSTEM_DEPARTMENT_UPDATE,
      PERMISSIONS.BUSINESS_ALL, // 业务管理全部权限
      PERMISSIONS.DATA_SCOPE_SUBORDINATE,
    ],
  },
  {
    code: SYSTEM_ROLES.SUB_DEPT_ADMIN,
    name: '子部门管理员',
    type: 0,
    dataScope: 1, // 本部门数据权限
    isSystem: 1,
    parentCode: SYSTEM_ROLES.DEPT_ADMIN,
    permissions: [
      PERMISSIONS.SYSTEM_USER_LIST,
      PERMISSIONS.SYSTEM_USER_UPDATE,
      PERMISSIONS.SYSTEM_DEPARTMENT_LIST,
      PERMISSIONS.BUSINESS_PROJECT_ALL,
      PERMISSIONS.BUSINESS_TASK_ALL,
      PERMISSIONS.DATA_SCOPE_DEPARTMENT,
    ],
  },
  {
    code: SYSTEM_ROLES.BUSINESS_ADMIN,
    name: '业务系统管理员',
    type: 1, // 业务角色
    dataScope: 0, // 全部数据
    isSystem: 0,
    parentCode: SYSTEM_ROLES.SUPER_ADMIN,
    permissions: [
      PERMISSIONS.BUSINESS_ALL, // 业务管理全部权限
      PERMISSIONS.SYSTEM_USER_LIST,
      PERMISSIONS.DATA_SCOPE_ALL,
    ],
  },
  {
    code: SYSTEM_ROLES.DEVELOPER,
    name: '开发人员',
    type: 1,
    dataScope: 2, // 仅个人数据
    isSystem: 0,
    parentCode: SYSTEM_ROLES.BUSINESS_ADMIN,
    permissions: [
      PERMISSIONS.BUSINESS_PROJECT_LIST,
      PERMISSIONS.BUSINESS_PROJECT_UPDATE,
      PERMISSIONS.BUSINESS_TASK_LIST,
      PERMISSIONS.BUSINESS_TASK_UPDATE,
      PERMISSIONS.DATA_SCOPE_SELF,
    ],
  },
  {
    code: SYSTEM_ROLES.TESTER,
    name: '测试人员',
    type: 1,
    dataScope: 2, // 仅个人数据
    isSystem: 0,
    parentCode: SYSTEM_ROLES.BUSINESS_ADMIN,
    permissions: [
      PERMISSIONS.BUSINESS_PROJECT_LIST,
      PERMISSIONS.BUSINESS_TASK_LIST,
      PERMISSIONS.BUSINESS_TASK_UPDATE,
      PERMISSIONS.DATA_SCOPE_SELF,
    ],
  },
  {
    code: SYSTEM_ROLES.NORMAL_USER,
    name: '普通用户',
    type: 1, // 业务角色
    dataScope: 2, // 仅个人数据
    isSystem: 0,
    parentId: null, // 独立角色，不继承其他角色
    permissions: [PERMISSIONS.BUSINESS_PROJECT_LIST, PERMISSIONS.BUSINESS_TASK_LIST, PERMISSIONS.DATA_SCOPE_SELF],
  },
]

async function seedRBACData() {
  console.log('🌱 开始初始化 RBAC 数据...')

  try {
    // 1. 创建权限
    console.log('📝 创建权限...')
    for (const permission of INITIAL_PERMISSIONS) {
      await prisma.permission.upsert({
        where: {
          code_deletedAt: {
            code: permission.code,
            deletedAt: -1,
          },
        },
        update: permission,
        create: {
          ...permission,
          createdBy: 'system',
          creatorName: 'system',
          updatedBy: 'system',
          updaterName: 'system',
        },
      })
    }
    console.log(`✅ 创建了 ${INITIAL_PERMISSIONS.length} 个权限`)

    // 2. 创建角色
    console.log('👥 创建角色...')
    const roleMap = new Map<string, string>() // code -> id

    for (const roleData of INITIAL_ROLES) {
      const { permissions, parentCode, ...role } = roleData

      // 处理父级角色
      let parentId = role.parentId
      if (parentCode && roleMap.has(parentCode)) {
        parentId = roleMap.get(parentCode)!
      }

      // 生成树路径
      let treePath = role.code
      if (parentId) {
        const parent = await prisma.role.findUnique({ where: { id: parentId } })
        if (parent) {
          treePath = `${parent.treePath}.${role.code}`
        }
      }

      const createdRole = await prisma.role.upsert({
        where: { code: role.code },
        update: {
          ...role,
          parentId,
          treePath,
          updatedBy: 'system',
          updaterName: 'system',
        },
        create: {
          ...role,
          parentId,
          treePath,
          createdBy: 'system',
          creatorName: 'system',
          updatedBy: 'system',
          updaterName: 'system',
        },
      })

      roleMap.set(role.code, createdRole.id)

      // 分配权限
      if (permissions && permissions.length > 0) {
        const permissionRecords = await prisma.permission.findMany({
          where: { code: { in: permissions } },
        })

        for (const permission of permissionRecords) {
          await prisma.rolePermission.upsert({
            where: {
              roleId_permissionId: {
                roleId: createdRole.id,
                permissionId: permission.id,
              },
            },
            update: {},
            create: {
              roleId: createdRole.id,
              permissionId: permission.id,
              createdBy: 'system',
              creatorName: 'system',
              updatedBy: 'system',
              updaterName: 'system',
            },
          })
        }
      }
    }
    console.log(`✅ 创建了 ${INITIAL_ROLES.length} 个角色`)

    // 3. 创建默认部门（可选）
    console.log('🏢 创建默认部门...')
    const rootDepartment = await prisma.department.upsert({
      where: { code: 'ROOT' },
      update: {},
      create: {
        code: 'ROOT',
        name: '根部门',
        treePath: 'ROOT',
        enabled: 1,
        createdBy: 'system',
        creatorName: 'system',
        updatedBy: 'system',
        updaterName: 'system',
      },
    })
    console.log('✅ 创建了根部门')

    console.log('🎉 RBAC 数据初始化完成！')

    // 输出统计信息
    const permissionCount = await prisma.permission.count()
    const roleCount = await prisma.role.count()
    const departmentCount = await prisma.department.count()

    console.log(`📊 统计信息:`)
    console.log(`   权限数量: ${permissionCount}`)
    console.log(`   角色数量: ${roleCount}`)
    console.log(`   部门数量: ${departmentCount}`)
  } catch (error) {
    console.error('❌ RBAC 数据初始化失败:', error)
    throw error
  }
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  seedRBACData()
    .catch((e) => {
      console.error(e)
      process.exit(1)
    })
    .finally(async () => {
      await prisma.$disconnect()
    })
}

export { seedRBACData }
