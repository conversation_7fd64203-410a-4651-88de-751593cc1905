// 简化版 RBAC 系统初始化种子数据

import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function seedRBACData() {
  console.log('🌱 开始初始化 RBAC 数据...')

  try {
    // 1. 创建基础权限
    console.log('📝 创建权限...')

    const permissions = [
      { code: 'system:user:list', name: '查看用户列表', category: 'system', resource: 'user', action: 'list' },
      { code: 'system:user:create', name: '创建用户', category: 'system', resource: 'user', action: 'create' },
      { code: 'system:user:update', name: '更新用户', category: 'system', resource: 'user', action: 'update' },
      { code: 'system:user:delete', name: '删除用户', category: 'system', resource: 'user', action: 'delete' },
      { code: 'system:role:list', name: '查看角色列表', category: 'system', resource: 'role', action: 'list' },
      { code: 'system:role:create', name: '创建角色', category: 'system', resource: 'role', action: 'create' },
      { code: 'system:role:update', name: '更新角色', category: 'system', resource: 'role', action: 'update' },
      { code: 'system:role:delete', name: '删除角色', category: 'system', resource: 'role', action: 'delete' },
      { code: 'system:permission:list', name: '查看权限列表', category: 'system', resource: 'permission', action: 'list' },
      { code: 'system:department:list', name: '查看部门列表', category: 'system', resource: 'department', action: 'list' },
      { code: 'system:department:create', name: '创建部门', category: 'system', resource: 'department', action: 'create' },
      { code: 'data:scope:all', name: '全部数据权限', category: 'data', resource: 'scope', action: 'all' },
      { code: 'data:scope:department', name: '本部门数据权限', category: 'data', resource: 'scope', action: 'department' },
      { code: 'data:scope:self', name: '个人数据权限', category: 'data', resource: 'scope', action: 'self' },
      { code: '*', name: '超级权限', category: 'system', resource: '*', action: '*' },
    ]

    for (const permission of permissions) {
      const existing = await prisma.permission.findFirst({
        where: { code: permission.code },
      })

      if (!existing) {
        await prisma.permission.create({
          data: {
            ...permission,
            createdBy: 'system',
            creatorName: 'system',
            updatedBy: 'system',
            updaterName: 'system',
          },
        })
      }
    }
    console.log(`✅ 创建了 ${permissions.length} 个权限`)

    // 2. 创建基础角色
    console.log('👥 创建角色...')

    const roles = [
      {
        code: 'SUPER_ADMIN',
        name: '超级管理员',
        type: 0,
        dataScope: 0,
        isSystem: 1,
        treePath: 'SUPER_ADMIN',
        permissions: ['*'],
      },
      {
        code: 'DEPT_ADMIN',
        name: '部门管理员',
        type: 0,
        dataScope: 3,
        isSystem: 1,
        treePath: 'SUPER_ADMIN.DEPT_ADMIN',
        permissions: ['system:user:list', 'system:user:update', 'system:department:list', 'data:scope:department'],
      },
      {
        code: 'NORMAL_USER',
        name: '普通用户',
        type: 1,
        dataScope: 2,
        isSystem: 0,
        treePath: 'NORMAL_USER',
        permissions: ['data:scope:self'],
      },
    ]

    for (const roleData of roles) {
      const { permissions: rolePermissions, ...role } = roleData

      const existing = await prisma.role.findFirst({
        where: { code: role.code },
      })

      let createdRole
      if (!existing) {
        createdRole = await prisma.role.create({
          data: {
            ...role,
            createdBy: 'system',
            creatorName: 'system',
            updatedBy: 'system',
            updaterName: 'system',
          },
        })
      } else {
        createdRole = existing
      }

      // 分配权限
      if (rolePermissions && rolePermissions.length > 0) {
        for (const permissionCode of rolePermissions) {
          const permission = await prisma.permission.findFirst({
            where: { code: permissionCode },
          })

          if (permission) {
            const existingRolePermission = await prisma.rolePermission.findFirst({
              where: {
                roleId: createdRole.id,
                permissionId: permission.id,
              },
            })

            if (!existingRolePermission) {
              await prisma.rolePermission.create({
                data: {
                  roleId: createdRole.id,
                  permissionId: permission.id,
                  createdBy: 'system',
                  creatorName: 'system',
                  updatedBy: 'system',
                  updaterName: 'system',
                },
              })
            }
          }
        }
      }
    }
    console.log(`✅ 创建了 ${roles.length} 个角色`)

    // 3. 创建默认部门
    console.log('🏢 创建默认部门...')
    const existingDept = await prisma.department.findFirst({
      where: { code: 'ROOT' },
    })

    if (!existingDept) {
      await prisma.department.create({
        data: {
          code: 'ROOT',
          name: '根部门',
          treePath: 'ROOT',
          enabled: 1,
          createdBy: 'system',
          creatorName: 'system',
          updatedBy: 'system',
          updaterName: 'system',
        },
      })
    }
    console.log('✅ 创建了根部门')

    console.log('🎉 RBAC 数据初始化完成！')

    // 输出统计信息
    const permissionCount = await prisma.permission.count()
    const roleCount = await prisma.role.count()
    const departmentCount = await prisma.department.count()

    console.log(`📊 统计信息:`)
    console.log(`   权限数量: ${permissionCount}`)
    console.log(`   角色数量: ${roleCount}`)
    console.log(`   部门数量: ${departmentCount}`)
  } catch (error) {
    console.error('❌ RBAC 数据初始化失败:', error)
    throw error
  }
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  seedRBACData()
    .catch((e) => {
      console.error(e)
      process.exit(1)
    })
    .finally(async () => {
      await prisma.$disconnect()
    })
}

export { seedRBACData }
