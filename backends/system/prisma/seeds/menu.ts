import { PrismaClient } from '@prisma/client'
import { nanoid } from 'nanoid'

const db = new PrismaClient()

// 菜单数据类型定义
interface MenuData {
  name: string
  icon?: string
  type: number
  path: string
  target: number
  sort: number
  hiddenInMenu: number
  enabled: number
  children?: MenuData[]
}

// 基础菜单定义
const baseMenus: MenuData[] = [
  {
    name: '系统管理',
    icon: 'SettingOutlined',
    type: 0,
    path: '/system',
    target: 0,
    sort: 1,
    hiddenInMenu: 0,
    enabled: 1,
    children: [
      {
        name: '用户管理',
        icon: 'UserOutlined',
        type: 0,
        path: '/system/users',
        target: 0,
        sort: 1,
        hiddenInMenu: 0,
        enabled: 1,
      },
      {
        name: '角色管理',
        icon: 'TeamOutlined',
        type: 0,
        path: '/system/roles',
        target: 0,
        sort: 2,
        hiddenInMenu: 0,
        enabled: 1,
      },
      {
        name: '权限管理',
        icon: 'SafetyCertificateOutlined',
        type: 0,
        path: '/system/permissions',
        target: 0,
        sort: 3,
        hiddenInMenu: 0,
        enabled: 1,
      },
      {
        name: '菜单管理',
        icon: 'MenuOutlined',
        type: 0,
        path: '/system/menus',
        target: 0,
        sort: 4,
        hiddenInMenu: 0,
        enabled: 1,
      },
      {
        name: '部门管理',
        icon: 'ApartmentOutlined',
        type: 0,
        path: '/system/departments',
        target: 0,
        sort: 5,
        hiddenInMenu: 0,
        enabled: 1,
      },
    ],
  },
  {
    name: '微应用管理',
    icon: 'AppstoreOutlined',
    type: 0,
    path: '/micro-apps',
    target: 0,
    sort: 2,
    hiddenInMenu: 0,
    enabled: 1,
  },
]

async function createMenu(menuData: MenuData, parentId: string | null = null, parentPath: string = '') {
  const { children, ...menuInfo } = menuData
  const menuId = nanoid()
  const treePath = parentPath ? `${parentPath}/${menuId}` : `/${menuId}`

  // 创建菜单
  const menu = await db.menu.create({
    data: {
      id: menuId,
      ...menuInfo,
      parentId,
      treePath,
      createdBy: 'system',
      creatorName: 'system',
      updatedBy: 'system',
      updaterName: 'system',
    },
  })

  // 递归创建子菜单
  if (children && Array.isArray(children)) {
    for (const child of children) {
      await createMenu(child, menu.id, treePath)
    }
  }

  return menu
}

export async function seedMenu() {
  try {
    console.log('开始初始化菜单数据...')

    // 创建基础菜单
    for (const menuData of baseMenus) {
      await createMenu(menuData)
    }

    console.log('菜单数据初始化完成！')
  } catch (error) {
    console.error('菜单数据初始化失败：', error)
    throw error
  }
}
