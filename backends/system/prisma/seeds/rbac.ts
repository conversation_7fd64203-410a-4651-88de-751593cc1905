import { PrismaClient } from '@prisma/client'
import { nanoid } from 'nanoid'

const db = new PrismaClient()

// 基础权限定义
const basePermissions = [
  // 用户管理权限
  { code: 'user:list', name: '查看用户列表' },
  { code: 'user:create', name: '创建用户' },
  { code: 'user:read', name: '查看用户详情' },
  { code: 'user:update', name: '更新用户信息' },
  { code: 'user:delete', name: '删除用户' },

  // 角色管理权限
  { code: 'role:list', name: '查看角色列表' },
  { code: 'role:create', name: '创建角色' },
  { code: 'role:read', name: '查看角色详情' },
  { code: 'role:update', name: '更新角色信息' },
  { code: 'role:delete', name: '删除角色' },

  // 权限管理权限
  { code: 'permission:list', name: '查看权限列表' },
  { code: 'permission:create', name: '创建权限' },
  { code: 'permission:read', name: '查看权限详情' },
  { code: 'permission:update', name: '更新权限信息' },
  { code: 'permission:delete', name: '删除权限' },

  // 菜单管理权限
  { code: 'menu:list', name: '查看菜单列表' },
  { code: 'menu:create', name: '创建菜单' },
  { code: 'menu:read', name: '查看菜单详情' },
  { code: 'menu:update', name: '更新菜单信息' },
  { code: 'menu:delete', name: '删除菜单' },

  // 部门管理权限
  { code: 'department:list', name: '查看部门列表' },
  { code: 'department:create', name: '创建部门' },
  { code: 'department:read', name: '查看部门详情' },
  { code: 'department:update', name: '更新部门信息' },
  { code: 'department:delete', name: '删除部门' },
]

// 基础角色定义
const baseRoles = [
  {
    code: 'SUPER_ADMIN',
    name: '超级管理员',
    type: 0, // 系统角色
    enabled: 1,
  },
  {
    code: 'ADMIN',
    name: '管理员',
    type: 0,
    enabled: 1,
  },
  {
    code: 'USER',
    name: '普通用户',
    type: 0,
    enabled: 1,
  },
]

export async function seedRBAC() {
  try {
    console.log('开始初始化 RBAC 数据...')

    // 创建基础权限
    console.log('创建基础权限...')
    const permissions = await Promise.all(
      basePermissions.map(async (permission) => {
        const existing = await db.permission.findFirst({
          where: { code: permission.code },
        })
        if (existing) {
          console.log(`权限 ${permission.code} 已存在，跳过创建`)
          return existing
        }
        return db.permission.create({
          data: {
            id: nanoid(),
            ...permission,
            createdBy: 'system',
            creatorName: 'system',
            updatedBy: 'system',
            updaterName: 'system',
          },
        })
      })
    )

    // 创建基础角色
    console.log('创建基础角色...')
    const roles = await Promise.all(
      baseRoles.map(async (role) => {
        const existing = await db.role.findFirst({
          where: { code: role.code },
        })
        if (existing) {
          console.log(`角色 ${role.code} 已存在，跳过创建`)
          return existing
        }
        const roleId = nanoid()
        const newRole = await db.role.create({
          data: {
            id: roleId,
            ...role,
            treePath: `/${roleId}`,
            createdBy: 'system',
            creatorName: 'system',
            updatedBy: 'system',
            updaterName: 'system',
          },
        })
        return newRole
      })
    )

    // 为超级管理员分配所有权限
    console.log('为超级管理员分配权限...')
    const superAdminRole = roles.find((role) => role.code === 'SUPER_ADMIN')
    if (superAdminRole) {
      await Promise.all(
        permissions.map((permission) =>
          db.rolePermission.create({
            data: {
              id: nanoid(),
              roleId: superAdminRole.id,
              permissionId: permission.id,
              createdBy: 'system',
              creatorName: 'system',
              updatedBy: 'system',
              updaterName: 'system',
            },
          })
        )
      )

      // 为超级管理员分配所有菜单
      console.log('为超级管理员分配菜单...')
      const allMenus = await db.menu.findMany({})
      await Promise.all(
        allMenus.map((menu) =>
          db.roleMenu.create({
            data: {
              id: nanoid(),
              roleId: superAdminRole.id,
              menuId: menu.id,
              createdBy: 'system',
              creatorName: 'system',
              updatedBy: 'system',
              updaterName: 'system',
            },
          })
        )
      )
    }

    // 为管理员分配部分权限（除了权限管理相关的权限）
    console.log('为管理员分配权限...')
    const adminRole = roles.find((role) => role.code === 'ADMIN')
    if (adminRole) {
      const adminPermissions = permissions.filter((permission) => !permission.code.startsWith('permission:'))
      await Promise.all(
        adminPermissions.map((permission) =>
          db.rolePermission.create({
            data: {
              id: nanoid(),
              roleId: adminRole.id,
              permissionId: permission.id,
              createdBy: 'system',
              creatorName: 'system',
              updatedBy: 'system',
              updaterName: 'system',
            },
          })
        )
      )

      // 为管理员分配部分菜单（除了权限管理相关的菜单）
      console.log('为管理员分配菜单...')
      const adminMenus = await db.menu.findMany({
        where: {
          NOT: {
            path: {
              contains: '/system/permissions',
            },
          },
        },
      })
      await Promise.all(
        adminMenus.map((menu) =>
          db.roleMenu.create({
            data: {
              id: nanoid(),
              roleId: adminRole.id,
              menuId: menu.id,
              createdBy: 'system',
              creatorName: 'system',
              updatedBy: 'system',
              updaterName: 'system',
            },
          })
        )
      )
    }

    // 为普通用户分配基本权限
    console.log('为普通用户分配权限...')
    const userRole = roles.find((role) => role.code === 'USER')
    if (userRole) {
      const userPermissions = permissions.filter((permission) =>
        ['user:read', 'department:list', 'department:read'].includes(permission.code)
      )
      await Promise.all(
        userPermissions.map((permission) =>
          db.rolePermission.create({
            data: {
              id: nanoid(),
              roleId: userRole.id,
              permissionId: permission.id,
              createdBy: 'system',
              creatorName: 'system',
              updatedBy: 'system',
              updaterName: 'system',
            },
          })
        )
      )
    }

    console.log('RBAC 数据初始化完成！')
  } catch (error) {
    console.error('RBAC 数据初始化失败：', error)
    throw error
  }
}
