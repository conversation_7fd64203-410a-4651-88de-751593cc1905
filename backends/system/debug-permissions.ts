import { db } from './src/db'
import { roleService } from './src/services/role.service'
import { userRoleService } from './src/services/user-role.service'

async function debugPermissions() {
  try {
    console.log('🔍 开始调试权限查询...')

    // 获取测试用户
    const user = await db.user.findFirst()

    if (!user) {
      console.log('❌ 没有找到用户')
      return
    }

    console.log(`📝 测试用户: ${user.name} (${user.id})`)

    // 1. 检查用户的全局角色
    console.log('\n1. 检查用户全局角色:')
    const globalRoles = await userRoleService.getUserGlobalRoles(user.id)
    console.log(`   全局角色数量: ${globalRoles.length}`)
    globalRoles.forEach((role) => {
      console.log(`   - ${role.name} (${role.code}) - ID: ${role.id}`)
    })

    // 2. 检查第一个角色的权限
    if (globalRoles.length > 0) {
      const firstRole = globalRoles[0]
      console.log(`\n2. 检查角色 "${firstRole.name}" 的权限:`)

      // 直接查询角色权限
      const rolePermissions = await db.rolePermission.findMany({
        where: {
          roleId: firstRole.id,
        },
        include: {
          permission: true,
        },
      })

      console.log(`   角色权限关联数量: ${rolePermissions.length}`)
      rolePermissions.slice(0, 5).forEach((rp) => {
        console.log(`   - ${rp.permission?.name} (${rp.permission?.code})`)
      })

      // 使用 RoleService 查询
      const servicePermissions = await roleService.getRolePermissions(firstRole.id)
      console.log(`   通过 RoleService 查询的权限数量: ${servicePermissions.length}`)
      servicePermissions.slice(0, 5).forEach((p) => {
        console.log(`   - ${p.name} (${p.code})`)
      })

      // 使用 getRoleEffectivePermissions 查询
      const effectivePermissions = await roleService.getRoleEffectivePermissions(firstRole.id)
      console.log(`   有效权限数量: ${effectivePermissions.length}`)
      effectivePermissions.slice(0, 5).forEach((p) => {
        console.log(`   - ${p.name} (${p.code})`)
      })
    }

    // 3. 检查用户的全局权限
    console.log('\n3. 检查用户全局权限:')
    const globalPermissions = await userRoleService.getUserGlobalPermissions(user.id)
    console.log(`   全局权限数量: ${globalPermissions.length}`)
    globalPermissions.slice(0, 10).forEach((p) => {
      console.log(`   - ${p}`)
    })

    // 4. 检查用户的所有有效权限
    console.log('\n4. 检查用户所有有效权限:')
    const allPermissions = await userRoleService.getUserEffectivePermissions(user.id)
    console.log(`   所有有效权限数量: ${allPermissions.size}`)
    Array.from(allPermissions)
      .slice(0, 10)
      .forEach((p) => {
        console.log(`   - ${p}`)
      })
  } catch (error) {
    console.error('❌ 调试失败:', error)
  } finally {
    await db.$disconnect()
  }
}

debugPermissions().catch(console.error)
