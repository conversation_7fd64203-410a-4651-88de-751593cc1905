{"name": "@be/system", "private": true, "type": "module", "scripts": {"build": "tsc", "clean": "rimraf .turbo .next .cache .deploy dist node_modules", "db:deploy": "prisma migrate deploy", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:push": "prisma db push", "db:seed": "prisma db seed", "dev": "NODE_ENV=development tsx watch src/index.ts", "lint": "oxlint . && eslint .", "lint:fix": "prettier --check --write . && oxlint --fix --fix-suggestions . && eslint . --fix", "start": "NODE_ENV=production node dist/index.js"}, "lint-staged": {"*": ["prettier --check --write", "oxlint --fix --fix-suggestions", "eslint --fix"]}, "dependencies": {"@dotenvx/dotenvx": "^1.43.0", "@hono/node-server": "^1.14.1", "@hono/swagger-ui": "^0.5.1", "@hono/zod-validator": "^0.5.0", "@prisma/client": "6", "@scalar/hono-api-reference": "^0.8.5", "@t3-oss/env-core": "^0.13.0", "better-auth": "^1.2.7", "hono": "^4.7.8", "hono-openapi": "^0.4.7", "nanoid": "^5.1.5", "prisma": "6", "prisma-extension-soft-delete": "^2.0.1", "radash": "^12.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.24.3", "zod-openapi": "^4.2.4", "zod-prisma-types": "^3.2.4"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^20.11.17", "tsx": "^4.7.1", "typescript": "^5.8.3"}, "prisma": {"schema": "./prisma/schema.prisma", "seed": "tsx ./prisma/seed.ts"}}