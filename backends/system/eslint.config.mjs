import { FlatCompat } from '@eslint/eslintrc'
import honoEslint from '@repo/eslint-config/hono.mjs'
import { defineConfig } from 'eslint/config'
import path from 'node:path'
import { fileURLToPath } from 'node:url'

const __dirname = path.dirname(fileURLToPath(import.meta.url))

const compat = new FlatCompat({
  baseDirectory: __dirname,
})

export default defineConfig([
  honoEslint,
  ...compat.config({ parser: '@typescript-eslint/parser' }),
  { ignores: ['src/gen/**'] },
])
