import { db } from './src/db'
import { userRoleService } from './src/services/user-role.service'

async function assignTestRole() {
  try {
    console.log('🚀 开始为测试用户分配角色...')

    // 获取第一个用户
    const user = await db.user.findFirst()

    if (!user) {
      console.log('❌ 没有找到用户')
      return
    }

    // 获取超级管理员角色
    const superAdminRole = await db.role.findFirst({
      where: {
        code: 'SUPER_ADMIN',
      },
    })

    if (!superAdminRole) {
      console.log('❌ 没有找到超级管理员角色')
      return
    }

    console.log(`📝 为用户 ${user.name} (${user.id}) 分配角色 ${superAdminRole.name} (${superAdminRole.id})`)

    // 分配角色
    await userRoleService.assignGlobalRoles(user.id, [superAdminRole.id], {
      createdBy: 'system',
      creatorName: 'System',
      updatedBy: 'system',
      updaterName: 'System',
    })

    console.log('✅ 角色分配成功！')

    // 验证分配结果
    const userRoles = await userRoleService.getUserGlobalRoles(user.id)
    console.log(`🔍 用户当前角色数量: ${userRoles.length}`)
    userRoles.forEach((role) => {
      console.log(`   - ${role.name} (${role.code})`)
    })
  } catch (error) {
    console.error('❌ 分配角色失败:', error)
  } finally {
    await db.$disconnect()
  }
}

assignTestRole().catch(console.error)
