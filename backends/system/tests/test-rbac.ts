// RBAC 系统功能测试脚本

import { db } from '../src/db'
import { authorizationService } from '../src/services/authorization.service'
import { departmentMemberService } from '../src/services/department-member.service'
import { menuPermissionService } from '../src/services/menu-permission.service'
import { userRoleService } from '../src/services/user-role.service'

async function testRBACSystem() {
  console.log('🚀 开始测试 RBAC 权限系统...\n')

  try {
    // 1. 测试基础数据查询
    console.log('📊 1. 测试基础数据查询')

    const users = await db.user.findMany({ take: 3 })
    console.log(`   用户数量: ${users.length}`)

    const roles = await db.role.findMany({ take: 5 })
    console.log(`   角色数量: ${roles.length}`)

    const permissions = await db.permission.findMany({ take: 10 })
    console.log(`   权限数量: ${permissions.length}`)

    const departments = await db.department.findMany({ take: 5 })
    console.log(`   部门数量: ${departments.length}`)

    if (users.length === 0) {
      console.log('   ⚠️  没有找到用户数据，请先运行种子数据初始化')
      return
    }

    const testUser = users[0]
    console.log(`   测试用户: ${testUser.name} (${testUser.id})\n`)

    // 2. 测试用户权限查询
    console.log('🔐 2. 测试用户权限查询')

    const userPermissions = await authorizationService.getUserEffectivePermissions(testUser.id)
    console.log(`   用户有效权限数量: ${userPermissions.size}`)
    console.log(`   权限列表: ${Array.from(userPermissions).slice(0, 5).join(', ')}...`)

    const userRoles = await userRoleService.getUserEffectiveRoles(testUser.id)
    console.log(`   全局角色数量: ${userRoles.globalRoles.length}`)
    console.log(`   部门角色数量: ${userRoles.departmentRoles.length}\n`)

    // 3. 测试权限检查
    console.log('✅ 3. 测试权限检查')

    const testPermissions = ['system:user:list', 'system:user:create', 'system:role:list']

    for (const permission of testPermissions) {
      const hasPermission = await authorizationService.checkPermission(testUser.id, permission)
      console.log(`   ${permission}: ${hasPermission ? '✅' : '❌'}`)
    }

    // 测试批量权限检查
    const batchResult = await authorizationService.checkPermissions(testUser.id, testPermissions, 'ANY')
    console.log(`   批量权限检查 (ANY): ${batchResult ? '✅' : '❌'}\n`)

    // 4. 测试角色检查
    console.log('👤 4. 测试角色检查')

    const testRoles = ['SUPER_ADMIN', 'DEPT_ADMIN', 'NORMAL_USER']

    for (const role of testRoles) {
      const hasRole = await authorizationService.checkRole(testUser.id, role, 'ANY')
      console.log(`   ${role}: ${hasRole ? '✅' : '❌'}`)
    }
    console.log()

    // 5. 测试菜单权限
    console.log('📋 5. 测试菜单权限')

    const userMenus = await menuPermissionService.getUserMenus(testUser.id)
    console.log(`   用户可访问菜单数量: ${userMenus.length}`)

    if (userMenus.length > 0) {
      console.log(
        `   菜单示例: ${userMenus
          .slice(0, 3)
          .map((m) => m.name)
          .join(', ')}`
      )
    }

    const userMicroApps = await menuPermissionService.getUserMicroApps(testUser.id)
    console.log(`   用户可访问微应用数量: ${userMicroApps.length}\n`)

    // 6. 测试部门权限
    console.log('🏢 6. 测试部门权限')

    if (departments.length > 0) {
      const testDept = departments[0]
      console.log(`   测试部门: ${testDept.name} (${testDept.id})`)

      // 检查部门成员
      const deptMembers = await departmentMemberService.getDepartmentMembers(testDept.id)
      console.log(`   部门成员数量: ${deptMembers.length}`)

      // 检查用户是否可以访问该部门
      const canAccessDept = await authorizationService.checkDataPermission(testUser.id, 'department', 'read', {
        departmentId: testDept.id,
      })
      console.log(`   用户可访问该部门: ${canAccessDept ? '✅' : '❌'}`)
    }
    console.log()

    // 7. 测试数据权限范围
    console.log('📊 7. 测试数据权限范围')

    const dataScope = await authorizationService.getUserDataScope(testUser.id)
    console.log(`   用户数据权限范围: ${dataScope.join(', ')}`)

    // 8. 性能测试
    console.log('⚡ 8. 性能测试')

    const startTime = Date.now()

    // 执行多次权限检查
    for (let i = 0; i < 100; i++) {
      await authorizationService.checkPermission(testUser.id, 'system:user:list')
    }

    const endTime = Date.now()
    console.log(`   100次权限检查耗时: ${endTime - startTime}ms`)
    console.log(`   平均每次检查: ${(endTime - startTime) / 100}ms\n`)

    // 9. 测试缓存功能
    console.log('💾 9. 测试缓存功能')

    // 第一次查询（无缓存）
    const start1 = Date.now()
    await authorizationService.getUserEffectivePermissions(testUser.id)
    const time1 = Date.now() - start1

    // 第二次查询（有缓存）
    const start2 = Date.now()
    await authorizationService.getUserEffectivePermissions(testUser.id)
    const time2 = Date.now() - start2

    console.log(`   首次查询耗时: ${time1}ms`)
    console.log(`   缓存查询耗时: ${time2}ms`)
    console.log(`   缓存加速比: ${(time1 / time2).toFixed(2)}x\n`)

    console.log('🎉 RBAC 系统测试完成！所有功能正常运行。')
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error)
  } finally {
    await db.$disconnect()
  }
}

// 运行测试
testRBACSystem().catch(console.error)
