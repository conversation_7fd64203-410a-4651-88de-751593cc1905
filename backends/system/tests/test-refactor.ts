import type { Permission, Role } from '@prisma/client'
import { db } from '../src/db'
import { permissionService } from '../src/services/permission.service'
import { roleService } from '../src/services/role.service'
import { userRoleService } from '../src/services/user-role.service'

async function testRefactor() {
  console.log('🚀 开始测试 RBAC 系统重构结果...\n')

  try {
    // 1. 测试服务重命名
    console.log('1. 测试服务重命名:')
    console.log(`   ✅ roleService 实例: ${roleService.constructor.name}`)
    console.log(`   ✅ permissionService 实例: ${permissionService.constructor.name}`)
    console.log(`   ✅ userRoleService 实例: ${userRoleService.constructor.name}`)

    // 2. 测试权限格式
    console.log('\n2. 测试权限格式规范化:')
    const systemPermissions = await db.permission.findMany({
      where: {
        code: { startsWith: 'system:' },
      },
      take: 5,
    })

    systemPermissions.forEach((p) => {
      console.log(`   ✅ ${p.code} - ${p.name}`)
    })

    // 3. 测试角色服务功能
    console.log('\n3. 测试角色服务功能:')
    const roles = await roleService.getRoles({ page: 1, pageSize: 3 })
    console.log(`   ✅ 角色列表查询成功，共 ${roles.total} 个角色`)
    roles.data.forEach((role: Role) => {
      console.log(`      - ${role.name} (${role.code})`)
    })

    // 4. 测试权限服务功能
    console.log('\n4. 测试权限服务功能:')
    const permissions = await permissionService.getPermissions({ page: 1, pageSize: 3 })
    console.log(`   ✅ 权限列表查询成功，共 ${permissions.total} 个权限`)
    permissions.data.forEach((permission: Permission) => {
      console.log(`      - ${permission.code} - ${permission.name}`)
    })

    // 5. 测试用户角色服务功能
    console.log('\n5. 测试用户角色服务功能:')
    const user = await db.user.findFirst()
    if (user) {
      const userRoles = await userRoleService.getUserGlobalRoles(user.id)
      console.log(`   ✅ 用户 ${user.name} 的角色查询成功，共 ${userRoles.length} 个角色`)
      userRoles.forEach((role: Role) => {
        console.log(`      - ${role.name} (${role.code})`)
      })

      const userPermissions = await userRoleService.getUserGlobalPermissions(user.id)
      console.log(`   ✅ 用户权限查询成功，共 ${userPermissions.length} 个权限`)
    }

    // 6. 测试数据库统计
    console.log('\n6. 数据库统计:')
    const stats = await Promise.all([
      db.role.count(),
      db.permission.count(),
      db.user.count(),
      db.userRole.count(),
      db.rolePermission.count(),
    ])

    console.log(`   ✅ 角色总数: ${stats[0]}`)
    console.log(`   ✅ 权限总数: ${stats[1]}`)
    console.log(`   ✅ 用户总数: ${stats[2]}`)
    console.log(`   ✅ 用户角色关联: ${stats[3]}`)
    console.log(`   ✅ 角色权限关联: ${stats[4]}`)

    console.log('\n🎉 RBAC 系统重构测试完成！所有功能正常工作。')
  } catch (error) {
    console.error('❌ 测试失败:', error)
    process.exit(1)
  } finally {
    await db.$disconnect()
  }
}

testRefactor().catch(console.error)
