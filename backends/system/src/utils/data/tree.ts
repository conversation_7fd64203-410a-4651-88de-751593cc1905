import { Any } from '@/types'

interface BaseTreeNode {
  id: number | bigint | string
  parentId: number | bigint | string | null
  [key: string]: Any
}

// 定义树形节点类型（包含 children 属性）
interface LikeTreeNode extends BaseTreeNode {
  children?: LikeTreeNode[]
}

/**
 * 数组转树形结构函数（支持泛型扩展）
 * @param array 输入数组，元素需包含 id、parentId、name 属性
 * @param rootParentId 根节点的 parentId 值（通常为 null 或 0）
 * @returns 树形结构数组
 */
export function arrayToTree<T extends BaseTreeNode>(array: T[], rootParentId: T['parentId']): LikeTreeNode[] {
  const tree: LikeTreeNode[] = []
  const itemMap = new Map<T['id'], LikeTreeNode>()

  array.forEach((item) => {
    const node: LikeTreeNode = { ...item, children: [] }
    itemMap.set(item.id, node)
  })

  array.forEach((item) => {
    const node = itemMap.get(item.id)!
    if (item.parentId === rootParentId) {
      tree.push(node)
    } else {
      const parent = itemMap.get(item.parentId!)
      parent?.children?.push(node)
    }
  })

  return tree
}
