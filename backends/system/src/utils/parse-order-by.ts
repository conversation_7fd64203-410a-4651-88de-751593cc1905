/**
 * 解析 orderBy 字符串参数为 Prisma 可用的对象格式
 * @param orderByStr 排序字符串，格式：field:asc 或 field:desc，多个字段用逗号分隔
 * @param defaultOrderBy 默认排序
 * @returns Prisma orderBy 对象
 *
 * @example
 * parseOrderBy('name:asc,createdAt:desc')
 * // 返回: { name: 'asc', createdAt: 'desc' }
 *
 * parseOrderBy('name:asc')
 * // 返回: { name: 'asc' }
 *
 * parseOrderBy('')
 * // 返回: defaultOrderBy
 */
export function parseOrderBy(
  orderByStr?: string,
  defaultOrderBy: Record<string, 'asc' | 'desc'> = { createdAt: 'desc' }
): Record<string, 'asc' | 'desc'> {
  if (!orderByStr || orderByStr.trim() === '') {
    return defaultOrderBy
  }

  try {
    const orderByObj: Record<string, 'asc' | 'desc'> = {}

    const pairs = orderByStr
      .split(',')
      .map((pair) => pair.trim())
      .filter(Boolean)

    for (const pair of pairs) {
      const [field, direction] = pair.split(':').map((part) => part.trim())

      if (field && (direction === 'asc' || direction === 'desc')) {
        orderByObj[field] = direction
      }
    }

    // 如果解析后没有有效的排序字段，返回默认排序
    return Object.keys(orderByObj).length > 0 ? orderByObj : defaultOrderBy
  } catch (error) {
    // 解析失败时返回默认排序
    return defaultOrderBy
  }
}
