import { Context } from 'hono'

// 操作者信息
export interface Operator {
  account: string
  name: string
}

// 从 Hono 上下文中获取操作者信息
export function getOperator(c: Context): Operator {
  const user = c.get('user')
  if (!user) {
    throw new Error('Unauthorized: User not found in context')
  }

  return {
    account: user.account || user.id, // 如果 account 为空，使用 id 作为备选
    name: user.name || 'Unknown',
  }
}

// 安全获取用户信息的辅助函数
export function getUser(c: Context) {
  const user = c.get('user')
  if (!user) {
    throw new Error('Unauthorized: User not found in context')
  }
  return user
}
