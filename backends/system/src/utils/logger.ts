// 日志分割默认参数
import { assign } from 'radash'
import { createLogger as createWinstonLogger, format, Logger, LoggerOptions, transports } from 'winston'
import 'winston-daily-rotate-file'

import { env } from '@/env'
import { getAppContext } from './get-app-context'

const dailyRotateDefaultOptions = {
  datePattern: 'YYYY-MM-DD',
  zippedArchive: true,
  maxSize: '20m',
  maxFiles: '14d',
}

const traceRequestId = format((info) => {
  const ctx = getAppContext()
  info.requestId = ctx?.get('requestId')
  return info
})

const traceUserAccount = format((info) => {
  const ctx = getAppContext()
  info.account = ctx?.get('user')?.account
  return info
})

// 日志输出格式化
function customFormat(showColor = false) {
  return format.combine(
    showColor
      ? format.colorize({ level: true, message: true, colors: { info: 'green', error: 'red', warn: 'yellow' } })
      : format.uncolorize(),
    traceRequestId(),
    traceUserAccount(),
    format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:SSS' }),
    format.json(),
    format.printf((info) => {
      const { timestamp, level, message, requestId, account, ...rest } = info
      const restString = Object.keys(rest).length ? JSON.stringify(rest) : ''
      return `[${timestamp}][${requestId || '-'}][${account || '-'}][${process.pid}][${level}][-]: ${message} ${restString}`
    })
  )
}

export function createLogger(namespace?: string, options: LoggerOptions = {}): Logger {
  // winston 日志配置
  const winstonLoggerConfig: LoggerOptions = {
    level: env.NODE_ENV === 'development' ? 'debug' : 'info',
    exitOnError: false,
    format: customFormat(),
    transports: [
      new transports.Console({ format: customFormat(true) }),
      new transports.DailyRotateFile({
        filename: namespace ? `logs/${namespace}/info/%DATE%.log` : 'logs/info/%DATE%.log',
        level: 'info',
        ...dailyRotateDefaultOptions,
      }),
      new transports.DailyRotateFile({
        filename: namespace ? `logs/${namespace}/error/%DATE%.log` : 'logs/error/%DATE%.log',
        level: 'error',
        ...dailyRotateDefaultOptions,
      }),
    ],
  }
  return createWinstonLogger(assign(winstonLoggerConfig, options))
}

const logger = createLogger()

export default logger
