import { serve } from '@hono/node-server'
import { Hono } from 'hono'
import 'zod-openapi/extend'

import { env } from '@/env'
import logger from '@/utils/logger'

import { registerOpenAPI } from '@/core/openapi'
import { registerHttpMiddleware } from '@/middlewares'
import { registerRoutes } from '@/routes'
import { AppContext } from '@/types'
import pkg from '../package.json'

export function bootstrap() {
  logger.info(`${pkg.name} Server run start...`)
  const app = new Hono<AppContext>({
    strict: true,
  })

  registerHttpMiddleware(app)

  registerRoutes(app)

  registerOpenAPI(app)

  serve({ fetch: app.fetch, port: env.PORT }, (info) => {
    logger.info(`${pkg.name} Server is running on http://localhost:${info.port}`)
  })
}
