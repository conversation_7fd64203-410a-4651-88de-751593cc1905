import { z } from 'zod'

const DIGITAL_REGEX = /^(-|\+)?\d+(\.\d+)?$/

const INT_REGEX = /^\d+$/

export const DigitalSchema: z.ZodType<string | number> = z
  .union([z.string().regex(DIGITAL_REGEX), z.number()])
  .transform((v) => (typeof v === 'string' ? Number(v) : v))

export type DigitalType = z.infer<typeof DigitalSchema>

export const IntSchema: z.ZodType<string | number> = z
  .union([z.string().regex(INT_REGEX), z.number().int()])
  .transform((v) => (typeof v === 'string' ? Number(v) : v))

export type IntType = z.infer<typeof IntSchema>
