import { z } from 'zod'

export const PageableQuerySchema = z.object({
  current: z.coerce.number().int().positive().describe('当前页').optional(),
  size: z.coerce.number().int().min(1).max(1000).describe('每页数量').optional(),
  orderBy: z.string().describe('排序字段，格式：field:asc 或 field:desc，多个字段用逗号分隔').optional(),
})

export const PageableResponseSchema = PageableQuerySchema.extend({
  total: z.number().int().describe('总数'),
})

export type PageableQuery = z.infer<typeof PageableQuerySchema>
export type PageableResponse = z.infer<typeof PageableResponseSchema>
