import { z } from 'zod'

export const CreatorAndUpdaterSchema = z.object({
  createdBy: z.string().describe('创建人'),
  creatorName: z.string().describe('创建人名称'),
  updatedBy: z.string().describe('更新人'),
  updaterName: z.string().describe('更新人名称'),
})

export const CreatedAtAndUpdatedAtQuerySchema = z.object({
  createdAtStart: z.coerce.number().int().positive().describe('创建时间起始').optional(),
  createdAtEnd: z.coerce.number().int().positive().describe('创建时间结束').optional(),
  updatedAtStart: z.coerce.number().int().positive().describe('更新时间起始').optional(),
  updatedAtEnd: z.coerce.number().int().positive().describe('更新时间结束').optional(),
})

export const CreatedAtAndUpdatedAtResponseSchema = z.object({
  createdAt: z.coerce.number().int().positive().describe('创建时间'),
  updatedAt: z.coerce.number().int().positive().describe('更新时间'),
})
