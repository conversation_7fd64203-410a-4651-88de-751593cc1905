import { z } from 'zod'

/**
 * Base Error Schema
 */
export const BaseErrorSchema = z
  .object({
    code: z.number().openapi({ example: 500 }),
    message: z.string().openapi({ example: 'Internal Server Error' }),
    data: z.null().openapi({ example: null }),
  })
  .openapi({ ref: 'BaseErrorResponse' })

export function createErrorResponseSchema(name: string, code: number, message: string = 'Server Error') {
  return BaseErrorSchema.extend({
    code: z.literal(code).openapi({ example: code }),
    message: z.string().default(message).openapi({ example: 'Internal Server Error' }),
  }).openapi({ ref: name })
}

export const BadRequestErrorSchema = createErrorResponseSchema('BadRequestError', 400)

export const UnauthorizedErrorSchema = createErrorResponseSchema('UnauthorizedError', 401)

export const ForbiddenErrorSchema = createErrorResponseSchema('ForbiddenError', 403)

export const NotFoundErrorSchema = createErrorResponseSchema('NotFoundError', 404)

export const InternalServerErrorSchema = createErrorResponseSchema('InternalServerError', 500)
