import { z } from 'zod'

import { JsonValueSchema, MicroAppSchema as OriginalMicroAppSchema } from '@runtime/zod'
import { CreatedAtAndUpdatedAtQuerySchema, PageableQuerySchema, PageableResponseSchema } from './common'
import { RemoveBaseFields } from './common/base'

// 避免 circularly referenced 问题
const MicroAppSchema = OriginalMicroAppSchema.extend({
  config: JsonValueSchema,
})

export const MicroAppResponseSchema = MicroAppSchema.omit({
  deletedAt: true,
}).openapi({ ref: 'MicroAppResponse' })

export const CreateMicroAppSchema = MicroAppSchema.extend({
  config: JsonValueSchema,
})
  .omit(RemoveBaseFields)
  .openapi({ ref: 'CreateMicroApp' })

export const UpdateMicroAppSchema = CreateMicroAppSchema.partial().openapi({ ref: 'UpdateMicroApp' })

export const MicroAppQuerySchema = MicroAppSchema.pick({
  id: true,
  name: true,
  code: true,
  authType: true,
  enabled: true,
})
  .partial()
  .openapi({ ref: 'MicroAppQuery' })

export const MicroAppPageQuerySchema = MicroAppQuerySchema.merge(PageableQuerySchema)
  .merge(CreatedAtAndUpdatedAtQuerySchema)
  .openapi({
    ref: 'MicroAppPageQuery',
  })

export const MicroAppPageResponseSchema = PageableResponseSchema.extend({
  records: z.array(MicroAppResponseSchema),
}).openapi({ ref: 'MicroAppPageResponse' })

export type CreateMicroApp = z.infer<typeof CreateMicroAppSchema>
export type UpdateMicroApp = z.infer<typeof UpdateMicroAppSchema>
export type MicroAppQuery = z.infer<typeof MicroAppQuerySchema>
export type MicroAppPageQuery = z.infer<typeof MicroAppPageQuerySchema>
export type MicroAppPageResponse = z.infer<typeof MicroAppPageResponseSchema>
export type MicroAppResponse = z.infer<typeof MicroAppResponseSchema>
