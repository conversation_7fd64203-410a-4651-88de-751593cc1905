import { RoleSchema } from '@runtime/zod'
import { z } from 'zod'
import { DepartmentResponseSchema } from './department.schema'

// ==================== 用户有效角色 Schema ====================
export const UserEffectiveRolesSchema = z
  .object({
    globalRoles: z.array(RoleSchema),
    departmentRoles: z.array(
      z.object({
        departmentId: z.string(),
        departmentName: z.string(),
        roles: z.array(RoleSchema),
      })
    ),
  })
  .openapi({ ref: 'UserEffectiveRoles' })

// ==================== 用户权限响应 Schema ====================
export const UserPermissionsResponseSchema = z
  .object({
    userId: z.string(),
    globalRoles: z.array(RoleSchema),
    departmentRoles: z.array(
      z.object({
        departmentId: z.string(),
        departmentName: z.string(),
        roles: z.array(RoleSchema),
      })
    ),
    permissions: z.array(z.any()), // 可以引入 PermissionSchema
    dataScope: z.array(z.string()),
  })
  .openapi({ ref: 'UserPermissionsResponse' })

// ==================== 权限检查请求 Schema ====================
export const CheckPermissionSchema = z
  .object({
    permission: z.string().min(1, '权限编码不能为空'),
  })
  .openapi({ ref: 'CheckPermission' })

export const CheckPermissionsSchema = z
  .object({
    permissions: z.array(z.string()).min(1, '权限列表不能为空'),
    mode: z.enum(['ALL', 'ANY']).default('ALL').describe('检查模式：ALL-全部权限，ANY-任一权限'),
  })
  .openapi({ ref: 'CheckPermissions' })

export const CheckRoleSchema = z
  .object({
    role: z.string().min(1, '角色编码不能为空'),
    scope: z
      .enum(['GLOBAL', 'DEPARTMENT', 'ANY'])
      .optional()
      .describe('角色范围：GLOBAL-全局角色，DEPARTMENT-部门角色，ANY-任意'),
  })
  .openapi({ ref: 'CheckRole' })

export const CheckMenuAccessSchema = z
  .object({
    menuPath: z.string().min(1, '菜单路径不能为空'),
  })
  .openapi({ ref: 'CheckMenuAccess' })

export const CheckDataPermissionSchema = z
  .object({
    resource: z.string().min(1, '资源类型不能为空'),
    action: z.string().min(1, '操作类型不能为空'),
    context: z
      .object({
        departmentId: z.string().optional(),
        userId: z.string().optional(),
      })
      .optional(),
  })
  .openapi({ ref: 'CheckDataPermission' })

// ==================== 权限检查响应 Schema ====================
export const PermissionCheckResultSchema = z
  .object({
    hasPermission: z.boolean().describe('是否拥有权限'),
  })
  .openapi({ ref: 'PermissionCheckResult' })

export const PermissionsCheckResultSchema = z
  .object({
    hasPermission: z.boolean().describe('是否拥有权限'),
    results: z.record(z.string(), z.boolean()).describe('各权限检查结果'),
  })
  .openapi({ ref: 'PermissionsCheckResult' })

export const RoleCheckResultSchema = z
  .object({
    hasRole: z.boolean().describe('是否拥有角色'),
  })
  .openapi({ ref: 'RoleCheckResult' })

export const MenuAccessCheckResultSchema = z
  .object({
    hasAccess: z.boolean().describe('是否有访问权限'),
  })
  .openapi({ ref: 'MenuAccessCheckResult' })

export const DataPermissionCheckResultSchema = z
  .object({
    hasPermission: z.boolean().describe('是否有数据权限'),
    dataScope: z.array(z.string()).describe('数据权限范围'),
  })
  .openapi({ ref: 'DataPermissionCheckResult' })

// ==================== 用户部门信息响应 Schema ====================
export const UserDepartmentsResponseSchema = z
  .object({
    departments: z.array(DepartmentResponseSchema).describe('所属部门列表'),
    manageableDepartments: z.array(DepartmentResponseSchema).describe('可管理部门列表'),
  })
  .openapi({ ref: 'UserDepartmentsResponse' })

// ==================== 用户菜单树节点 Schema ====================
export const MenuTreeNodeSchema = z
  .object({
    id: z.string(),
    name: z.string(),
    path: z.string(),
    icon: z.string().nullable(),
    type: z.number().int(),
    target: z.number().int(),
    sort: z.number().int().nullable(),
    hiddenInMenu: z.number().int(),
    enabled: z.number().int(),
    parentId: z.string().nullable(),
    microApp: z
      .object({
        id: z.string(),
        name: z.string(),
        code: z.string(),
        url: z.string(),
      })
      .optional(),
    children: z.array(z.any()).optional().describe('子菜单列表'),
  })
  .openapi({ ref: 'MenuTreeNode' })

export const UserMenusResponseSchema = z
  .object({
    menus: z.array(MenuTreeNodeSchema).describe('用户可访问的菜单树'),
  })
  .openapi({ ref: 'UserMenusResponse' })

// ==================== 用户数据权限范围响应 Schema ====================
export const UserDataScopeResponseSchema = z
  .object({
    dataScope: z.array(z.string()).describe('数据权限范围'),
    accessibleDepartments: z.array(z.string()).describe('可访问的部门ID列表'),
    accessibleUsers: z.array(z.string()).describe('可访问的用户ID列表'),
  })
  .openapi({ ref: 'UserDataScopeResponse' })

// ==================== 批量权限检查 Schema ====================
export const BatchPermissionCheckSchema = z
  .object({
    checks: z
      .array(
        z.object({
          permission: z.string(),
          context: z
            .object({
              departmentId: z.string().optional(),
              userId: z.string().optional(),
            })
            .optional(),
        })
      )
      .min(1, '检查列表不能为空'),
  })
  .openapi({ ref: 'BatchPermissionCheck' })

export const BatchPermissionCheckResultSchema = z
  .object({
    results: z.record(z.string(), z.boolean()).describe('权限检查结果映射'),
  })
  .openapi({ ref: 'BatchPermissionCheckResult' })

// ==================== 用户角色管理 Schema ====================
export const AssignUserRolesSchema = z
  .object({
    roleIds: z.array(z.string()).min(1, '至少选择一个角色'),
  })
  .openapi({ ref: 'AssignUserRoles' })

export const AssignUserDepartmentRolesSchema = z
  .object({
    departmentId: z.string().min(1, '部门ID不能为空'),
    roleIds: z.array(z.string()).min(1, '至少选择一个角色'),
  })
  .openapi({ ref: 'AssignUserDepartmentRoles' })

// ==================== 权限检查上下文 Schema ====================
export const PermissionCheckContextSchema = z
  .object({
    departmentId: z.string().optional(),
    userId: z.string().optional(),
    req: z.any().optional(), // Request 对象
  })
  .openapi({ ref: 'PermissionCheckContext' })

// ==================== 审计字段 Schema ====================
export const AuditFieldsSchema = z
  .object({
    createdBy: z.string(),
    creatorName: z.string(),
    updatedBy: z.string(),
    updaterName: z.string(),
  })
  .openapi({ ref: 'AuditFields' })

// ==================== 缓存选项 Schema ====================
export const CacheOptionsSchema = z
  .object({
    ttl: z.number().int().positive().optional(), // 缓存时间（秒）
  })
  .openapi({ ref: 'CacheOptions' })

// ==================== 分页结果 Schema ====================
export const PaginatedResultSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    data: z.array(dataSchema),
    total: z.number().int().nonnegative(),
    page: z.number().int().positive(),
    pageSize: z.number().int().positive(),
    totalPages: z.number().int().nonnegative(),
  })

// ==================== 类型导出 ====================
export type CheckPermission = z.infer<typeof CheckPermissionSchema>
export type CheckPermissions = z.infer<typeof CheckPermissionsSchema>
export type CheckRole = z.infer<typeof CheckRoleSchema>
export type CheckMenuAccess = z.infer<typeof CheckMenuAccessSchema>
export type CheckDataPermission = z.infer<typeof CheckDataPermissionSchema>
export type PermissionCheckResult = z.infer<typeof PermissionCheckResultSchema>
export type PermissionsCheckResult = z.infer<typeof PermissionsCheckResultSchema>
export type RoleCheckResult = z.infer<typeof RoleCheckResultSchema>
export type MenuAccessCheckResult = z.infer<typeof MenuAccessCheckResultSchema>
export type DataPermissionCheckResult = z.infer<typeof DataPermissionCheckResultSchema>
export type UserDepartmentsResponse = z.infer<typeof UserDepartmentsResponseSchema>
export type MenuTreeNode = z.infer<typeof MenuTreeNodeSchema>
export type UserMenusResponse = z.infer<typeof UserMenusResponseSchema>
export type UserDataScopeResponse = z.infer<typeof UserDataScopeResponseSchema>
export type BatchPermissionCheck = z.infer<typeof BatchPermissionCheckSchema>
export type BatchPermissionCheckResult = z.infer<typeof BatchPermissionCheckResultSchema>
export type AssignUserRoles = z.infer<typeof AssignUserRolesSchema>
export type AssignUserDepartmentRoles = z.infer<typeof AssignUserDepartmentRolesSchema>
export type PermissionCheckContext = z.infer<typeof PermissionCheckContextSchema>
export type AuditFields = z.infer<typeof AuditFieldsSchema>
export type CacheOptions = z.infer<typeof CacheOptionsSchema>
export type UserEffectiveRoles = z.infer<typeof UserEffectiveRolesSchema>
export type UserPermissionsResponse = z.infer<typeof UserPermissionsResponseSchema>

// ==================== 类型别名 ====================
export type PermissionCheckMode = 'ALL' | 'ANY'
export type RoleScope = 'GLOBAL' | 'DEPARTMENT' | 'ANY'
export type PaginatedResult<T> = {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}
