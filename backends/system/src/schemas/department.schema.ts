import { DepartmentSchema } from '@runtime/zod'
import { z } from 'zod'
import { CreatedAtAndUpdatedAtQuerySchema, PageableQuerySchema, PageableResponseSchema } from './common'
import { RemoveBaseFields } from './common/base'

// ==================== 创建部门 Schema ====================
export const CreateDepartmentSchema = DepartmentSchema.extend({
  roleIds: z.array(z.string()).optional().describe('部门基础角色'),
})
  .omit(RemoveBaseFields)
  .openapi({ ref: 'CreateDepartment' })

// ==================== 更新部门 Schema ====================
export const UpdateDepartmentSchema = CreateDepartmentSchema.partial().openapi({ ref: 'UpdateDepartment' })

// ==================== 查询部门 Schema ====================
export const DepartmentQuerySchema = DepartmentSchema.pick({
  id: true,
  name: true,
  code: true,
  parentId: true,
  enabled: true,
})
  .partial()
  .openapi({ ref: 'DepartmentQuery' })

export const DepartmentPageQuerySchema = DepartmentQuerySchema.merge(PageableQuerySchema)
  .merge(CreatedAtAndUpdatedAtQuerySchema)
  .openapi({ ref: 'DepartmentPageQuery' })

// ==================== 响应 Schema ====================
export const DepartmentResponseSchema = DepartmentSchema.omit({
  deletedAt: true,
}).openapi({ ref: 'DepartmentResponse' })

export const DepartmentPageResponseSchema = PageableResponseSchema.extend({
  records: z.array(DepartmentResponseSchema),
}).openapi({ ref: 'DepartmentPageResponse' })

// ==================== 部门树 Schema ====================
export type DepartmentTreeResponse = z.infer<typeof DepartmentResponseSchema> & {
  children?: DepartmentTreeResponse[]
}

export const DepartmentTreeResponseSchema: z.ZodType<DepartmentTreeResponse> = z.lazy(() =>
  DepartmentResponseSchema.extend({
    children: z.array(DepartmentTreeResponseSchema).optional(),
  }).openapi({ ref: 'DepartmentTreeResponse' })
)

// ==================== 部门成员管理 Schema ====================
export const AddDepartmentMemberSchema = z
  .object({
    userIds: z.array(z.string()).min(1, '至少选择一个用户'),
    type: z.enum(['OWNER', 'MANAGER', 'MEMBER']).default('MEMBER'),
  })
  .openapi({ ref: 'AddDepartmentMember' })

export const UpdateDepartmentMemberSchema = z
  .object({
    type: z.enum(['OWNER', 'MANAGER', 'MEMBER']),
  })
  .openapi({ ref: 'UpdateDepartmentMember' })

export const AssignDepartmentRolesSchema = z
  .object({
    userId: z.string(),
    roleIds: z.array(z.string()).min(1, '至少选择一个角色'),
  })
  .openapi({ ref: 'AssignDepartmentRoles' })

// ==================== 部门成员类型枚举 ====================
export enum DepartmentMemberType {
  OWNER = 'OWNER',
  MANAGER = 'MANAGER',
  MEMBER = 'MEMBER',
}

// ==================== 类型导出 ====================
export type CreateDepartment = z.infer<typeof CreateDepartmentSchema>
export type UpdateDepartment = z.infer<typeof UpdateDepartmentSchema>
export type DepartmentQuery = z.infer<typeof DepartmentQuerySchema>
export type DepartmentPageQuery = z.infer<typeof DepartmentPageQuerySchema>
export type DepartmentResponse = z.infer<typeof DepartmentResponseSchema>
export type DepartmentPageResponse = z.infer<typeof DepartmentPageResponseSchema>
export type AddDepartmentMember = z.infer<typeof AddDepartmentMemberSchema>
export type UpdateDepartmentMember = z.infer<typeof UpdateDepartmentMemberSchema>
export type AssignDepartmentRoles = z.infer<typeof AssignDepartmentRolesSchema>
