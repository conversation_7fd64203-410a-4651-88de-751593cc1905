import { RoleSchema } from '@runtime/zod'
import { z } from 'zod'
import { CreatedAtAndUpdatedAtQuerySchema, PageableQuerySchema, PageableResponseSchema } from './common'
import { RemoveBaseFields } from './common/base'

// ==================== 创建角色 Schema ====================
export const CreateRoleSchema = RoleSchema.extend({
  permissionIds: z.array(z.string()).optional().describe('权限ID列表'),
  menuIds: z.array(z.string()).optional().describe('菜单ID列表'),
})
  .omit(RemoveBaseFields)
  .openapi({ ref: 'CreateRole' })

// ==================== 更新角色 Schema ====================
export const UpdateRoleSchema = CreateRoleSchema.partial().openapi({ ref: 'UpdateRole' })

// ==================== 查询角色 Schema ====================
export const RoleQuerySchema = RoleSchema.pick({
  id: true,
  code: true,
  name: true,
  type: true,
  parentId: true,
  enabled: true,
})
  .partial()
  .openapi({ ref: 'RoleQuery' })

export const RolePageQuerySchema = RoleQuerySchema.merge(PageableQuerySchema)
  .merge(CreatedAtAndUpdatedAtQuerySchema)
  .openapi({ ref: 'RolePageQuery' })

// ==================== 响应 Schema ====================
export const RoleResponseSchema = RoleSchema.omit({
  deletedAt: true,
}).openapi({ ref: 'RoleResponse' })

export const RolePageResponseSchema = PageableResponseSchema.extend({
  records: z.array(RoleResponseSchema),
}).openapi({ ref: 'RolePageResponse' })

// ==================== 角色树 Schema ====================
export type RoleTreeResponse = z.infer<typeof RoleResponseSchema> & {
  children?: RoleTreeResponse[]
}

export const RoleTreeResponseSchema: z.ZodType<RoleTreeResponse> = z.lazy(() =>
  RoleResponseSchema.extend({
    children: z.array(RoleTreeResponseSchema).optional(),
  }).openapi({ ref: 'RoleTreeResponse' })
)

// ==================== 角色权限分配 Schema ====================
export const AssignRolePermissionsSchema = z
  .object({
    permissionIds: z.array(z.string()).min(1, '至少选择一个权限'),
  })
  .openapi({ ref: 'AssignRolePermissions' })

// ==================== 角色菜单分配 Schema ====================
export const AssignRoleMenusSchema = z
  .object({
    menuIds: z.array(z.string()).min(1, '至少选择一个菜单'),
  })
  .openapi({ ref: 'AssignRoleMenus' })

// ==================== 数据权限范围枚举 ====================
export enum DataScopeEnum {
  ALL = 0, // 全部数据权限
  DEPARTMENT = 1, // 本部门数据权限
  SELF = 2, // 仅个人数据权限
  SUBORDINATE = 3, // 下级部门数据权限
}

export const DATA_SCOPE_PERMISSIONS = {
  [DataScopeEnum.ALL]: 'data:scope:all',
  [DataScopeEnum.DEPARTMENT]: 'data:scope:department',
  [DataScopeEnum.SELF]: 'data:scope:self',
  [DataScopeEnum.SUBORDINATE]: 'data:scope:subordinate',
} as const

// ==================== 系统预定义角色 ====================
export const SYSTEM_ROLES = {
  SUPER_ADMIN: 'SUPER_ADMIN',
  DEPT_ADMIN: 'DEPT_ADMIN',
  SUB_DEPT_ADMIN: 'SUB_DEPT_ADMIN',
  BUSINESS_ADMIN: 'BUSINESS_ADMIN',
  DEVELOPER: 'DEVELOPER',
  TESTER: 'TESTER',
  NORMAL_USER: 'NORMAL_USER',
} as const

export type SystemRoleCode = (typeof SYSTEM_ROLES)[keyof typeof SYSTEM_ROLES]

// ==================== 类型导出 ====================
export type CreateRole = z.infer<typeof CreateRoleSchema>
export type UpdateRole = z.infer<typeof UpdateRoleSchema>
export type RoleQuery = z.infer<typeof RoleQuerySchema>
export type RolePageQuery = z.infer<typeof RolePageQuerySchema>
export type RoleResponse = z.infer<typeof RoleResponseSchema>
export type RolePageResponse = z.infer<typeof RolePageResponseSchema>
export type AssignRolePermissions = z.infer<typeof AssignRolePermissionsSchema>
export type AssignRoleMenus = z.infer<typeof AssignRoleMenusSchema>
