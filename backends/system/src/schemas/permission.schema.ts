import { PermissionSchema } from '@runtime/zod'
import { z } from 'zod'
import { CreatedAtAndUpdatedAtQuerySchema, PageableQuerySchema, PageableResponseSchema } from './common'
import { RemoveBaseFields } from './common/base'

// ==================== 创建权限 Schema ====================
export const CreatePermissionSchema = PermissionSchema.omit(RemoveBaseFields).openapi({ ref: 'CreatePermission' })

// ==================== 更新权限 Schema ====================
export const UpdatePermissionSchema = CreatePermissionSchema.partial().openapi({ ref: 'UpdatePermission' })

// ==================== 查询权限 Schema ====================
export const PermissionQuerySchema = PermissionSchema.pick({
  id: true,
  code: true,
  name: true,
  category: true,
  resource: true,
  action: true,
})
  .partial()
  .openapi({ ref: 'PermissionQuery' })

export const PermissionPageQuerySchema = PermissionQuerySchema.merge(PageableQuerySchema)
  .merge(CreatedAtAndUpdatedAtQuerySchema)
  .openapi({ ref: 'PermissionPageQuery' })

// ==================== 响应 Schema ====================
export const PermissionResponseSchema = PermissionSchema.omit({
  deletedAt: true,
}).openapi({ ref: 'PermissionResponse' })

export const PermissionPageResponseSchema = PageableResponseSchema.extend({
  records: z.array(PermissionResponseSchema),
}).openapi({ ref: 'PermissionPageResponse' })

// ==================== 权限树 Schema ====================
export type PermissionTreeResponse = z.infer<typeof PermissionResponseSchema> & {
  children?: PermissionTreeResponse[]
}

export const PermissionTreeResponseSchema: z.ZodType<PermissionTreeResponse> = z.lazy(() =>
  PermissionResponseSchema.extend({
    children: z.array(PermissionTreeResponseSchema).optional(),
  }).openapi({ ref: 'PermissionTreeResponse' })
)

// ==================== 权限分类 Schema ====================
export const PermissionCategorySchema = z
  .object({
    category: z.string(),
    displayName: z.string(),
    permissions: z.array(PermissionResponseSchema),
  })
  .openapi({ ref: 'PermissionCategory' })

// ==================== 权限常量 ====================
export const PERMISSIONS = {
  // 系统管理权限
  SYSTEM_USER_LIST: 'system:user:list',
  SYSTEM_USER_CREATE: 'system:user:create',
  SYSTEM_USER_UPDATE: 'system:user:update',
  SYSTEM_USER_DELETE: 'system:user:delete',
  SYSTEM_USER_ALL: 'system:user:*',

  SYSTEM_ROLE_LIST: 'system:role:list',
  SYSTEM_ROLE_CREATE: 'system:role:create',
  SYSTEM_ROLE_UPDATE: 'system:role:update',
  SYSTEM_ROLE_DELETE: 'system:role:delete',
  SYSTEM_ROLE_ALL: 'system:role:*',

  SYSTEM_PERMISSION_LIST: 'system:permission:list',
  SYSTEM_PERMISSION_CREATE: 'system:permission:create',
  SYSTEM_PERMISSION_UPDATE: 'system:permission:update',
  SYSTEM_PERMISSION_DELETE: 'system:permission:delete',
  SYSTEM_PERMISSION_ALL: 'system:permission:*',

  SYSTEM_DEPARTMENT_LIST: 'system:department:list',
  SYSTEM_DEPARTMENT_CREATE: 'system:department:create',
  SYSTEM_DEPARTMENT_UPDATE: 'system:department:update',
  SYSTEM_DEPARTMENT_DELETE: 'system:department:delete',
  SYSTEM_DEPARTMENT_ALL: 'system:department:*',

  SYSTEM_MENU_LIST: 'system:menu:list',
  SYSTEM_MENU_CREATE: 'system:menu:create',
  SYSTEM_MENU_UPDATE: 'system:menu:update',
  SYSTEM_MENU_DELETE: 'system:menu:delete',
  SYSTEM_MENU_ALL: 'system:menu:*',

  SYSTEM_ALL: 'system:*',

  // 数据权限
  DATA_SCOPE_ALL: 'data:scope:all',
  DATA_SCOPE_DEPARTMENT: 'data:scope:department',
  DATA_SCOPE_SELF: 'data:scope:self',
  DATA_SCOPE_SUBORDINATE: 'data:scope:subordinate',

  // 业务权限（示例）
  BUSINESS_PROJECT_LIST: 'business:project:list',
  BUSINESS_PROJECT_CREATE: 'business:project:create',
  BUSINESS_PROJECT_UPDATE: 'business:project:update',
  BUSINESS_PROJECT_DELETE: 'business:project:delete',
  BUSINESS_PROJECT_ALL: 'business:project:*',

  BUSINESS_TASK_LIST: 'business:task:list',
  BUSINESS_TASK_CREATE: 'business:task:create',
  BUSINESS_TASK_UPDATE: 'business:task:update',
  BUSINESS_TASK_ASSIGN: 'business:task:assign',
  BUSINESS_TASK_ALL: 'business:task:*',

  BUSINESS_ALL: 'business:*',

  // 超级权限
  SUPER: '*',
} as const

export type PermissionCode = (typeof PERMISSIONS)[keyof typeof PERMISSIONS]

// ==================== 类型导出 ====================
export type CreatePermission = z.infer<typeof CreatePermissionSchema>
export type UpdatePermission = z.infer<typeof UpdatePermissionSchema>
export type PermissionQuery = z.infer<typeof PermissionQuerySchema>
export type PermissionPageQuery = z.infer<typeof PermissionPageQuerySchema>
export type PermissionResponse = z.infer<typeof PermissionResponseSchema>
export type PermissionPageResponse = z.infer<typeof PermissionPageResponseSchema>
export type PermissionCategory = z.infer<typeof PermissionCategorySchema>
