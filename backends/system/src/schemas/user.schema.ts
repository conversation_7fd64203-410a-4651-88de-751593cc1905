import { UserSchema } from '@runtime/zod'
import { z } from 'zod'
import { CreatedAtAndUpdatedAtQuerySchema, PageableQuerySchema, PageableResponseSchema } from './common'
import { RemoveBaseFields } from './common/base'
import { RoleResponseSchema } from './role.schema'

// FIXME: 待删除，Create User 逻辑已在 better-auth 中实现
export const CreateUserSchema = UserSchema.omit(RemoveBaseFields)
  .extend({
    uid: z.string().min(1, '用户ID不能为空'),
    account: z.string().min(1, '账号不能为空'),
    email: z.string().email('邮箱格式不正确'),
    name: z.string().min(1, '姓名不能为空'),
    gender: z.number().int().min(0).max(1),
    simUid: z.string().optional(),
    password: z.string().optional(),
    enabled: z.number().int().min(0).max(1).default(1),
    roles: z.array(z.string()).optional(),
    departmentId: z.string().optional(),
  })
  .openapi({ ref: 'CreateUser' })

export const UpdateUserSchema = CreateUserSchema.partial()
  .extend({
    id: z.number().int().positive('ID必须是正整数'),
  })
  .openapi({ ref: 'UpdateUser' })

export const UserQuerySchema = UserSchema.pick({
  id: true,
  name: true,
  account: true,
  email: true,
  enabled: true,
  departmentId: true,
})
  .partial()
  .openapi({ ref: 'UserQuery' })

export const UserPageQuerySchema = UserQuerySchema.merge(PageableQuerySchema)
  .merge(CreatedAtAndUpdatedAtQuerySchema)
  .openapi({ ref: 'UserPageQuery' })

export const UserResponseSchema = UserSchema.omit(RemoveBaseFields)
  .extend({
    roles: z.array(RoleResponseSchema.pick({ id: true, name: true, code: true, parentId: true, type: true })),
  })
  .openapi({ ref: 'UserResponse' })

export const UserPageResponseSchema = PageableResponseSchema.extend({
  records: z.array(UserResponseSchema),
}).openapi({ ref: 'UserPageResponse' })

export type CreateUser = z.infer<typeof CreateUserSchema>
export type UpdateUser = z.infer<typeof UpdateUserSchema>
export type UserQuery = z.infer<typeof UserQuerySchema>
export type UserPageQuery = z.infer<typeof UserPageQuerySchema>
export type UserResponse = z.infer<typeof UserResponseSchema>
export type UserPageResponse = z.infer<typeof UserPageResponseSchema>
