import { JsonValueSchema, MenuSchema } from '@runtime/zod'
import { z } from 'zod'
import { CreatedAtAndUpdatedAtQuerySchema, PageableQuerySchema, PageableResponseSchema } from './common'
import { RemoveBaseFields } from './common/base'

export const CreateMenuSchema = MenuSchema.extend({
  config: JsonValueSchema,
})
  .omit(RemoveBaseFields)
  .openapi({ ref: 'CreateMenu' })

export const UpdateMenuSchema = CreateMenuSchema.partial().openapi({ ref: 'UpdateMenu' })

export const MenuQuerySchema = MenuSchema.pick({
  id: true,
  parentId: true,
  name: true,
  icon: true,
  type: true,
  path: true,
  target: true,
  query: true,
  sort: true,
  hiddenInMenu: true,
  enabled: true,
})
  .partial()
  .openapi({ ref: 'MenuQuery' })

export const MenuPageQuerySchema = MenuQuerySchema.merge(PageableQuerySchema).merge(CreatedAtAndUpdatedAtQuerySchema).openapi({
  ref: 'MenuPageQuery',
})

export const MenuResponseSchema = MenuSchema.omit({
  deletedAt: true,
}).openapi({ ref: 'MenuResponse' })

export const MenuPageResponseSchema = PageableResponseSchema.extend({
  records: z.array(MenuResponseSchema),
}).openapi({ ref: 'MenuPageResponse' })

export type CreateMenu = z.infer<typeof CreateMenuSchema>
export type UpdateMenu = z.infer<typeof UpdateMenuSchema>
export type MenuQuery = z.infer<typeof MenuQuerySchema>
export type MenuPageQuery = z.infer<typeof MenuPageQuerySchema>
export type MenuResponseWithChildren = z.infer<typeof MenuResponseSchema> & { children: MenuResponseWithChildren[] }
export type MenuResponse = z.infer<typeof MenuResponseSchema>
export type MenuPageResponse = z.infer<typeof MenuPageResponseSchema>
