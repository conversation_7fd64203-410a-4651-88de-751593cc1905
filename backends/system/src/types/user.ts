import { User } from '@prisma/client'

export interface UserWithRoles extends User {
  userRoles: {
    role: {
      code: string
      name: string
      rolePermissions: {
        permission: {
          code: string
          name: string
        }
      }[]
      roleMenus: {
        menu: {
          id: string
          name: string
          path: string
          icon: string | null
          type: number
          target: number
          sort: number
          hiddenInMenu: number
          enabled: number
          parentId: string | null
          treePath: string
          microId: string | null
        }
      }[]
    }
  }[]
  memberships: {
    departmentMemberRoles: {
      role: {
        code: string
        name: string
        rolePermissions: {
          permission: {
            code: string
            name: string
          }
        }[]
        roleMenus: {
          menu: {
            id: string
            name: string
            path: string
            icon: string | null
            type: number
            target: number
            sort: number
            hiddenInMenu: number
            enabled: number
            parentId: string | null
            treePath: string
            microId: string | null
          }
        }[]
      }
    }[]
  }[]
}

declare module 'hono' {
  interface ContextVariableMap {
    user: UserWithRoles | undefined
  }
}
