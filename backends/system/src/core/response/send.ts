import { Context } from 'hono'
import { ContentfulStatusCode } from 'hono/utils/http-status'

import { ApiResponse } from '@/core/response/types'
import { Any } from '@/types'
import { logger } from '@/utils'
import { InternalServerErrorException } from '../exceptions'

/**
 * 创建标准响应对象
 * @param code 状态码
 * @param message 响应消息
 * @param data 响应数据
 * @returns 标准响应对象
 */
export function createResponse<T>(code: number, message: string, data: T): ApiResponse<T> {
  return { code, message, data }
}

export function send<T>(c: Context, response: ApiResponse<T>, status?: ContentfulStatusCode) {
  const respSchema = c.get('respSchema')

  if (respSchema) {
    const { data, error } = respSchema.safeParse(response.data)

    if (error) {
      logger.error('Response data schema validation failed!', { error })
      throw new InternalServerErrorException('Response data schema validation failed!')
    }

    response.data = data
  }

  return c.json(response, status)
}

/**
 * 创建成功响应
 * @param data 响应数据
 * @param message 成功消息，默认为"操作成功"
 * @returns 成功响应对象
 */
export function success<T>(c: Context, data: T, message = 'success') {
  return send(c, createResponse(200, message, data))
}

/**
 * 创建失败响应
 * @param message 错误消息
 * @param code 错误码，默认为500
 * @param data 错误数据，默认为null
 * @returns 失败响应对象
 */
export function fail<T extends Any | null = null>(c: Context, message: string, code = 500, data: T | null = null) {
  return send(c, createResponse(code, message, data))
}
