import { resolver } from 'hono-openapi/zod'
import { z, ZodType, ZodTypeDef } from 'zod'

import {
  BadRequestErrorSchema,
  ForbiddenErrorSchema,
  InternalServerErrorSchema,
  NotFoundErrorSchema,
  UnauthorizedErrorSchema,
} from '@/schemas/error.schema'
import { Any } from '@/types'

export function createStandardResponses<T extends ZodType<Any, ZodTypeDef, Any>>(successSchema: T, summary?: string) {
  return {
    200: {
      description: summary || 'Successful response',
      content: { 'application/json': { schema: resolver(successSchema) } },
    },
    400: {
      description: 'Bad Request',
      content: { 'application/json': { schema: resolver(BadRequestErrorSchema) } },
    },
    401: {
      description: 'Unauthorized',
      content: { 'application/json': { schema: resolver(UnauthorizedErrorSchema) } },
    },
    403: {
      description: 'Forbidden',
      content: { 'application/json': { schema: resolver(ForbiddenErrorSchema) } },
    },
    404: {
      description: 'Not Found',
      content: { 'application/json': { schema: resolver(NotFoundErrorSchema) } },
    },
    500: {
      description: 'Internal Server Error',
      content: { 'application/json': { schema: resolver(InternalServerErrorSchema) } },
    },
  }
}

export function createSuccessResponseSchema<T extends ZodType<Any, ZodTypeDef, Any>>(dataSchema: T) {
  const schema = z.object({
    code: z.number().openapi({ example: 200 }),
    message: z.string().openapi({ example: 'Operation successful' }),
    data: dataSchema.nullable().openapi({ description: 'Response data' }),
  })

  return schema
}

/**
 * 创建分页响应 Schema
 * @param itemSchema 单个项目的 Schema
 * @param refName 引用名称
 */
export function createPageResponseSchema<T extends ZodType<Any, ZodTypeDef, Any>>(itemSchema: T) {
  const schema = z.object({
    total: z.number().int().nonnegative().openapi({ example: 100, description: '总记录数' }),
    page: z.number().int().positive().openapi({ example: 1, description: '当前页码' }),
    size: z.number().int().positive().openapi({ example: 10, description: '每页记录数' }),
    records: z.array(itemSchema).openapi({ description: '列表数据' }),
  })
  return schema
}
