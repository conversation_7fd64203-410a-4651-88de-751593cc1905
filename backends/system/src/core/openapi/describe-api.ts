import { describeRoute } from 'hono-openapi'
import { ZodType, ZodTypeDef } from 'zod'

import { Any } from '@/types'
import { createPageResponseSchema, createStandardResponses, createSuccessResponseSchema } from './describe-api-handle'

interface DescribeUnifiedRouteOptions<TData extends ZodType<Any, ZodTypeDef, Any>> {
  summary: string
  description?: string
  tags?: string[]
  successDataSchema: TData
  bearerAuth?: boolean
}

export function describeUnifiedAPI<TData extends ZodType<Any, ZodTypeDef, Any>>(options: DescribeUnifiedRouteOptions<TData>) {
  // Ensure the data schema itself has a name if a refName is provided
  const namedDataSchema = options.successDataSchema

  // Create the full success response schema using the (potentially named) data schema
  const successSchema = createSuccessResponseSchema(namedDataSchema)

  const responses = createStandardResponses(successSchema, options.summary)

  return describeRoute({
    summary: options.summary,
    description: options.description,
    tags: options.tags,
    responses: responses,
    security: options.bearerAuth ? [{ BearerAuth: [] }] : void 0,
  })
}

/**
 * 分页路由描述的配置选项
 */
interface DescribeUnifiedPageRouteOptions<TItem extends ZodType<Any, ZodTypeDef, Any>> {
  summary: string
  description?: string
  tags?: string[]
  itemSchema: TItem
}

/**
 * 用于分页路由的描述函数
 * 自动创建分页响应格式并集成到统一响应模式
 */
export function describeUnifiedPageAPI<TItem extends ZodType<Any, ZodTypeDef, Any>>(
  options: DescribeUnifiedPageRouteOptions<TItem>
) {
  // 创建分页响应 schema
  const pageSchema = createPageResponseSchema(options.itemSchema)

  return describeUnifiedAPI({
    summary: options.summary,
    description: options.description,
    tags: options.tags,
    successDataSchema: pageSchema,
  })
}
