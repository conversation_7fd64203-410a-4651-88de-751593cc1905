import { swaggerUI } from '@hono/swagger-ui'
import { Env, Hono, Schema } from 'hono'
import { openAPISpecs } from 'hono-openapi'

import { auth } from '@/auth'
import { env } from '@/env'
import { zValidator } from '@hono/zod-validator'
import { Context } from 'hono'
import { BlankEnv, BlankSchema } from 'hono/types'
import { z } from 'zod'

/**
 * Registers OpenAPI specification endpoint and Swagger UI routes.
 * @param app The Hono application instance.
 */
export function registerOpenAPI<E extends Env = BlankEnv, S extends Schema = BlankSchema, BasePath extends string = '/'>(
  app: Hono<E, S, BasePath>
) {
  // Register OpenAPI specification endpoint
  // This should be registered after all other API routes
  app.get(
    '/docs/api/openapi.json',
    openAPISpecs(app, {
      documentation: {
        info: { title: 'Admin System API', version: 'v1', description: 'Admin System API Documentation' },
        servers: [
          { url: `http://localhost:${env.PORT}`, description: 'Local Server' },
          // Add other servers if needed (e.g., production)
          // { url: `https://your-production-url.com`, description: "Production Server" },
        ],
        components: {
          securitySchemes: {
            BearerAuth: { type: 'http', scheme: 'bearer', bearerFormat: 'JWT' },
          },
        },
      },
    })
  )

  app.get('/docs/auth/openapi.json', async (c) => {
    const schema = await auth.api.generateOpenAPISchema()
    return c.json({
      ...schema,
      servers: [
        { url: `http://localhost:${env.PORT}/api/v1/auth`, description: 'Local Server' },
        // Add other servers if needed (e.g., production)
        // { url: `https://your-production-url.com`, description: "Production Server" },
      ],
    })
  })

  // Scalar API 参考文档
  app.get('/docs/:type/scalar', zValidator('param', z.object({ type: z.enum(['api', 'auth']) })), async (c, next) => {
    try {
      const { type } = c.req.valid('param')

      // 使用动态导入 Scalar
      const { Scalar } = await import('@scalar/hono-api-reference')
      const scalarHandler = Scalar({
        pageTitle: 'Scalar API Reference',
        url: `/docs/${type}/openapi.json`,
      })

      // 执行 Scalar 返回的处理函数
      return scalarHandler(c as never, next)
    } catch (error) {
      console.error('Error loading Scalar:', error)
      return c.text('Error loading API documentation', 500)
    }
  })

  app.get('/docs/:type/swagger-ui', zValidator('param', z.object({ type: z.enum(['api', 'auth']) })), (c, next) => {
    const { type } = c.req.valid('param')
    return swaggerUI({ url: `/docs/${type}/openapi.json` })(c as unknown as Context<Env, string, Record<string, never>>, next)
  })
}
