import dotenv from '@dotenvx/dotenvx'
import { createEnv } from '@t3-oss/env-core'
import { z } from 'zod'

dotenv.config({ path: ['.env.local', '.env'], ignore: ['MISSING_ENV_FILE'] })

export const env = createEnv({
  server: {
    NODE_ENV: z.enum(['development', 'production', 'test']),
    RUNNING_ENV: z.enum(['dev', 'fat', 'prod']).default(process.env.NODE_ENV === 'development' ? 'dev' : 'prod'),
    PORT: z.coerce.number().default(3000),
    DATABASE_URL: z.string().url().optional(),
    // CVTE OAuth2
    CVTE_OAUTH2_DOMAIN: z.string().url(),
    CVTE_OAUTH2_CLIENT_ID: z.string(),
  },
  runtimeEnv: process.env,
})

export type Env = typeof env
