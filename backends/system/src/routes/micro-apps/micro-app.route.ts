import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'

import { describeUnifiedAPI, describeUnifiedPageAPI } from '@/core/openapi'
import { success } from '@/core/response/send'
import {
  CreateMicroAppSchema,
  MicroAppPageQuerySchema,
  MicroAppPageResponseSchema,
  MicroAppQuerySchema,
  MicroAppResponseSchema,
  UpdateMicroAppSchema,
} from '@/schemas/micro-app.schema'
import { getOperator } from '@/utils'

import { responseResolver } from '@/middlewares'
import { microAppService } from '../../services/micro-app.service'

// /api/v1/micro-apps/*
const app = new Hono()

app.get(
  '/detail/:id',
  describeUnifiedAPI({
    summary: '获取微应用信息',
    description: '根据微应用ID获取详细微应用信息',
    tags: ['微应用管理'],
    successDataSchema: MicroAppResponseSchema,
  }),
  validator('param', z.object({ id: z.string() })),
  responseResolver(MicroAppResponseSchema),
  async (c) => {
    const { id } = c.req.valid('param')

    const role = await microAppService.findById(id)

    return success(c, role)
  }
)

app.get(
  '/list',
  describeUnifiedAPI({
    summary: '获取微应用列表',
    description: '获取系统中微应用列表',
    tags: ['微应用管理'],
    successDataSchema: z.array(MicroAppResponseSchema),
  }),
  validator('query', MicroAppQuerySchema),
  responseResolver(z.array(MicroAppResponseSchema)),
  async (c) => {
    const query = c.req.valid('query')
    const allRoles = await microAppService.findList(query)
    return success(c, allRoles)
  }
)

app.get(
  '/page',
  describeUnifiedPageAPI({
    summary: '分页查询微应用',
    description: '根据条件分页查询微应用列表',
    tags: ['微应用管理'],
    itemSchema: MicroAppPageResponseSchema,
  }),
  validator('query', MicroAppPageQuerySchema),
  responseResolver(MicroAppPageResponseSchema),
  async (c) => {
    const query = c.req.valid('query')
    const result = await microAppService.findPage(query)
    return success(c, result)
  }
)

// 创建菜单
app.post(
  '/create',
  describeUnifiedAPI({
    summary: '创建新微应用',
    description: '创建新微应用并返回微应用信息',
    tags: ['微应用管理'],
    successDataSchema: MicroAppResponseSchema,
  }),
  validator('json', CreateMicroAppSchema),
  responseResolver(MicroAppResponseSchema),
  async (c) => {
    const data = c.req.valid('json')
    const operator = getOperator(c)

    const result = await microAppService.create(data, operator)

    return success(c, result, 'Role created successfully')
  }
)

// 更新菜单
app.put(
  '/detail/:id',
  describeUnifiedAPI({
    summary: '更新微应用信息',
    description: '根据微应用ID更新微应用信息',
    tags: ['微应用管理'],
    successDataSchema: MicroAppResponseSchema,
  }),
  validator('param', z.object({ id: z.string() })),
  validator('json', UpdateMicroAppSchema),
  responseResolver(MicroAppResponseSchema),
  async (c) => {
    const { id } = c.req.valid('param')
    const data = c.req.valid('json')
    const operator = getOperator(c)

    const result = await microAppService.update(id, data, operator)

    return success(c, result, 'Role updated successfully')
  }
)

app.delete(
  '/detail/:id',
  describeUnifiedAPI({
    summary: '删除微应用',
    description: '根据微应用ID删除微应用',
    tags: ['微应用管理'],
    successDataSchema: z.string(),
  }),
  validator('param', z.object({ id: z.string() })),
  responseResolver(z.string()),
  async (c) => {
    const { id } = c.req.valid('param')
    const operator = getOperator(c)

    await microAppService.delete(id, operator)

    return success(c, id, 'Role deleted successfully')
  }
)

export default app
