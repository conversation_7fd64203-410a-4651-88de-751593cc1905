import { Env, Hono } from 'hono'
import { showRoutes } from 'hono/dev'
import { BlankEnv, BlankSchema, Schema } from 'hono/types'

import { authRoute } from './auth'
import { faviconRoute, healthRoute } from './common'
import { departmentRoute } from './departments'
import { menuRoute } from './menus'
import { microAppRoute } from './micro-apps'
import { permissionRoute } from './permissions'
import { roleRoute } from './roles'
import { userRoute } from './users'

export function registerRoutes<E extends Env = BlankEnv, S extends Schema = BlankSchema, BasePath extends string = '/'>(
  app: Hono<E, S, BasePath>
) {
  app.route('/favicon.ico', faviconRoute)

  app.route('/api/v1/health', healthRoute)

  app.route('/api/v1/auth', authRoute)

  app.route('/api/v1/users', userRoute)

  app.route('/api/v1/roles', roleRoute)

  app.route('/api/v1/permissions', permissionRoute)

  app.route('/api/v1/menus', menuRoute)

  app.route('/api/v1/departments', departmentRoute)

  app.route('/api/v1/micro-apps', microAppRoute)

  showRoutes(app)
}

const app = new Hono()

export default app
