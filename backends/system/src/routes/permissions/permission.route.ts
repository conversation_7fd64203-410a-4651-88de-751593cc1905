import { describeUnifiedAP<PERSON>, describeUnifiedPageAPI } from '@/core/openapi'
import { success } from '@/core/response/send'
import { responseResolver } from '@/middlewares'
import { requirePermissions } from '@/middlewares/rbac'
import {
  CreatePermissionSchema,
  PermissionPageQuerySchema,
  PermissionPageResponseSchema,
  PermissionQuerySchema,
  PermissionResponseSchema,
  UpdatePermissionSchema,
  type CreatePermission,
} from '@/schemas/permission.schema'
import { permissionService } from '@/services/permission.service'
import { getOperator } from '@/utils'
import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'

// /api/v1/permissions/*
const app = new Hono()

// 创建权限
app.post(
  '/',
  describeUnifiedAPI({
    summary: '创建权限',
    description: '创建新权限并返回权限信息',
    tags: ['权限管理'],
    successDataSchema: PermissionResponseSchema,
  }),
  requirePermissions('system:permission:create'),
  validator('json', CreatePermissionSchema),
  responseResolver(PermissionResponseSchema),
  async (c) => {
    const data = c.req.valid('json')
    const operator = getOperator(c)

    const permissionData: CreatePermission = {
      code: data.code,
      name: data.name,
      description: data.description || null,
      category: data.category || null,
      resource: data.resource || null,
      action: data.action || null,
    }

    const permission = await permissionService.createPermission(permissionData, {
      createdBy: operator.account,
      creatorName: operator.name,
      updatedBy: operator.account,
      updaterName: operator.name,
    })

    return success(c, permission, '创建权限成功')
  }
)

// 获取权限列表（分页）
app.get(
  '/page',
  describeUnifiedPageAPI({
    summary: '分页查询权限',
    description: '根据条件分页查询权限列表',
    tags: ['权限管理'],
    itemSchema: PermissionPageResponseSchema,
  }),
  requirePermissions('system:permission:list'),
  validator('query', PermissionPageQuerySchema),
  responseResolver(PermissionPageResponseSchema),
  async (c) => {
    const query = c.req.valid('query')

    const result = await permissionService.getPermissions({
      page: query.current || 1,
      pageSize: query.size || 10,
      code: query.code,
      name: query.name,
    })

    return success(c, {
      total: result.total,
      records: result.data,
      current: query.current || 1,
      size: query.size || 10,
    })
  }
)

// 获取权限列表（简单列表）
app.get(
  '/list',
  describeUnifiedAPI({
    summary: '获取权限列表',
    description: '获取系统中权限列表',
    tags: ['权限管理'],
    successDataSchema: z.array(PermissionResponseSchema),
  }),
  requirePermissions('system:permission:list'),
  validator('query', PermissionQuerySchema),
  responseResolver(z.array(PermissionResponseSchema)),
  async (c) => {
    const query = c.req.valid('query')

    const result = await permissionService.getPermissions({
      page: 1,
      pageSize: 1000, // 获取所有权限
      code: query.code,
      name: query.name,
    })

    return success(c, result.data)
  }
)

// 获取权限详情
app.get(
  '/detail/:id',
  describeUnifiedAPI({
    summary: '获取权限详情',
    description: '根据权限ID获取详细权限信息',
    tags: ['权限管理'],
    successDataSchema: PermissionResponseSchema,
  }),
  requirePermissions('system:permission:list'),
  validator('param', z.object({ id: z.string() })),
  responseResolver(PermissionResponseSchema),
  async (c) => {
    const { id } = c.req.valid('param')

    const permission = await permissionService.getPermissionById(id)

    return success(c, permission)
  }
)

// 更新权限
app.put(
  '/detail/:id',
  describeUnifiedAPI({
    summary: '更新权限信息',
    description: '根据权限ID更新权限信息',
    tags: ['权限管理'],
    successDataSchema: PermissionResponseSchema,
  }),
  requirePermissions('system:permission:update'),
  validator('param', z.object({ id: z.string() })),
  validator('json', UpdatePermissionSchema),
  responseResolver(PermissionResponseSchema),
  async (c) => {
    const { id } = c.req.valid('param')
    const data = c.req.valid('json')
    const operator = getOperator(c)

    const updatedPermission = await permissionService.updatePermission(
      id,
      {
        name: data.name,
      },
      {
        updatedBy: operator.account,
        updaterName: operator.name,
      }
    )

    return success(c, updatedPermission, '更新权限成功')
  }
)

// 删除权限
app.delete(
  '/detail/:id',
  describeUnifiedAPI({
    summary: '删除权限',
    description: '根据权限ID删除权限',
    tags: ['权限管理'],
    successDataSchema: z.string(),
  }),
  requirePermissions('system:permission:delete'),
  validator('param', z.object({ id: z.string() })),
  responseResolver(z.string()),
  async (c) => {
    const { id } = c.req.valid('param')

    await permissionService.deletePermission(id)

    return success(c, id, '删除权限成功')
  }
)

// 获取权限树结构
app.get(
  '/tree',
  describeUnifiedAPI({
    summary: '获取权限树结构',
    description: '获取系统中权限的树形结构',
    tags: ['权限管理'],
    successDataSchema: z.array(z.any()), // 可以定义更具体的树结构schema
  }),
  requirePermissions('system:permission:list'),
  responseResolver(z.array(z.any())),
  async (c) => {
    const permissionTree = await permissionService.getPermissionTree()

    return success(c, permissionTree)
  }
)

// 根据分类获取权限
app.get(
  '/category/:category',
  describeUnifiedAPI({
    summary: '根据分类获取权限',
    description: '根据权限分类获取权限列表',
    tags: ['权限管理'],
    successDataSchema: z.array(PermissionResponseSchema),
  }),
  requirePermissions('system:permission:list'),
  validator('param', z.object({ category: z.string() })),
  responseResolver(z.array(PermissionResponseSchema)),
  async (c) => {
    const { category } = c.req.valid('param')

    const permissions = await permissionService.getPermissionsByCategory(category)

    return success(c, permissions)
  }
)

export default app
