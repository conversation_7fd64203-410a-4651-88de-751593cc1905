import { describeUnifiedAPI, describeUnifiedPageAPI } from '@/core/openapi'
import { success } from '@/core/response/send'
import { responseResolver } from '@/middlewares'
import { requirePermissions } from '@/middlewares/rbac'
import {
  CreateRoleSchema,
  RolePageQuerySchema,
  RolePageResponseSchema,
  RoleQuerySchema,
  RoleResponseSchema,
  UpdateRoleSchema,
} from '@/schemas/role.schema'
import { roleService } from '@/services/role.service'
import { getOperator } from '@/utils'
import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'

// /api/v1/roles/*
const app = new Hono()

// 创建角色
app.post(
  '/',
  describeUnifiedAPI({
    summary: '创建角色',
    description: '创建新角色并返回角色信息',
    tags: ['角色管理'],
    successDataSchema: RoleResponseSchema,
  }),
  requirePermissions('system:role:create'),
  validator('json', CreateRoleSchema),
  responseResolver(RoleResponseSchema),
  async (c) => {
    const data = c.req.valid('json')
    const operator = getOperator(c)

    // 直接使用验证后的数据
    const roleData = data

    const role = await roleService.createRole(roleData, {
      createdBy: operator.account,
      creatorName: operator.name,
      updatedBy: operator.account,
      updaterName: operator.name,
    })

    return success(c, role, '创建角色成功')
  }
)

// 获取角色列表（分页）
app.get(
  '/page',
  describeUnifiedPageAPI({
    summary: '分页查询角色',
    description: '根据条件分页查询角色列表',
    tags: ['角色管理'],
    itemSchema: RolePageResponseSchema,
  }),
  requirePermissions('system:role:list'),
  validator('query', RolePageQuerySchema),
  responseResolver(RolePageResponseSchema),
  async (c) => {
    const query = c.req.valid('query')

    const result = await roleService.getRoles({
      page: query.current || 1,
      pageSize: query.size || 10,
      code: query.code,
      name: query.name,
      type: query.type,
      enabled: query.enabled,
    })

    return success(c, {
      total: result.total,
      records: result.data,
      current: query.current || 1,
      size: query.size || 10,
    })
  }
)

// 获取角色列表（简单列表）
app.get(
  '/list',
  describeUnifiedAPI({
    summary: '获取角色列表',
    description: '获取系统中角色列表',
    tags: ['角色管理'],
    successDataSchema: z.array(RoleResponseSchema),
  }),
  requirePermissions('system:role:list'),
  validator('query', RoleQuerySchema),
  responseResolver(z.array(RoleResponseSchema)),
  async (c) => {
    const query = c.req.valid('query')

    const result = await roleService.getRoles({
      page: 1,
      pageSize: 1000, // 获取所有角色
      code: query.code,
      name: query.name,
      type: query.type,
      enabled: query.enabled,
    })

    return success(c, result.data)
  }
)

// 获取角色详情
app.get(
  '/detail/:id',
  describeUnifiedAPI({
    summary: '获取角色详情',
    description: '根据角色ID获取详细角色信息',
    tags: ['角色管理'],
    successDataSchema: RoleResponseSchema,
  }),
  requirePermissions('system:role:list'),
  validator('param', z.object({ id: z.string() })),
  responseResolver(RoleResponseSchema),
  async (c) => {
    const { id } = c.req.valid('param')

    const role = await roleService.getRoleById(id)

    return success(c, role)
  }
)

// 更新角色
app.put(
  '/detail/:id',
  describeUnifiedAPI({
    summary: '更新角色信息',
    description: '根据角色ID更新角色信息',
    tags: ['角色管理'],
    successDataSchema: RoleResponseSchema,
  }),
  requirePermissions('system:role:update'),
  validator('param', z.object({ id: z.string() })),
  validator('json', UpdateRoleSchema),
  responseResolver(RoleResponseSchema),
  async (c) => {
    const { id } = c.req.valid('param')
    const data = c.req.valid('json')
    const operator = getOperator(c)

    // 转换数据类型，处理 null 值
    const updateData = {
      code: data.code,
      name: data.name,
      type: data.type,
      parentId: data.parentId,
      dataScope: data.dataScope,
      enabled: data.enabled,
      description: data.description,
      permissionIds: data.permissionIds,
      menuIds: data.menuIds,
    }

    const updatedRole = await roleService.updateRole(id, updateData, {
      updatedBy: operator.account,
      updaterName: operator.name,
    })

    return success(c, updatedRole, '更新角色成功')
  }
)

// 删除角色
app.delete(
  '/detail/:id',
  describeUnifiedAPI({
    summary: '删除角色',
    description: '根据角色ID删除角色',
    tags: ['角色管理'],
    successDataSchema: z.string(),
  }),
  requirePermissions('system:role:delete'),
  validator('param', z.object({ id: z.string() })),
  responseResolver(z.string()),
  async (c) => {
    const { id } = c.req.valid('param')

    await roleService.deleteRole(id)

    return success(c, id, '删除角色成功')
  }
)

// 获取角色树结构
app.get(
  '/tree',
  describeUnifiedAPI({
    summary: '获取角色树结构',
    description: '获取系统中角色的树形结构',
    tags: ['角色管理'],
    successDataSchema: z.array(z.any()), // 可以定义更具体的树结构schema
  }),
  requirePermissions('system:role:list'),
  responseResolver(z.array(z.any())),
  async (c) => {
    const roleTree = await roleService.getRoleTree()

    return success(c, roleTree)
  }
)

// 获取角色权限
app.get(
  '/detail/:id/permissions',
  describeUnifiedAPI({
    summary: '获取角色权限',
    description: '根据角色ID获取角色拥有的权限列表',
    tags: ['角色管理'],
    successDataSchema: z.array(z.any()), // 可以定义权限schema
  }),
  requirePermissions('system:role:list'),
  validator('param', z.object({ id: z.string() })),
  responseResolver(z.array(z.any())),
  async (c) => {
    const { id } = c.req.valid('param')

    const permissions = await roleService.getRolePermissions(id)

    return success(c, permissions)
  }
)

// 分配角色权限
app.put(
  '/detail/:id/permissions',
  describeUnifiedAPI({
    summary: '设置角色权限',
    description: '为角色分配权限',
    tags: ['角色管理'],
    successDataSchema: z.string(),
  }),
  requirePermissions('system:role:update'),
  validator('param', z.object({ id: z.string() })),
  validator('json', z.object({ permissionIds: z.array(z.string()) })),
  responseResolver(z.string()),
  async (c) => {
    const { id } = c.req.valid('param')
    const { permissionIds } = c.req.valid('json')
    const operator = getOperator(c)

    await roleService.replacePermissions(id, permissionIds, {
      updatedBy: operator.account,
      updaterName: operator.name,
    })

    return success(c, id, '角色权限设置成功')
  }
)

// 获取角色菜单
app.get(
  '/detail/:id/menus',
  describeUnifiedAPI({
    summary: '获取角色菜单',
    description: '根据角色ID获取角色拥有的菜单列表',
    tags: ['角色管理'],
    successDataSchema: z.array(z.any()), // 可以定义菜单schema
  }),
  requirePermissions('system:role:list'),
  validator('param', z.object({ id: z.string() })),
  responseResolver(z.array(z.any())),
  async (c) => {
    const { id } = c.req.valid('param')

    const menus = await roleService.getRoleMenus(id)

    return success(c, menus)
  }
)

// 分配角色菜单
app.put(
  '/detail/:id/menus',
  describeUnifiedAPI({
    summary: '设置角色菜单',
    description: '为角色分配菜单',
    tags: ['角色管理'],
    successDataSchema: z.string(),
  }),
  requirePermissions('system:role:update'),
  validator('param', z.object({ id: z.string() })),
  validator('json', z.object({ menuIds: z.array(z.string()) })),
  responseResolver(z.string()),
  async (c) => {
    const { id } = c.req.valid('param')
    const { menuIds } = c.req.valid('json')
    const operator = getOperator(c)

    await roleService.replaceMenus(id, menuIds, {
      updatedBy: operator.account,
      updaterName: operator.name,
    })

    return success(c, id, '角色菜单设置成功')
  }
)

export default app
