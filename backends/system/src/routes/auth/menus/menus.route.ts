import { describeUnifiedAPI } from '@/core/openapi'
import { fail, success } from '@/core/response/send'
import { responseResolver } from '@/middlewares'
import { menuPermissionService } from '@/services/menu-permission.service'
import { AppContext } from '@/types'
import { getUser } from '@/utils/get-operator'
import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'

const app = new Hono<AppContext>({ strict: true })

// 菜单权限检查 API
app.post(
  '/check-access',
  describeUnifiedAPI({
    summary: '批量检查菜单访问权限',
    description: '批量检查当前用户对指定菜单列表的访问权限',
    tags: ['菜单权限'],
    successDataSchema: z.record(z.string(), z.boolean()),
  }),
  validator(
    'json',
    z.object({
      menuIds: z.array(z.string()).min(1, '菜单ID列表不能为空'),
    })
  ),
  responseResolver(z.record(z.string(), z.boolean())),
  async (c) => {
    const user = getUser(c)
    if (!user?.id) {
      return fail(c, 'Authentication required', 401)
    }

    const { menuIds } = c.req.valid('json')

    const results = await menuPermissionService.batchCheckMenuAccess(user.id, menuIds)

    return success(c, results, '菜单权限检查完成')
  }
)

// 检查单个菜单访问权限
app.post(
  '/check-single',
  describeUnifiedAPI({
    summary: '检查单个菜单访问权限',
    description: '检查当前用户对指定菜单的访问权限',
    tags: ['菜单权限'],
    successDataSchema: z.object({
      hasAccess: z.boolean(),
    }),
  }),
  validator(
    'json',
    z.object({
      menuId: z.string().min(1, '菜单ID不能为空'),
    })
  ),
  responseResolver(
    z.object({
      hasAccess: z.boolean(),
    })
  ),
  async (c) => {
    const user = getUser(c)
    if (!user?.id) {
      return fail(c, 'Authentication required', 401)
    }

    const { menuId } = c.req.valid('json')

    const hasAccess = await menuPermissionService.checkMenuAccess(user.id, menuId)

    return success(c, { hasAccess }, '菜单权限检查完成')
  }
)

export default app
