import { auth } from '@/auth/server'
import { Hono } from 'hono'

import { AppContext } from '@/types'
import meRoute from './me'
import menusRoute from './menus'
import permissionsRoute from './permissions'

const app = new Hono<AppContext>()

// 注册子路由
app.route('/me', meRoute)
app.route('/permissions', permissionsRoute)
app.route('/menus', menusRoute)

// /api/v1/auth/*
app.on(['GET', 'POST'], '*', async (c) => {
  return auth.handler(c.req.raw)
})

export default app
