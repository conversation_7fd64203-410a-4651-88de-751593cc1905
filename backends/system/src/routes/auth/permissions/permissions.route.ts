import { describeUnifiedAPI } from '@/core/openapi'
import { fail, success } from '@/core/response/send'
import { responseResolver } from '@/middlewares'
import { authorizationService } from '@/services/authorization.service'
import { getUser } from '@/utils/get-operator'
import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'

const app = new Hono()

// 权限检查 API - 供前端使用
app.post(
  '/check',
  describeUnifiedAPI({
    summary: '批量检查用户权限',
    description: '批量检查当前用户是否拥有指定权限列表',
    tags: ['权限检查'],
    successDataSchema: z.record(z.string(), z.boolean()),
  }),
  validator(
    'json',
    z.object({
      permissions: z.array(z.string()).min(1, '权限列表不能为空'),
    })
  ),
  responseResolver(z.record(z.string(), z.boolean())),
  async (c) => {
    const user = getUser(c)
    if (!user?.id) {
      return fail(c, 'Authentication required', 401)
    }

    const { permissions } = c.req.valid('json')

    const results: Record<string, boolean> = {}

    for (const permission of permissions) {
      results[permission] = await authorizationService.checkPermission(user.id, permission)
    }

    return success(c, results, '权限检查完成')
  }
)

// 批量权限检查
app.post(
  '/batch-check',
  describeUnifiedAPI({
    summary: '高级批量权限检查',
    description: '执行复杂的批量权限检查，支持更多检查选项',
    tags: ['权限检查'],
    successDataSchema: z.array(
      z.object({
        permission: z.string(),
        hasPermission: z.boolean(),
        context: z.any().optional(),
      })
    ),
  }),
  validator(
    'json',
    z.object({
      permissionChecks: z
        .array(
          z.object({
            permission: z.string(),
            context: z.any().optional(),
          })
        )
        .min(1, '权限检查列表不能为空'),
    })
  ),
  responseResolver(
    z.array(
      z.object({
        permission: z.string(),
        hasPermission: z.boolean(),
        context: z.any().optional(),
      })
    )
  ),
  async (c) => {
    const user = getUser(c)
    if (!user?.id) {
      return fail(c, 'Authentication required', 401)
    }

    const { permissionChecks } = c.req.valid('json')

    const results = await authorizationService.batchCheckPermissions(user.id, permissionChecks)

    return success(c, results, '批量权限检查完成')
  }
)

export default app
