import { describeUnifiedAPI } from '@/core/openapi'
import { fail, success } from '@/core/response/send'
import { responseResolver } from '@/middlewares'
import { UserEffectiveRolesSchema } from '@/schemas/auth.schema'
import { MicroAppResponseSchema } from '@/schemas/micro-app.schema'
import { authorizationService } from '@/services/authorization.service'
import { menuPermissionService } from '@/services/menu-permission.service'
import { userRoleService } from '@/services/user-role.service'
import { AppContext } from '@/types'
import { getUser } from '@/utils/get-operator'
import { Hono } from 'hono'
import { z } from 'zod'

const app = new Hono<AppContext>({ strict: true })

// 获取当前用户权限信息
const permissionsSchema = z.object({
  userId: z.string(),
  permissions: z.array(z.string()),
  dataScope: z.array(z.string()),
  roles: UserEffectiveRolesSchema,
})
app.get(
  '/permissions',
  describeUnifiedAPI({
    summary: '获取当前用户权限信息',
    description: '获取当前登录用户的权限列表、数据权限范围和角色信息',
    tags: ['用户认证'],
    successDataSchema: permissionsSchema,
  }),
  responseResolver(permissionsSchema),
  async (c) => {
    const test = c.get('user')
    test?.roles
    const user = getUser(c)
    if (!user?.id) {
      return fail(c, 'Authentication required', 401)
    }

    const permissions = await authorizationService.getUserEffectivePermissions(user.id)
    const dataScope = await authorizationService.getUserDataScope(user.id)
    const roles = await userRoleService.getUserEffectiveRoles(user.id)

    const userPermissions = {
      userId: user.id,
      permissions: Array.from(permissions),
      dataScope,
      roles,
    }

    return success(c, userPermissions, '获取用户权限成功')
  }
)

// 获取当前用户可访问的菜单
app.get(
  '/menus',
  describeUnifiedAPI({
    summary: '获取当前用户可访问的菜单',
    description: '获取当前登录用户有权限访问的菜单树结构',
    tags: ['用户认证'],
    successDataSchema: z.array(z.any()), // 使用菜单树结构
  }),
  responseResolver(z.array(z.any())),
  async (c) => {
    const user = getUser(c)
    if (!user?.id) {
      return fail(c, 'Authentication required', 401)
    }

    const menus = await menuPermissionService.getUserMenus(user.id)
    return success(c, menus, '获取用户菜单成功')
  }
)

// 获取当前用户可访问的微应用
app.get(
  '/micro-apps',
  describeUnifiedAPI({
    summary: '获取当前用户可访问的微应用',
    description: '获取当前登录用户有权限访问的微应用列表',
    tags: ['用户认证'],
    successDataSchema: z.array(MicroAppResponseSchema),
  }),
  responseResolver(z.array(MicroAppResponseSchema)),
  async (c) => {
    const user = getUser(c)
    if (!user?.id) {
      return fail(c, 'Authentication required', 401)
    }

    const microApps = await menuPermissionService.getUserMicroApps(user.id)
    return success(c, microApps, '获取用户微应用成功')
  }
)

// 获取当前用户基本信息
const profileSchema = z.object({
  id: z.string(),
  email: z.string(),
  account: z.string().nullable(),
  name: z.string().nullable(),
})
app.get(
  '/profile',
  describeUnifiedAPI({
    summary: '获取当前用户基本信息',
    description: '获取当前登录用户的基本个人信息',
    tags: ['用户认证'],
    successDataSchema: profileSchema,
  }),
  responseResolver(profileSchema),
  async (c) => {
    const user = getUser(c)

    if (!user?.id) {
      return fail(c, 'Authentication required', 401)
    }

    const userInfo = {
      id: user.id,
      email: user.email,
      account: user.account,
      name: user.name,
    }

    return success(c, userInfo, '获取用户信息成功')
  }
)

// 获取用户可管理的部门
app.get(
  '/manageable-departments',
  describeUnifiedAPI({
    summary: '获取用户可管理的部门',
    description: '获取当前登录用户有权限管理的部门列表',
    tags: ['用户认证'],
    successDataSchema: z.array(z.any()), // 部门列表结构
  }),
  responseResolver(z.array(z.any())),
  async (c) => {
    const user = getUser(c)
    if (!user?.id) {
      return fail(c, 'Authentication required', 401)
    }

    // TODO: 实现获取用户可管理部门的逻辑
    const _manageableDepartments: unknown[] = []
    return fail(c, 'Not implemented', 501)
  }
)

export default app
