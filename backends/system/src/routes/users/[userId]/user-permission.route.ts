import { describeUnifiedAPI } from '@/core/openapi'
import { success } from '@/core/response/send'
import { responseResolver } from '@/middlewares'
import { requirePermissions } from '@/middlewares/rbac'
import { userRoleService } from '@/services/user-role.service'
import { getOperator } from '@/utils'
import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'

// /api/v1/users/:userId/*
const app = new Hono()

// 获取用户的角色列表
app.get(
  '/roles',
  describeUnifiedAPI({
    summary: '获取用户角色列表',
    description: '根据用户ID获取用户的所有角色',
    tags: ['用户角色管理'],
    successDataSchema: z.array(z.any()), // 可以定义更具体的角色schema
  }),
  requirePermissions('system:user:list'),
  validator('param', z.object({ userId: z.string() })),
  responseResolver(z.array(z.any())),
  async (c) => {
    const { userId } = c.req.valid('param')

    const userRoles = await userRoleService.getUserGlobalRoles(userId)

    return success(c, userRoles, '获取用户角色列表成功')
  }
)

// 为用户分配角色
app.post(
  '/roles',
  describeUnifiedAPI({
    summary: '分配用户角色',
    description: '为指定用户分配全局角色',
    tags: ['用户角色管理'],
    successDataSchema: z.string(),
  }),
  requirePermissions('system:user:update'),
  validator('param', z.object({ userId: z.string() })),
  validator('json', z.object({ roleIds: z.array(z.string()) })),
  responseResolver(z.string()),
  async (c) => {
    const { userId } = c.req.valid('param')
    const { roleIds } = c.req.valid('json')
    const operator = getOperator(c)

    await userRoleService.assignGlobalRoles(userId, roleIds, {
      createdBy: operator.account,
      creatorName: operator.name,
      updatedBy: operator.account,
      updaterName: operator.name,
    })

    return success(c, userId, '分配用户角色成功')
  }
)

// 移除用户的角色
app.delete(
  '/roles',
  describeUnifiedAPI({
    summary: '移除用户角色',
    description: '移除指定用户的全局角色',
    tags: ['用户角色管理'],
    successDataSchema: z.string(),
  }),
  requirePermissions('system:user:update'),
  validator('param', z.object({ userId: z.string() })),
  validator('json', z.object({ roleIds: z.array(z.string()) })),
  responseResolver(z.string()),
  async (c) => {
    const { userId } = c.req.valid('param')
    const { roleIds } = c.req.valid('json')

    await userRoleService.removeGlobalRoles(userId, roleIds)

    return success(c, userId, '移除用户角色成功')
  }
)

// 获取用户的有效角色（全局+部门）
app.get(
  '/effective-roles',
  describeUnifiedAPI({
    summary: '获取用户有效角色',
    description: '获取用户的所有有效角色，包括全局角色和部门角色',
    tags: ['用户角色管理'],
    successDataSchema: z.any(), // 可以定义更具体的有效角色schema
  }),
  requirePermissions('system:user:list'),
  validator('param', z.object({ userId: z.string() })),
  responseResolver(z.any()),
  async (c) => {
    const { userId } = c.req.valid('param')

    const effectiveRoles = await userRoleService.getUserEffectiveRoles(userId)

    return success(c, effectiveRoles, '获取用户有效角色成功')
  }
)

// 获取用户的有效权限
app.get(
  '/permissions',
  describeUnifiedAPI({
    summary: '获取用户权限',
    description: '获取用户的所有有效权限',
    tags: ['用户角色管理'],
    successDataSchema: z.array(z.string()),
  }),
  requirePermissions('system:user:list'),
  validator('param', z.object({ userId: z.string() })),
  responseResolver(z.array(z.string())),
  async (c) => {
    const { userId } = c.req.valid('param')

    const permissions = await userRoleService.getUserEffectivePermissions(userId)

    return success(c, Array.from(permissions), '获取用户权限成功')
  }
)

export default app
