import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'

import { describeUnifiedAPI, describeUnifiedPageAPI } from '@/core/openapi'
import { success } from '@/core/response/send'
import { responseResolver } from '@/middlewares'
import { requirePermission } from '@/middlewares/permission.middleware'
import { UpdateUserSchema, UserPageQuerySchema, UserPageResponseSchema, UserResponseSchema } from '@/schemas/user.schema'
import { getOperator } from '@/utils'

import { userService } from '@/services/user.service'
import userPermissionRoute from './[userId]'

// /api/v1/users/*
const app = new Hono()

app.route('/:userId', userPermissionRoute)

app.get(
  '/detail/:id',
  describeUnifiedAPI({
    summary: '获取用户信息',
    description: '根据用户ID获取详细用户信息',
    tags: ['用户管理'],
    successDataSchema: UserResponseSchema,
  }),
  validator('param', z.object({ id: z.string() })),
  requirePermission('user:read'),
  responseResolver(UserResponseSchema),
  async (c) => {
    const { id } = c.req.valid('param')
    const user = await userService.findById(id)
    return success(c, user)
  }
)

app.get(
  '/page',
  describeUnifiedPageAPI({
    summary: '分页查询用户',
    description: '根据条件分页查询用户列表',
    tags: ['用户管理'],
    itemSchema: UserPageResponseSchema,
  }),
  validator('query', UserPageQuerySchema),
  requirePermission('user:list'),
  responseResolver(UserPageResponseSchema),
  async (c) => {
    const query = c.req.valid('query')
    const result = await userService.findPage(query)
    return success(c, result)
  }
)

app.put(
  '/detail/:id',
  describeUnifiedAPI({
    summary: '更新用户信息',
    description: '根据用户ID更新用户信息',
    tags: ['用户管理'],
    successDataSchema: UserResponseSchema,
  }),
  validator('param', z.object({ id: z.string() })),
  validator('json', UpdateUserSchema),
  requirePermission('user:update'),
  responseResolver(UserResponseSchema),
  async (c) => {
    const { id } = c.req.valid('param')
    const data = c.req.valid('json')
    const operator = getOperator(c)

    const result = await userService.update(id, data, operator)

    return success(c, result, 'User updated successfully')
  }
)

app.delete(
  '/detail/:id',
  describeUnifiedAPI({
    summary: '删除用户',
    description: '根据用户ID删除用户（软删除）',
    tags: ['用户管理'],
    successDataSchema: z.string(),
  }),
  validator('param', z.object({ id: z.string() })),
  requirePermission('user:delete'),
  responseResolver(z.string()),
  async (c) => {
    const { id } = c.req.valid('param')
    const operator = getOperator(c)

    const result = await userService.delete(id, operator)

    return success(c, result, 'User deleted successfully')
  }
)

export default app
