import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'

import { success } from '@/core/response/send'
import {
  CreateDepartmentSchema,
  DepartmentPageQuerySchema,
  DepartmentPageResponseSchema,
  DepartmentQuerySchema,
  DepartmentResponseSchema,
  UpdateDepartmentSchema,
} from '@/schemas/department.schema'
import { getOperator } from '@/utils'

import { describeUnifiedAPI, describeUnifiedPageAPI } from '@/core/openapi'
import { responseResolver } from '@/middlewares'
import { departmentService } from '../../services/department.service'

// /api/v1/departments/*
const app = new Hono()

app.get(
  '/detail/:id',
  describeUnifiedAPI({
    summary: '获取部门信息',
    description: '根据部门ID获取详细部门信息',
    tags: ['部门管理'],
    successDataSchema: DepartmentResponseSchema,
  }),
  validator('param', z.object({ id: z.string() })),
  responseResolver(DepartmentResponseSchema),
  async (c) => {
    const { id } = c.req.valid('param')

    const Department = await departmentService.findById(id)

    return success(c, Department)
  }
)

app.get(
  '/list',
  describeUnifiedAPI({
    summary: '获取部门列表',
    description: '获取系统中部门列表',
    tags: ['部门管理'],
    successDataSchema: z.array(DepartmentResponseSchema),
  }),
  validator('query', DepartmentQuerySchema),
  responseResolver(z.array(DepartmentResponseSchema)),
  async (c) => {
    const query = c.req.valid('query')
    const allDepartments = await departmentService.findList(query)
    return success(c, allDepartments)
  }
)

app.get(
  '/page',
  describeUnifiedPageAPI({
    summary: '分页查询部门',
    description: '根据条件分页查询部门列表',
    tags: ['部门管理'],
    itemSchema: DepartmentPageResponseSchema,
  }),
  validator('query', DepartmentPageQuerySchema),
  responseResolver(DepartmentPageResponseSchema),
  async (c) => {
    const query = c.req.valid('query')
    const result = await departmentService.findPage(query)
    return success(c, result)
  }
)

app.post(
  '/create',
  describeUnifiedAPI({
    summary: '创建新部门',
    description: '创建新部门并返回部门信息',
    tags: ['部门管理'],
    successDataSchema: DepartmentResponseSchema,
  }),
  validator('json', CreateDepartmentSchema),
  responseResolver(DepartmentResponseSchema),
  async (c) => {
    const data = c.req.valid('json')
    const operator = getOperator(c)

    const result = await departmentService.create(data, operator)

    return success(c, result, 'Department created successfully')
  }
)

app.put(
  '/detail/:id',
  describeUnifiedAPI({
    summary: '更新部门信息',
    description: '根据部门ID更新部门信息',
    tags: ['部门管理'],
    successDataSchema: DepartmentResponseSchema,
  }),
  validator('param', z.object({ id: z.string() })),
  validator('json', UpdateDepartmentSchema),
  responseResolver(DepartmentResponseSchema),
  async (c) => {
    const { id } = c.req.valid('param')
    const data = c.req.valid('json')
    const operator = getOperator(c)

    const result = await departmentService.update(id, data, operator)

    return success(c, result, 'Department updated successfully')
  }
)

app.delete(
  '/detail/:id',
  describeUnifiedAPI({
    summary: '删除部门',
    description: '根据部门ID删除部门',
    tags: ['部门管理'],
    successDataSchema: z.string(),
  }),
  validator('param', z.object({ id: z.string() })),
  responseResolver(z.string()),
  async (c) => {
    const { id } = c.req.valid('param')
    const operator = getOperator(c)

    await departmentService.delete(id, operator)

    return success(c, id, 'Department deleted successfully')
  }
)

export default app
