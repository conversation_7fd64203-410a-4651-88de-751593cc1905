import { describeUnifiedAPI, describeUnifiedPageAPI } from '@/core/openapi'
import { success } from '@/core/response/send'
import { responseResolver } from '@/middlewares'
import { requirePermissions } from '@/middlewares/rbac'
import {
  CreateMenuSchema,
  MenuPageQuerySchema,
  MenuPageResponseSchema,
  MenuQuerySchema,
  MenuResponseSchema,
  UpdateMenuSchema,
} from '@/schemas/menu.schema'
import { menuService } from '@/services/menu.service'
import { getOperator } from '@/utils'
import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'

// /api/v1/menus/*
const app = new Hono()

// 创建菜单
app.post(
  '/',
  describeUnifiedAPI({
    summary: '创建菜单',
    description: '创建新菜单并返回菜单信息',
    tags: ['菜单管理'],
    successDataSchema: MenuResponseSchema,
  }),
  requirePermissions('system:menu:create'),
  validator('json', CreateMenuSchema),
  responseResolver(MenuResponseSchema),
  async (c) => {
    const data = c.req.valid('json')
    const operator = getOperator(c)

    const menu = await menuService.create(data, operator)

    return success(c, menu, '创建菜单成功')
  }
)

// 获取菜单列表（分页）
app.get(
  '/page',
  describeUnifiedPageAPI({
    summary: '分页查询菜单',
    description: '根据条件分页查询菜单列表',
    tags: ['菜单管理'],
    itemSchema: MenuPageResponseSchema,
  }),
  requirePermissions('system:menu:list'),
  validator('query', MenuPageQuerySchema),
  responseResolver(MenuPageResponseSchema),
  async (c) => {
    const query = c.req.valid('query')

    const result = await menuService.findPage({
      current: query.current || 1,
      size: query.size || 10,
      name: query.name,
      type: query.type,
      enabled: query.enabled,
      parentId: query.parentId,
      createdAtStart: query.createdAtStart,
      createdAtEnd: query.createdAtEnd,
      updatedAtStart: query.updatedAtStart,
      updatedAtEnd: query.updatedAtEnd,
      orderBy: query.orderBy,
    })

    return success(c, {
      total: result.total,
      records: result.records,
      current: query.current || 1,
      size: query.size || 10,
    })
  }
)

// 获取菜单列表（简单列表）
app.get(
  '/list',
  describeUnifiedAPI({
    summary: '获取菜单列表',
    description: '获取系统中菜单列表',
    tags: ['菜单管理'],
    successDataSchema: z.array(MenuResponseSchema),
  }),
  requirePermissions('system:menu:list'),
  validator('query', MenuQuerySchema),
  responseResolver(z.array(MenuResponseSchema)),
  async (c) => {
    const query = c.req.valid('query')

    const menus = await menuService.findList(query)

    return success(c, menus)
  }
)

// 获取菜单详情
app.get(
  '/detail/:id',
  describeUnifiedAPI({
    summary: '获取菜单详情',
    description: '根据菜单ID获取详细菜单信息',
    tags: ['菜单管理'],
    successDataSchema: MenuResponseSchema,
  }),
  requirePermissions('system:menu:list'),
  validator('param', z.object({ id: z.string() })),
  responseResolver(MenuResponseSchema),
  async (c) => {
    const { id } = c.req.valid('param')

    const menu = await menuService.findById(id)

    return success(c, menu)
  }
)

// 更新菜单
app.put(
  '/detail/:id',
  describeUnifiedAPI({
    summary: '更新菜单信息',
    description: '根据菜单ID更新菜单信息',
    tags: ['菜单管理'],
    successDataSchema: MenuResponseSchema,
  }),
  requirePermissions('system:menu:update'),
  validator('param', z.object({ id: z.string() })),
  validator('json', UpdateMenuSchema),
  responseResolver(MenuResponseSchema),
  async (c) => {
    const { id } = c.req.valid('param')
    const data = c.req.valid('json')
    const operator = getOperator(c)

    const updatedMenu = await menuService.update(id, data, operator)

    return success(c, updatedMenu, '更新菜单成功')
  }
)

// 删除菜单
app.delete(
  '/detail/:id',
  describeUnifiedAPI({
    summary: '删除菜单',
    description: '根据菜单ID删除菜单',
    tags: ['菜单管理'],
    successDataSchema: z.string(),
  }),
  requirePermissions('system:menu:delete'),
  validator('param', z.object({ id: z.string() })),
  responseResolver(z.string()),
  async (c) => {
    const { id } = c.req.valid('param')
    const operator = getOperator(c)

    const deletedId = await menuService.delete(id, operator)

    return success(c, deletedId, '删除菜单成功')
  }
)

// 获取菜单树结构
app.get(
  '/tree',
  describeUnifiedAPI({
    summary: '获取菜单树结构',
    description: '获取系统中菜单的树形结构',
    tags: ['菜单管理'],
    successDataSchema: z.array(z.any()), // 可以定义更具体的树结构schema
  }),
  requirePermissions('system:menu:list'),
  responseResolver(z.array(z.any())),
  async (c) => {
    const menuTree = await menuService.getMenuTree()

    return success(c, menuTree)
  }
)

// 获取启用的菜单列表
app.get(
  '/enabled',
  describeUnifiedAPI({
    summary: '获取启用的菜单列表',
    description: '获取系统中所有启用状态的菜单',
    tags: ['菜单管理'],
    successDataSchema: z.array(MenuResponseSchema),
  }),
  requirePermissions('system:menu:list'),
  responseResolver(z.array(MenuResponseSchema)),
  async (c) => {
    const menus = await menuService.getEnabledMenus()

    return success(c, menus)
  }
)

// 批量更新菜单状态
app.put(
  '/batch/status',
  describeUnifiedAPI({
    summary: '批量更新菜单状态',
    description: '批量启用或禁用菜单',
    tags: ['菜单管理'],
    successDataSchema: z.array(z.string()),
  }),
  requirePermissions('system:menu:update'),
  validator(
    'json',
    z.object({
      ids: z.array(z.string()),
      enabled: z.number().int().min(0).max(1),
    })
  ),
  responseResolver(z.array(z.string())),
  async (c) => {
    const { ids, enabled } = c.req.valid('json')
    const operator = getOperator(c)

    const updatedIds = await menuService.batchUpdateStatus(ids, enabled, operator)

    return success(c, updatedIds, '批量更新菜单状态成功')
  }
)

export default app
