import { env } from '@/env'
import { logger } from '@/utils'
import { PrismaClient } from '@prisma/client'
import { Account, betterAuth } from 'better-auth'
import { prismaAdapter } from 'better-auth/adapters/prisma'
import { genericOAuth, openAPI } from 'better-auth/plugins'
import { nanoid } from 'nanoid'

const CVTE_OAUTH2_PROVIDER_ID = 'cvte-oauth2'

// 创建一个内存 Map 用于临时存储 OAuth 用户的 profile 数据
// key: accountId 或 email, value: profile 数据
const oauthProfileMap = new Map<string, Record<string, unknown>>()

const prisma = new PrismaClient()

async function cacheCvteOauth2Profile(email: string, profile: Record<string, unknown>) {
  // 使用 email 作为 Map 的 key
  // 将完整的 profile 数据存储到内存 Map
  oauthProfileMap.set(email, profile)

  // 设置一个短暂的超时，以确保数据不会永久留在内存中
  setTimeout(
    () => {
      oauthProfileMap.delete(email)
    },
    5 * 60 * 1000
  ) // 5分钟后自动清理
}

async function storeCvteOauth2Profile(prisma: PrismaClient, account: Account) {
  try {
    // 1. 尝试从用户信息获取 email
    const user = await prisma.user.findUnique({
      where: { id: account.userId },
      select: { email: true },
    })

    if (!user?.email) {
      logger.warn('User email not found for account:', account.id)
      return null
    }

    // 2. 尝试获取缓存的 profile 数据
    let profileData = oauthProfileMap.get(user.email)

    // 3. 如果没有缓存数据且有 accessToken，尝试从 API 获取
    if (!profileData && account.accessToken) {
      try {
        const response = await fetch(`${env.CVTE_OAUTH2_DOMAIN}/portal/oauth2/user`, {
          headers: {
            Authorization: `Bearer ${account.accessToken}`,
          },
        })

        if (response.ok) {
          profileData = await response.json()
        } else {
          logger.warn('Failed to fetch user profile from API:', response.status)
        }
      } catch (error) {
        logger.warn('Error fetching user profile:', error instanceof Error ? error.message : 'Unknown error')
      }
    }

    // 4. 清理缓存数据
    if (oauthProfileMap.has(user.email)) {
      oauthProfileMap.delete(user.email)
    }

    // 5. 更新账号信息
    if (profileData) {
      return await prisma.account.update({
        where: { id: account.id },
        data: {
          profile: JSON.parse(JSON.stringify(profileData)),
        },
      })
    }

    return null
  } catch (error) {
    logger.error('Error in storeCvteOauth2Profile:', error instanceof Error ? error.message : 'Unknown error')
    throw error // 重新抛出错误，让上层处理
  }
}

export const auth = betterAuth({
  basePath: '/api/v1/auth',
  database: prismaAdapter(prisma, {
    provider: 'mysql',
  }),
  user: {
    modelName: 'User',
    additionalFields: {
      gender: {
        type: 'number',
        required: false,
        defaultValue: undefined,
      },
      telephone: {
        type: 'string',
        required: false,
      },
      account: {
        type: 'string',
        required: false,
      },
      roles: {
        type: 'string[]',
        required: false,
        defaultValue: undefined,
        input: false, // don't allow user to set role
      },
      lang: {
        type: 'string',
        required: false,
        defaultValue: undefined,
      },
    },
  },
  session: {
    modelName: 'Session',
  },
  account: {
    modelName: 'Account',
    accountLinking: {
      enabled: true,
      // Reference Link: https://www.better-auth.com/docs/concepts/users-accounts#forced-linking
      trustedProviders: [CVTE_OAUTH2_PROVIDER_ID],
    },
  },
  verification: {
    modelName: 'Verification',
  },
  emailAndPassword: {
    enabled: true,
  },
  advanced: {
    database: {
      generateId: () => nanoid(),
    },
    // 跨子域共享 cookie，统一用 API Gateway，暂无此需求
    // crossSubDomainCookies: {
    //   enabled: true
    // },
  },
  trustedOrigins: ['*', 'http://localhost:8000'],
  databaseHooks: {
    account: {
      create: {
        after: async (account) => {
          if (account.providerId === CVTE_OAUTH2_PROVIDER_ID) {
            storeCvteOauth2Profile(prisma, account)
          }
        },
      },
    },
  },
  plugins: [
    openAPI(),
    genericOAuth({
      config: [
        {
          providerId: CVTE_OAUTH2_PROVIDER_ID,
          clientId: env.CVTE_OAUTH2_CLIENT_ID,
          clientSecret: "Don't need the secret.", // Only God and the people from CVTE IT know why there is no need for the secret.
          authorizationUrl: `${env.CVTE_OAUTH2_DOMAIN}/portal/oauth2/authorize`,
          tokenUrl: `${env.CVTE_OAUTH2_DOMAIN}/portal/oauth2/token`,
          userInfoUrl: `${env.CVTE_OAUTH2_DOMAIN}/portal/oauth2/user`,
          mapProfileToUser: async (profile) => {
            await cacheCvteOauth2Profile(profile.email, profile)
            return {
              ...profile,
              emailVerified: true, // Only God and CVTE IT know why the account email hasn't been verified.
              gender: parseInt(profile.gender),
            }
          },
        },
      ],
    }),
  ],
})

export default auth
