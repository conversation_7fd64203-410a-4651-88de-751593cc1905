import { Prisma } from '@prisma/client'

/**
 * 解析 PRISMA_LOG 环境变量，转换为 Prisma 日志配置
 * 支持多种格式：
 * - 'true' -> ['query', 'error', 'warn', 'info']
 * - 'false' -> ['error']
 * - 逗号分隔的日志级别 'query,error,warn' -> ['query', 'error', 'warn']
 */
export function getLogLevel(logConfig: string | undefined): Prisma.LogLevel[] {
  if (!logConfig) return ['error']

  if (logConfig === 'true') return ['query', 'error', 'warn', 'info']

  if (logConfig === 'false') return ['error']

  // 支持逗号分隔的日志级别
  const validLevels = ['query', 'error', 'warn', 'info'] as const
  const levels = logConfig
    .split(',')
    .map((level) => level.trim())
    .filter((level) => validLevels.includes(level as Prisma.LogLevel))

  // 如果没有有效的日志级别，默认使用 error
  return levels.length > 0 ? (levels as Prisma.LogLevel[]) : ['error']
}
