// import { config } from '@/config'
import { PrismaClient } from '@prisma/client'
import { createSoftDeleteExtension } from 'prisma-extension-soft-delete'

import { env } from '@/env'
import logger from '@/utils/logger'

import { registerExtensions } from './extensions'
import { getLogLevel } from './utils/log-level.util'

/**
 * 创建 PrismaClient 实例，支持软删除扩展和日志级别配置
 */
function createPrismaClient() {
  // 解析日志配置
  const logLevels = getLogLevel('query')

  // 初始化客户端实例，使用处理后的日志级别配置
  const _client = new PrismaClient({
    log: logLevels,
  })

  // 记录初始化的日志配置
  logger.info(`PrismaClient initialized, log levels: [${logLevels.join(', ')}]`)

  // 应用扩展
  const _extendedClient = registerExtensions(_client)

  // 添加软删除扩展
  const db = _extendedClient.$extends(
    createSoftDeleteExtension({
      models: {
        User: true,
        Role: true,
        Permission: true,
        Menu: true,
        UserRole: true,
        RolePermission: true,
        RoleMenu: true,
        DepartmentMemberRole: true,
      },
      defaultConfig: {
        field: 'deletedAt',
        createValue: (deleted: boolean) => (deleted ? Math.floor(Date.now() / 1000) : -1),
      },
    })
  )

  return db
}

const globalForPrisma = globalThis as unknown as {
  prisma: ReturnType<typeof createPrismaClient> | undefined
}

const db = globalForPrisma.prisma ?? createPrismaClient()

if (env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = db
}

export { db }
