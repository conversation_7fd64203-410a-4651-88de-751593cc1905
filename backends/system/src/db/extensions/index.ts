import { PrismaClient } from '@prisma/client'
import { createFindPageExtension } from './find-page.extension'
import { createPerformanceMonitoringExtension } from './performance-monitoring.extension'
import { createSoftDeleteExtension } from './soft-delete.extension'

/**
 * 注册所有 Prisma 扩展
 * @param client Prisma 客户端实例
 * @returns 扩展后的 Prisma 客户端
 */
export function registerExtensions(client: PrismaClient) {
  return client
    .$extends(createSoftDeleteExtension())
    .$extends(createFindPageExtension())
    .$extends(createPerformanceMonitoringExtension())
}
