import { Any } from '@/types'
import { Prisma } from '@prisma/client'

export type FindPageResult<T> = {
  records: T
  total: number
  current: number
  size: number
  orderBy?: Record<string, 'asc' | 'desc'>
}

/**
 * 创建分页查询扩展
 * 为所有 Prisma 模型添加 findPage 方法，支持分页查询
 */
export function createFindPageExtension() {
  return Prisma.defineExtension((client) => {
    return client.$extends({
      model: {
        $allModels: {
          async findPage<T, A>(
            this: T,
            args?: Prisma.Exact<A, Omit<Prisma.Args<T, 'findMany'>, 'cursor' | 'take' | 'skip'>>,
            pageable?: { current?: number; size?: number; orderBy?: Prisma.Args<T, 'findMany'>['orderBy'] }
          ): Promise<FindPageResult<Prisma.Result<T, A, 'findMany'>>> {
            const context = Prisma.getExtensionContext(this)

            const { current = 1, size = 10, orderBy } = pageable ?? {}
            const [records, total] = await Promise.all([
              // @ts-expect-error - a
              (context as Any).findMany({ skip: (current - 1) * size, take: size, orderBy, ...args }),
              // @ts-expect-error - a
              (context as Any).count({ where: args?.where as never }),
            ])
            return { records, total, current, size, orderBy }
          },
        },
      },
    })
  })
}
