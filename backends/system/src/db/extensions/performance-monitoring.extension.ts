import { Prisma } from '@prisma/client'

const logger = console
const PRISMA_SLOW_QUERY_THRESHOLD = parseInt(process.env.PRISMA_SLOW_QUERY_THRESHOLD ?? '1000')

/**
 * 创建性能监控扩展
 * 监控 Prisma 查询性能，记录慢查询
 */
export function createPerformanceMonitoringExtension() {
  return Prisma.defineExtension((client) => {
    return client.$extends({
      query: {
        async $allOperations({ operation, model, args, query }) {
          const start = performance.now()
          const result = await query(args)
          const end = performance.now()
          const duration = end - start
          if (duration > PRISMA_SLOW_QUERY_THRESHOLD) {
            logger.warn(`Slow query detected, duration: ${duration.toFixed(2)}ms`, {
              action: operation,
              model,
              query: args,
            })
          }
          return result
        },
      },
    })
  })
}
