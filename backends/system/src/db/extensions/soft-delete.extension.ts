import { Prisma } from '@prisma/client'
import { createSoftDeleteExtension as createSoftDeleteExtensionOriginal } from 'prisma-extension-soft-delete'

const SOFT_DELETE_FIELD = 'deletedAt'

/**
 * 创建软删除扩展的包装器
 * 自动检测模型中的软删除字段并应用扩展
 */
export function createSoftDeleteExtension() {
  // 获取所有模型并检查 deletedAt 字段
  const models = Object.fromEntries(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    Object.values((Prisma as any).dmmf.datamodel.models).map((model: any) => [
      model.name,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      model.fields.some((field: any) => field.name === SOFT_DELETE_FIELD),
    ])
  )

  return createSoftDeleteExtensionOriginal({
    models,
    defaultConfig: {
      field: SOFT_DELETE_FIELD,
      createValue: (deleted) => {
        if (deleted) return Date.now()
        return -1
      },
    },
  })
}
