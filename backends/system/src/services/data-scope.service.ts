// RBAC 数据权限范围服务

import { db } from '@/db'
import { userRoleService } from './user-role.service'

export class DataScopeService {
  /**
   * 检查用户是否有访问目标部门数据的权限
   */
  async checkDepartmentDataAccess(userId: string, targetDeptId: string): Promise<boolean> {
    if (!targetDeptId) return true // 如果没有指定部门，允许访问

    // 获取用户所属的所有部门
    const userDepartments = await db.departmentMember.findMany({
      where: {
        userId,
      },
      include: {
        department: {
          select: { id: true, treePath: true },
        },
      },
    })

    // 获取目标部门信息
    const targetDept = await db.department.findUnique({
      where: { id: targetDeptId },
      select: { treePath: true },
    })

    if (!targetDept) return false

    // 检查用户是否有权限访问目标部门
    for (const userDept of userDepartments) {
      const userDeptPath = userDept.department.treePath
      const targetDeptPath = targetDept.treePath

      // 检查是否是同一部门
      if (userDept.department.id === targetDeptId) {
        return true
      }

      // 检查是否是父级部门（用户部门路径是目标部门路径的前缀）
      if (targetDeptPath.startsWith(userDeptPath + '.') || targetDeptPath === userDeptPath) {
        return true
      }
    }

    return false
  }

  /**
   * 检查下级部门数据访问权限
   */
  async checkSubordinateDataAccess(userId: string, targetDeptId: string): Promise<boolean> {
    if (!targetDeptId) return true

    // 获取用户管理的部门
    const userManagedDepts = await db.departmentMember.findMany({
      where: {
        userId,
        type: { in: ['OWNER', 'MANAGER'] }, // 只有部门负责人和管理员可以管理下级
      },
      include: {
        department: {
          select: { id: true, treePath: true },
        },
      },
    })

    // 获取目标部门信息
    const targetDept = await db.department.findUnique({
      where: { id: targetDeptId },
      select: { treePath: true },
    })

    if (!targetDept) return false

    // 检查是否可以管理目标部门
    for (const managedDept of userManagedDepts) {
      const managedDeptPath = managedDept.department.treePath
      const targetDeptPath = targetDept.treePath

      // 检查目标部门是否是用户管理部门的下级
      if (targetDeptPath.startsWith(managedDeptPath + '.') || targetDeptPath === managedDeptPath) {
        return true
      }
    }

    return false
  }

  /**
   * 检查个人数据访问权限
   */
  async checkSelfDataAccess(userId: string, targetUserId: string): Promise<boolean> {
    // 如果没有指定用户ID，或者就是当前用户，允许访问
    return !targetUserId || targetUserId === userId
  }

  /**
   * 获取用户可访问的部门ID列表
   */
  async getUserAccessibleDepartments(userId: string): Promise<string[]> {
    const dataScopes = await this.getUserDataScope(userId)

    // 如果有全部数据权限，返回所有部门
    if (dataScopes.includes('all')) {
      const allDepts = await db.department.findMany({
        select: { id: true },
      })
      return allDepts.map((d) => d.id)
    }

    const accessibleDeptIds = new Set<string>()

    // 获取用户所属部门
    const userDepartments = await db.departmentMember.findMany({
      where: {
        userId,
      },
      include: {
        department: true,
      },
    })

    for (const userDept of userDepartments) {
      accessibleDeptIds.add(userDept.department.id)

      // 如果有下级部门权限，添加所有下级部门
      if (dataScopes.includes('subordinate') && userDept.type === 'MANAGER') {
        const subordinateDepts = await db.department.findMany({
          where: {
            treePath: {
              startsWith: `${userDept.department.treePath}.`,
            },
          },
          select: { id: true },
        })
        subordinateDepts.forEach((d) => accessibleDeptIds.add(d.id))
      }
    }

    return Array.from(accessibleDeptIds)
  }

  /**
   * 获取用户可访问的用户ID列表
   */
  async getUserAccessibleUsers(userId: string): Promise<string[]> {
    const dataScopes = await this.getUserDataScope(userId)

    // 如果有全部数据权限，返回所有用户
    if (dataScopes.includes('all')) {
      const allUsers = await db.user.findMany({
        select: { id: true },
      })
      return allUsers.map((u) => u.id)
    }

    const accessibleUserIds = new Set<string>()

    // 如果只有个人数据权限，只返回自己
    if (dataScopes.includes('self') && !dataScopes.includes('department') && !dataScopes.includes('subordinate')) {
      return [userId]
    }

    // 获取可访问的部门
    const accessibleDeptIds = await this.getUserAccessibleDepartments(userId)

    // 获取这些部门的所有成员
    const deptMembers = await db.departmentMember.findMany({
      where: {
        departmentId: { in: accessibleDeptIds },
      },
      select: { userId: true },
    })

    deptMembers.forEach((m) => accessibleUserIds.add(m.userId))

    // 总是包含自己
    accessibleUserIds.add(userId)

    return Array.from(accessibleUserIds)
  }

  /**
   * 获取用户数据权限范围
   */
  private async getUserDataScope(userId: string): Promise<string[]> {
    const userPermissions = await userRoleService.getUserEffectivePermissions(userId)
    const dataScopes: string[] = []

    // 检查各种数据范围权限
    if (userPermissions.has('data:scope:all') || userPermissions.has('*')) {
      dataScopes.push('all')
    }
    if (userPermissions.has('data:scope:subordinate')) {
      dataScopes.push('subordinate')
    }
    if (userPermissions.has('data:scope:department')) {
      dataScopes.push('department')
    }
    if (userPermissions.has('data:scope:self')) {
      dataScopes.push('self')
    }

    return dataScopes
  }

  /**
   * 检查用户是否有指定资源的数据权限
   */
  async checkResourceDataAccess(
    userId: string,
    resourceType: string,
    resourceId: string,
    context: {
      departmentId?: string
      ownerId?: string
    } = {}
  ): Promise<boolean> {
    const dataScopes = await this.getUserDataScope(userId)

    // 如果有全部数据权限，直接通过
    if (dataScopes.includes('all')) {
      return true
    }

    // 检查个人数据权限
    if (context.ownerId && dataScopes.includes('self')) {
      if (context.ownerId === userId) {
        return true
      }
    }

    // 检查部门数据权限
    if (context.departmentId) {
      if (dataScopes.includes('department')) {
        return await this.checkDepartmentDataAccess(userId, context.departmentId)
      }

      if (dataScopes.includes('subordinate')) {
        return await this.checkSubordinateDataAccess(userId, context.departmentId)
      }
    }

    return false
  }
}

// 创建单例实例
export const dataScopeService = new DataScopeService()
