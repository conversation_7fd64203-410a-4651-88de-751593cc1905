// RBAC 部门管理服务

import { db } from '@/db'
import type { AuditFields, PaginatedResult } from '@/schemas/auth.schema'
import type { CreateDepartment, DepartmentQuery, DepartmentTreeResponse, UpdateDepartment } from '@/schemas/department.schema'
import type { Department } from '@prisma/client'

export class DepartmentRoleService {
  /**
   * 创建部门
   */
  async createDepartment(data: CreateDepartment, auditFields: AuditFields): Promise<Department> {
    // 生成树路径
    let treePath = data.code
    if (data.parentId) {
      const parent = await this.getDepartmentById(data.parentId)
      if (parent) {
        treePath = `${parent.treePath}.${data.code}`
      }
    }

    return await db.department.create({
      data: {
        ...data,
        treePath,
        ...auditFields,
      },
    })
  }

  /**
   * 更新部门
   */
  async updateDepartment(id: string, data: UpdateDepartment, auditFields: Partial<AuditFields>): Promise<Department> {
    // 如果更新了父级或编码，需要重新计算树路径
    if (data.parentId !== undefined || data.code) {
      const currentDept = await this.getDepartmentById(id)
      if (!currentDept) {
        throw new Error('Department not found')
      }

      let treePath = data.code || currentDept.code
      if (data.parentId) {
        const parent = await this.getDepartmentById(data.parentId)
        if (parent) {
          treePath = `${parent.treePath}.${treePath}`
        }
      }
      ;(data as UpdateDepartment & { treePath: string }).treePath = treePath
    }

    return await db.department.update({
      where: { id },
      data: {
        ...data,
        updatedBy: auditFields.updatedBy,
        updaterName: auditFields.updaterName,
      },
    })
  }

  /**
   * 删除部门
   */
  async deleteDepartment(id: string): Promise<void> {
    // 检查是否有子部门
    const childrenCount = await db.department.count({
      where: {
        parentId: id,
      },
    })

    if (childrenCount > 0) {
      throw new Error('Cannot delete department with children')
    }

    // 检查是否有部门成员
    const memberCount = await db.departmentMember.count({
      where: {
        departmentId: id,
      },
    })

    if (memberCount > 0) {
      throw new Error('Cannot delete department with members')
    }

    // 软删除部门
    await db.department.delete({
      where: { id },
    })
  }

  /**
   * 根据ID获取部门
   */
  async getDepartmentById(id: string): Promise<Department | null> {
    return await db.department.findUnique({
      where: { id },
    })
  }

  /**
   * 根据编码获取部门
   */
  async getDepartmentByCode(code: string): Promise<Department | null> {
    return await db.department.findFirst({
      where: {
        code,
      },
    })
  }

  /**
   * 获取部门列表（分页）
   */
  async getDepartments(query: DepartmentQuery & { page?: number; pageSize?: number }): Promise<PaginatedResult<Department>> {
    const { page = 1, pageSize = 20, name, code, enabled, parentId } = query

    const where = {
      ...(name && { name: { contains: name } }),
      ...(code && { code: { contains: code } }),
      ...(enabled !== undefined && { enabled }),
      ...(parentId !== undefined && { parentId }),
    }

    const [data, total] = await Promise.all([
      db.department.findMany({
        where,
        skip: (page - 1) * pageSize,
        take: pageSize,
        orderBy: { treePath: 'asc' },
      }),
      db.department.count({ where }),
    ])

    return {
      data,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    }
  }

  /**
   * 获取部门树结构
   */
  async getDepartmentTree(): Promise<DepartmentTreeResponse[]> {
    const departments = await db.department.findMany({
      orderBy: { treePath: 'asc' },
    })

    return this.buildDepartmentTree(departments)
  }

  /**
   * 构建部门树结构
   */
  private buildDepartmentTree(departments: Department[], parentId: string | null = null): DepartmentTreeResponse[] {
    const children = departments.filter((dept) => dept.parentId === parentId)

    return children.map((dept) => ({
      ...dept,
      children: this.buildDepartmentTree(departments, dept.id),
    }))
  }

  /**
   * 获取部门的所有子部门
   */
  async getDepartmentChildren(parentId: string): Promise<Department[]> {
    return await db.department.findMany({
      where: {
        parentId,
      },
      orderBy: { treePath: 'asc' },
    })
  }

  /**
   * 获取部门的所有下级部门（递归）
   */
  async getDepartmentDescendants(parentId: string): Promise<Department[]> {
    const parent = await this.getDepartmentById(parentId)
    if (!parent) return []

    return await db.department.findMany({
      where: {
        treePath: {
          startsWith: `${parent.treePath}.`,
        },
      },
      orderBy: { treePath: 'asc' },
    })
  }

  /**
   * 获取部门的所有祖先部门
   */
  async getDepartmentAncestors(departmentId: string): Promise<Department[]> {
    const department = await this.getDepartmentById(departmentId)
    if (!department) return []

    const pathParts = department.treePath.split('.')
    const ancestorCodes = []

    for (let i = 0; i < pathParts.length - 1; i++) {
      ancestorCodes.push(pathParts.slice(0, i + 1).join('.'))
    }

    if (ancestorCodes.length === 0) return []

    return await db.department.findMany({
      where: {
        treePath: { in: ancestorCodes },
      },
      orderBy: { treePath: 'asc' },
    })
  }

  /**
   * 获取用户可管理的部门列表
   */
  async getManageableDepartments(userId: string): Promise<Department[]> {
    // 获取用户作为管理员的部门
    const managedDepartments = await db.departmentMember.findMany({
      where: {
        userId,
        type: { in: ['OWNER', 'MANAGER'] },
      },
      include: {
        department: true,
      },
    })

    const departments: Department[] = []

    for (const member of managedDepartments) {
      if (!member.department) continue

      // 添加当前部门
      departments.push(member.department)

      // 添加所有下级部门
      const descendants = await this.getDepartmentDescendants(member.department.id)
      departments.push(...descendants)
    }

    // 去重
    const uniqueDepartments = departments.filter((dept, index, self) => index === self.findIndex((d) => d.id === dept.id))

    return uniqueDepartments.sort((a, b) => a.treePath.localeCompare(b.treePath))
  }

  /**
   * 检查部门编码是否存在
   */
  async isDepartmentCodeExists(code: string, excludeId?: string): Promise<boolean> {
    const where = {
      code,
      ...(excludeId && { id: { not: excludeId } }),
    }

    const count = await db.department.count({ where })
    return count > 0
  }

  /**
   * 检查用户是否有部门管理权限
   */
  async hasManagePermission(userId: string, departmentId: string): Promise<boolean> {
    // 检查是否是部门管理员
    const memberCount = await db.departmentMember.count({
      where: {
        userId,
        departmentId,
        type: { in: ['OWNER', 'MANAGER'] },
      },
    })

    if (memberCount > 0) return true

    // 检查是否是父级部门管理员
    const department = await this.getDepartmentById(departmentId)
    if (!department) return false

    const ancestors = await this.getDepartmentAncestors(departmentId)
    for (const ancestor of ancestors) {
      const ancestorMemberCount = await db.departmentMember.count({
        where: {
          userId,
          departmentId: ancestor.id,
          type: { in: ['OWNER', 'MANAGER'] },
        },
      })

      if (ancestorMemberCount > 0) return true
    }

    return false
  }

  /**
   * 获取部门成员数量
   */
  async getDepartmentMemberCount(departmentId: string): Promise<number> {
    return await db.departmentMember.count({
      where: {
        departmentId,
      },
    })
  }

  /**
   * 获取部门层级深度
   */
  getDepartmentLevel(department: Department): number {
    return department.treePath.split('.').length
  }

  /**
   * 检查是否是父子部门关系
   */
  isParentDepartment(parentId: string, childId: string, departments?: Department[]): boolean {
    if (!departments) {
      // 如果没有提供部门列表，需要异步查询
      throw new Error('Departments list is required for synchronous check')
    }

    const child = departments.find((d) => d.id === childId)
    if (!child) return false

    const parent = departments.find((d) => d.id === parentId)
    if (!parent) return false

    return child.treePath.startsWith(`${parent.treePath}.`)
  }

  /**
   * 获取根部门列表
   */
  async getRootDepartments(): Promise<Department[]> {
    return await db.department.findMany({
      where: {
        parentId: null,
      },
      orderBy: { treePath: 'asc' },
    })
  }
}

// 创建单例实例
export const departmentRoleService = new DepartmentRoleService()
