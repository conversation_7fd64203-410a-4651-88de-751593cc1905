// RBAC 授权服务 - 权限检查核心

import type { PermissionCheckContext, PermissionCheckMode, RoleScope, UserEffectiveRoles } from '@/schemas/auth.schema'
import { cacheService } from './cache.service'
import { userRoleService } from './user-role.service'

export class AuthorizationService {
  /**
   * 检查用户是否拥有指定权限
   */
  async checkPermission(userId: string, permission: string): Promise<boolean> {
    const userPermissions = await this.getUserEffectivePermissions(userId)
    return this.checkPermissionMatch(userPermissions, permission)
  }

  /**
   * 检查用户是否拥有多个权限
   */
  async checkPermissions(userId: string, permissions: string[], mode: PermissionCheckMode = 'ALL'): Promise<boolean> {
    const userPermissions = await this.getUserEffectivePermissions(userId)

    if (mode === 'ALL') {
      // 需要拥有所有权限
      return permissions.every((permission) => this.checkPermissionMatch(userPermissions, permission))
    } else {
      // 只需要拥有任一权限
      return permissions.some((permission) => this.checkPermissionMatch(userPermissions, permission))
    }
  }

  /**
   * 检查用户是否拥有指定角色
   */
  async checkRole(userId: string, roleCode: string, scope: RoleScope = 'ANY'): Promise<boolean> {
    switch (scope) {
      case 'GLOBAL': {
        return await userRoleService.hasGlobalRole(userId, roleCode)
      }
      case 'DEPARTMENT': {
        return await userRoleService.hasDepartmentRole(userId, roleCode)
      }
      case 'ANY':
      default: {
        const hasGlobal = await userRoleService.hasGlobalRole(userId, roleCode)
        const hasDepartment = await userRoleService.hasDepartmentRole(userId, roleCode)
        return hasGlobal || hasDepartment
      }
    }
  }

  /**
   * 检查数据权限
   */
  async checkDataPermission(
    userId: string,
    resource: string,
    action: string,
    context: PermissionCheckContext
  ): Promise<boolean> {
    // 首先检查基础权限
    const permissionCode = `${resource}:${action}`
    const hasBasicPermission = await this.checkPermission(userId, permissionCode)
    if (!hasBasicPermission) {
      return false
    }

    // 如果没有指定上下文，只检查基础权限
    if (!context.departmentId && !context.userId) {
      return true
    }

    // 检查数据范围权限
    return await this.checkDataScopePermission(userId, context)
  }

  /**
   * 获取用户数据权限范围
   */
  async getUserDataScope(userId: string): Promise<string[]> {
    const userPermissions = await this.getUserEffectivePermissions(userId)
    const dataScopes: string[] = []

    // 检查各种数据范围权限
    if (userPermissions.has('data:scope:all') || userPermissions.has('*')) {
      dataScopes.push('all')
    }
    if (userPermissions.has('data:scope:subordinate')) {
      dataScopes.push('subordinate')
    }
    if (userPermissions.has('data:scope:department')) {
      dataScopes.push('department')
    }
    if (userPermissions.has('data:scope:self')) {
      dataScopes.push('self')
    }

    return dataScopes
  }

  /**
   * 获取用户有效权限
   */
  async getUserEffectivePermissions(userId: string): Promise<Set<string>> {
    return await userRoleService.getUserEffectivePermissions(userId)
  }

  /**
   * 检查权限匹配（支持通配符和继承）
   */
  private checkPermissionMatch(userPermissions: Set<string>, requiredPermission: string): boolean {
    // 1. 完全匹配
    if (userPermissions.has(requiredPermission)) {
      return true
    }

    // 2. 超级权限
    if (userPermissions.has('*')) {
      return true
    }

    // 3. 通配符匹配
    for (const permission of userPermissions) {
      if (permission.endsWith(':*')) {
        const prefix = permission.slice(0, -1) // 移除 '*'
        if (requiredPermission.startsWith(prefix)) {
          return true
        }
      }
    }

    return false
  }

  /**
   * 检查数据范围权限
   */
  private async checkDataScopePermission(userId: string, context: PermissionCheckContext): Promise<boolean> {
    const dataScopes = await this.getUserDataScope(userId)

    // 如果有全部数据权限，直接通过
    if (dataScopes.includes('all')) {
      return true
    }

    // 检查个人数据权限
    if (context.userId && dataScopes.includes('self')) {
      if (context.userId === userId) {
        return true
      }
    }

    // 检查部门数据权限
    if (context.departmentId) {
      if (dataScopes.includes('department')) {
        return await this.checkDepartmentDataAccess(userId, context.departmentId)
      }

      if (dataScopes.includes('subordinate')) {
        return await this.checkSubordinateDataAccess(userId, context.departmentId)
      }
    }

    return false
  }

  /**
   * 检查部门数据访问权限
   */
  private async checkDepartmentDataAccess(userId: string, targetDeptId: string): Promise<boolean> {
    // 获取用户所属部门
    const userEffectiveRoles = await userRoleService.getUserEffectiveRoles(userId)
    const userDepartmentIds = userEffectiveRoles.departmentRoles.map(
      (dr: UserEffectiveRoles['departmentRoles'][number]) => dr.departmentId
    )

    // 检查是否在同一部门
    return userDepartmentIds.includes(targetDeptId)
  }

  /**
   * 检查下级部门数据访问权限
   */
  private async checkSubordinateDataAccess(userId: string, targetDeptId: string): Promise<boolean> {
    // 这里需要实现部门层级检查逻辑
    // 暂时简化为检查是否有部门管理员权限
    return await userRoleService.isDepartmentAdmin(userId, targetDeptId)
  }

  /**
   * 检查用户是否为系统管理员
   */
  async isSystemAdmin(userId: string): Promise<boolean> {
    return await userRoleService.isSystemAdmin(userId)
  }

  /**
   * 检查用户是否为部门管理员
   */
  async isDepartmentAdmin(userId: string, departmentId?: string): Promise<boolean> {
    return await userRoleService.isDepartmentAdmin(userId, departmentId)
  }

  /**
   * 获取用户可访问的资源列表
   */
  async getUserAccessibleResources(userId: string, resourceType: string): Promise<string[]> {
    const userPermissions = await this.getUserEffectivePermissions(userId)
    const accessibleResources: string[] = []

    // 检查通配符权限
    if (userPermissions.has('*') || userPermissions.has(`${resourceType}:*`)) {
      // 返回所有资源（这里需要根据具体业务实现）
      return ['*']
    }

    // 检查具体权限
    for (const permission of userPermissions) {
      if (permission.startsWith(`${resourceType}:`)) {
        const parts = permission.split(':')
        if (parts.length >= 3 && parts[2]) {
          accessibleResources.push(parts[2]) // 资源ID或类型
        }
      }
    }

    return Array.from(new Set(accessibleResources))
  }

  /**
   * 批量检查权限
   */
  async batchCheckPermissions(
    userId: string,
    permissionChecks: Array<{ permission: string; context?: PermissionCheckContext }>
  ): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {}

    for (const check of permissionChecks) {
      if (check.context) {
        const parts = check.permission.split(':')
        if (parts.length >= 2 && parts[0] && parts[1]) {
          results[check.permission] = await this.checkDataPermission(userId, parts[0], parts[1], check.context)
        } else {
          results[check.permission] = false
        }
      } else {
        results[check.permission] = await this.checkPermission(userId, check.permission)
      }
    }

    return results
  }

  /**
   * 清除用户权限缓存
   */
  async invalidateUserPermissions(userId: string): Promise<void> {
    await cacheService.invalidateUserCache(userId)
    await cacheService.invalidateUserRolesCache(userId)
  }

  /**
   * 清除多个用户的权限缓存
   */
  async invalidateUsersPermissions(userIds: string[]): Promise<void> {
    await cacheService.invalidateUsersCaches(userIds)
  }
}

// 创建单例实例
export const authorizationService = new AuthorizationService()
