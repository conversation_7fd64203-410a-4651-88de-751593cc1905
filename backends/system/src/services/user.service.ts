// 用户管理服务

import { AppException, InternalServerErrorException, NotFoundException } from '@/core/exceptions'
import { db } from '@/db'
import type { UpdateUser, UserPageQuery, UserResponse } from '@/schemas/user.schema'
import type { Any } from '@/types'
import type { Operator } from '@/utils'
import { parseOrderBy } from '@/utils/parse-order-by'
import { omit, tryit } from 'radash'

export class UserService {
  /**
   * 根据ID查找用户
   */
  async findById(id: string): Promise<UserResponse> {
    const [err, user] = await tryit(db.user.findUnique)({
      where: { id },
      include: {
        userRoles: {
          include: { role: true },
        },
        memberships: {
          include: { department: true },
        },
      },
    })

    if (err) {
      throw new InternalServerErrorException('查询用户失败')
    }

    if (!user) {
      throw new NotFoundException('用户不存在')
    }

    return user as unknown as UserResponse
  }

  /**
   * 分页查询用户
   */
  async findPage(params: UserPageQuery) {
    const { size, current, orderBy, createdAtStart, createdAtEnd, updatedAtStart, updatedAtEnd, name, ...rest } = params
    const orderByClause = parseOrderBy(orderBy)

    const where: Any = {
      ...rest,
      name: { contains: name },
      createdAt: { gte: createdAtStart, lte: createdAtEnd },
      updatedAt: { gte: updatedAtStart, lte: updatedAtEnd },
    }

    const [err, users] = await tryit(db.user.findPage.bind(db.user))(
      {
        where,
        include: {
          memberships: {
            include: { department: true },
          },
          userRoles: {
            include: { role: true },
          },
        },
      },
      { current, size, orderBy: orderByClause }
    )

    if (err) {
      throw new InternalServerErrorException('查询用户失败')
    }

    return users
  }

  /**
   * 更新用户信息
   */
  async update(id: string, data: UpdateUser, operator: Operator) {
    try {
      const old = await db.user.findUnique({ where: { id } })

      if (!old) {
        throw new NotFoundException('用户不存在')
      }

      // 如果更新邮箱，检查是否与其他用户冲突
      if (data.email && data.email !== old.email) {
        const existingUser = await db.user.findFirst({
          where: {
            email: data.email,
            id: { not: id },
          },
        })

        if (existingUser) {
          throw new AppException('邮箱已被其他用户使用')
        }
      }

      // 如果更新账号，检查是否与其他用户冲突
      if (data.account && data.account !== old.account) {
        const existingUser = await db.user.findFirst({
          where: {
            account: data.account,
            id: { not: id },
          },
        })

        if (existingUser) {
          throw new AppException('账号已被其他用户使用')
        }
      }

      return await db.user.update({
        where: { id },
        data: {
          ...omit(data, ['id']),
          updatedBy: operator.account,
          updaterName: operator.name,
        },
      })
    } catch (error) {
      if (error instanceof AppException) throw error
      throw new InternalServerErrorException('用户信息更新失败')
    }
  }

  /**
   * 删除用户
   */
  async delete(id: string, operator: Operator) {
    const [err, user] = await tryit(db.user.findUnique)({ where: { id } })

    if (err) {
      throw new InternalServerErrorException('查询用户失败')
    }

    if (!user) {
      throw new NotFoundException('用户不存在')
    }

    // 检查用户是否有关联的角色
    const userRoleCount = await db.userRole.count({
      where: {
        userId: id,
      },
    })

    if (userRoleCount > 0) {
      throw new AppException('该用户还有关联的角色，请先移除角色后再删除')
    }

    // 检查用户是否有部门成员关系
    const membershipCount = await db.departmentMember.count({
      where: {
        userId: id,
      },
    })

    if (membershipCount > 0) {
      throw new AppException('该用户还有部门成员关系，请先移除部门关系后再删除')
    }

    await db.user.delete({
      where: { id },
    })

    return id
  }

  /**
   * 根据账号查找用户
   */
  async findByAccount(account: string) {
    const [err, user] = await tryit(db.user.findFirst)({
      where: {
        account,
      },
    })

    if (err) {
      throw new InternalServerErrorException('查询用户失败')
    }

    return user
  }

  /**
   * 根据邮箱查找用户
   */
  async findByEmail(email: string) {
    const [err, user] = await tryit(db.user.findFirst)({
      where: {
        email,
      },
    })

    if (err) {
      throw new InternalServerErrorException('查询用户失败')
    }

    return user
  }

  /**
   * 检查账号是否存在
   */
  async isAccountExists(account: string, excludeId?: string): Promise<boolean> {
    const where = {
      account,
      ...(excludeId && { id: { not: excludeId } }),
    }

    const count = await db.user.count({ where })
    return count > 0
  }

  /**
   * 检查邮箱是否存在
   */
  async isEmailExists(email: string, excludeId?: string): Promise<boolean> {
    const where = {
      email,
      ...(excludeId && { id: { not: excludeId } }),
    }

    const count = await db.user.count({ where })
    return count > 0
  }

  /**
   * 批量更新用户状态
   */
  async batchUpdateStatus(ids: string[], enabled: number, operator: Operator) {
    try {
      await db.user.updateMany({
        where: {
          id: { in: ids },
        },
        data: {
          enabled,
          updatedBy: operator.account,
          updaterName: operator.name,
        },
      })

      return ids
    } catch (error) {
      throw new InternalServerErrorException('批量更新用户状态失败')
    }
  }

  /**
   * 获取用户的完整信息（包含角色和部门）
   */
  async getUserFullInfo(id: string) {
    const [err, user] = await tryit(db.user.findUnique)({
      where: { id },
      include: {
        userRoles: {
          include: {
            role: {
              include: {
                rolePermissions: {
                  include: {
                    permission: true,
                  },
                },
              },
            },
          },
        },
        memberships: {
          include: {
            department: true,
            departmentMemberRoles: {
              include: {
                role: {
                  include: {
                    rolePermissions: {
                      include: {
                        permission: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    })

    if (err) {
      throw new InternalServerErrorException('查询用户完整信息失败')
    }

    if (!user) {
      throw new NotFoundException('用户不存在')
    }

    return user
  }
}

// 创建单例实例
export const userService = new UserService()
