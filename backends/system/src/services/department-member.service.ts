// RBAC 部门成员管理服务

import { db } from '@/db'
import type { AuditFields } from '@/schemas/auth.schema'
import { DepartmentMemberType } from '@/schemas/department.schema'
import type { DepartmentMember } from '@prisma/client'

export class DepartmentMemberService {
  /**
   * 添加部门成员
   */
  async addMember(
    deptId: string,
    userId: string,
    type: DepartmentMemberType,
    auditFields: AuditFields
  ): Promise<DepartmentMember> {
    // 检查部门是否存在
    const department = await db.department.findUnique({
      where: { id: deptId },
    })
    if (!department) {
      throw new Error('Department not found')
    }

    // 检查用户是否存在
    const user = await db.user.findUnique({
      where: { id: userId },
    })
    if (!user) {
      throw new Error('User not found')
    }

    // 检查是否已经是成员
    const existingMember = await db.departmentMember.findFirst({
      where: {
        departmentId: deptId,
        userId,
      },
    })

    if (existingMember) {
      throw new Error('User is already a member of this department')
    }

    return await db.departmentMember.create({
      data: {
        departmentId: deptId,
        userId,
        type,
        ...auditFields,
      },
    })
  }

  /**
   * 移除部门成员
   */
  async removeMember(deptId: string, userId: string): Promise<void> {
    // 删除部门成员
    await db.departmentMember.deleteMany({
      where: {
        departmentId: deptId,
        userId,
      },
    })

    // 同时删除该成员在该部门的所有角色
    await db.departmentMemberRole.deleteMany({
      where: {
        userId,
        departmentId: deptId,
      },
    })
  }

  /**
   * 更新成员类型
   */
  async updateMemberType(
    deptId: string,
    userId: string,
    type: DepartmentMemberType,
    auditFields: Partial<AuditFields>
  ): Promise<DepartmentMember> {
    const member = await db.departmentMember.findFirst({
      where: {
        departmentId: deptId,
        userId,
      },
    })

    if (!member) {
      throw new Error('Member not found')
    }

    return await db.departmentMember.update({
      where: { id: member.id },
      data: {
        type,
        updatedBy: auditFields.updatedBy,
        updaterName: auditFields.updaterName,
      },
    })
  }

  /**
   * 获取部门成员列表
   */
  async getDepartmentMembers(deptId: string) {
    return await db.departmentMember.findMany({
      where: {
        departmentId: deptId,
      },
      include: {
        user: true,
      },
      orderBy: [{ type: 'asc' }, { createdAt: 'asc' }],
    })
  }

  /**
   * 获取用户所属的部门列表
   */
  async getUserDepartments(userId: string) {
    return await db.departmentMember.findMany({
      where: {
        userId,
      },
      include: {
        department: true,
      },
      orderBy: { createdAt: 'asc' },
    })
  }

  /**
   * 检查用户是否是部门成员
   */
  async isMember(userId: string, deptId: string): Promise<boolean> {
    const count = await db.departmentMember.count({
      where: {
        userId,
        departmentId: deptId,
      },
    })
    return count > 0
  }

  /**
   * 检查用户是否是部门管理员
   */
  async isDepartmentManager(userId: string, deptId: string): Promise<boolean> {
    const count = await db.departmentMember.count({
      where: {
        userId,
        departmentId: deptId,
        type: { in: ['OWNER', 'MANAGER'] },
      },
    })
    return count > 0
  }

  /**
   * 获取部门管理员列表
   */
  async getDepartmentManagers(deptId: string) {
    return await db.departmentMember.findMany({
      where: {
        departmentId: deptId,
        type: { in: ['OWNER', 'MANAGER'] },
      },
      include: {
        user: true,
      },
      orderBy: [{ type: 'asc' }, { createdAt: 'asc' }],
    })
  }

  /**
   * 获取用户管理的部门列表
   */
  async getUserManagedDepartments(userId: string) {
    return await db.departmentMember.findMany({
      where: {
        userId,
        type: { in: ['OWNER', 'MANAGER'] },
      },
      include: {
        department: true,
      },
      orderBy: { createdAt: 'asc' },
    })
  }

  /**
   * 批量添加部门成员
   */
  async addMembers(deptId: string, userIds: string[], type: DepartmentMemberType, auditFields: AuditFields): Promise<void> {
    // 检查部门是否存在
    const department = await db.department.findUnique({
      where: { id: deptId },
    })
    if (!department) {
      throw new Error('Department not found')
    }

    // 检查用户是否都存在
    const users = await db.user.findMany({
      where: {
        id: { in: userIds },
      },
    })
    if (users.length !== userIds.length) {
      throw new Error('Some users not found')
    }

    // 获取已存在的成员
    const existingMembers = await db.departmentMember.findMany({
      where: {
        departmentId: deptId,
        userId: { in: userIds },
      },
    })
    const existingUserIds = new Set(existingMembers.map((m) => m.userId))

    // 过滤出需要新增的用户
    const newUserIds = userIds.filter((id) => !existingUserIds.has(id))

    if (newUserIds.length > 0) {
      await db.departmentMember.createMany({
        data: newUserIds.map((userId) => ({
          departmentId: deptId,
          userId,
          type,
          ...auditFields,
        })),
      })
    }
  }

  /**
   * 批量移除部门成员
   */
  async removeMembers(deptId: string, userIds: string[]): Promise<void> {
    // 删除部门成员
    await db.departmentMember.deleteMany({
      where: {
        departmentId: deptId,
        userId: { in: userIds },
      },
    })

    // 同时删除这些成员在该部门的所有角色
    await db.departmentMemberRole.deleteMany({
      where: {
        userId: { in: userIds },
        departmentId: deptId,
      },
    })
  }

  /**
   * 转移部门成员到其他部门
   */
  async transferMembers(fromDeptId: string, toDeptId: string, userIds: string[], auditFields: AuditFields): Promise<void> {
    // 检查目标部门是否存在
    const toDepartment = await db.department.findUnique({
      where: { id: toDeptId },
    })
    if (!toDepartment) {
      throw new Error('Target department not found')
    }

    await db.$transaction(async (tx) => {
      // 删除原部门成员
      await tx.departmentMember.deleteMany({
        where: {
          departmentId: fromDeptId,
          userId: { in: userIds },
        },
      })

      // 删除原部门的角色
      await tx.departmentMemberRole.deleteMany({
        where: {
          userId: { in: userIds },
          departmentId: fromDeptId,
        },
      })

      // 添加到新部门（默认为普通成员）
      const existingMembers = await tx.departmentMember.findMany({
        where: {
          departmentId: toDeptId,
          userId: { in: userIds },
        },
      })
      const existingUserIds = new Set(existingMembers.map((m) => m.userId))
      const newUserIds = userIds.filter((id) => !existingUserIds.has(id))

      if (newUserIds.length > 0) {
        await tx.departmentMember.createMany({
          data: newUserIds.map((userId) => ({
            departmentId: toDeptId,
            userId,
            type: 'MEMBER' as DepartmentMemberType,
            ...auditFields,
          })),
        })
      }
    })
  }

  /**
   * 获取部门成员统计信息
   */
  async getDepartmentMemberStats(deptId: string): Promise<{
    total: number
    owners: number
    managers: number
    members: number
  }> {
    const stats = await db.departmentMember.groupBy({
      by: ['type'],
      where: {
        departmentId: deptId,
      },
      _count: {
        type: true,
      },
    })

    const result = {
      total: 0,
      owners: 0,
      managers: 0,
      members: 0,
    }

    for (const stat of stats) {
      result.total += stat._count.type
      switch (stat.type) {
        case 'OWNER':
          result.owners = stat._count.type
          break
        case 'MANAGER':
          result.managers = stat._count.type
          break
        case 'MEMBER':
          result.members = stat._count.type
          break
      }
    }

    return result
  }
}

// 创建单例实例
export const departmentMemberService = new DepartmentMemberService()
