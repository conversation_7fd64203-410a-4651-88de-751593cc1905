// 菜单管理服务

import { AppException, BadRequestException, InternalServerErrorException, NotFoundException } from '@/core/exceptions'
import { db } from '@/db'
import type { CreateMenu, MenuPageQuery, MenuQuery, MenuResponseWithChildren, UpdateMenu } from '@/schemas/menu.schema'
import type { Any } from '@/types'
import { arrayToTree, type Operator } from '@/utils'
import { parseOrderBy } from '@/utils/parse-order-by'
import { tryit } from 'radash'

export class MenuService {
  /**
   * 根据ID查找菜单（包含子菜单）
   */
  async findById(id: string): Promise<MenuResponseWithChildren> {
    const [err, menu] = await tryit(db.menu.findUnique)({ where: { id } })

    if (err) {
      throw new InternalServerErrorException('查询菜单失败')
    }

    if (!menu) {
      throw new NotFoundException('菜单不存在')
    }

    const pathPrefix = menu.treePath

    const [, menus = []] = await tryit(db.menu.findMany)({
      where: { treePath: { startsWith: pathPrefix }, id: { not: menu.id } },
    })

    const children = arrayToTree(menus, menu.id) as MenuResponseWithChildren[]

    return { ...menu, children } as unknown as MenuResponseWithChildren
  }

  /**
   * 查找菜单列表
   */
  async findList(params?: MenuQuery) {
    const [err, menus] = await tryit(db.menu.findMany)({ where: params })

    if (err) {
      throw new InternalServerErrorException('查询菜单失败')
    }

    return menus
  }

  /**
   * 分页查询菜单
   */
  async findPage(params: MenuPageQuery = {}) {
    const {
      size,
      current,
      orderBy,
      createdAtStart,
      createdAtEnd,
      updatedAtStart,
      updatedAtEnd,
      name,
      parentId,
      type,
      enabled,
    } = params
    const orderByClause = parseOrderBy(orderBy)

    const where: Any = {
      parentId,
      type,
      enabled,
      name: { contains: name },
      createdAt: { gte: createdAtStart, lte: createdAtEnd },
      updatedAt: { gte: updatedAtStart, lte: updatedAtEnd },
    }

    const [err, menus] = await tryit(db.menu.findPage.bind(db.menu))({ where }, { current, size, orderBy: orderByClause })

    if (err) {
      throw new InternalServerErrorException('查询菜单失败')
    }

    return menus
  }

  /**
   * 创建菜单
   */
  async create(data: CreateMenu, operator: Operator) {
    try {
      const ret = await db.$transaction(async (ts) => {
        // 检查菜单路径是否已存在
        const existingMenu = await ts.menu.findFirst({
          where: {
            path: data.path,
          },
        })

        if (existingMenu) {
          throw new BadRequestException('菜单路径已存在')
        }

        const parentId = data.parentId
        let parentPath = ''

        // 检查父菜单是否存在
        if (parentId) {
          const parent = await ts.menu.findUnique({ where: { id: parentId } })
          if (!parent) {
            throw new BadRequestException('父级菜单不存在')
          }
          parentPath = parent.treePath
        }

        const menu = await ts.menu.create({
          data: {
            ...data,
            treePath: '',
            createdBy: operator.account,
            creatorName: operator.name,
            updatedBy: operator.account,
            updaterName: operator.name,
          },
        })

        return await ts.menu.update({
          where: { id: menu.id },
          data: { treePath: `${parentPath}/${menu.id}` },
        })
      })

      return ret
    } catch (error) {
      if (error instanceof AppException) throw error
      throw new InternalServerErrorException('创建菜单失败')
    }
  }

  /**
   * 更新菜单
   */
  async update(id: string, data: UpdateMenu, operator: Operator) {
    try {
      const old = await db.menu.findUnique({ where: { id } })

      if (!old) {
        throw new NotFoundException('菜单不存在')
      }

      // 如果更新路径，检查是否与其他菜单冲突
      if (data.path && data.path !== old.path) {
        const existingMenu = await db.menu.findFirst({
          where: {
            path: data.path,
            id: { not: id },
          },
        })

        if (existingMenu) {
          throw new BadRequestException('菜单路径已存在')
        }
      }

      let treePath = old.treePath

      if (data.parentId && old.parentId?.toString() !== data.parentId.toString()) {
        const parent = await db.menu.findFirst({ where: { id: data.parentId } })
        if (!parent) {
          throw new BadRequestException('父级菜单不存在')
        }
        treePath = `${parent.treePath}/${old.id}`
      }

      return await db.menu.update({
        where: { id },
        data: {
          ...data,
          treePath,
          updatedBy: operator.account,
          updaterName: operator.name,
        },
      })
    } catch (error) {
      if (error instanceof AppException) throw error
      throw new InternalServerErrorException('菜单更新失败')
    }
  }

  /**
   * 删除菜单
   */
  async delete(id: string, operator: Operator) {
    const [err, menu] = await tryit(db.menu.findUnique)({ where: { id } })

    if (err) {
      throw new InternalServerErrorException('查询菜单失败')
    }

    if (!menu) {
      throw new NotFoundException('菜单不存在')
    }

    // 检查是否有子菜单
    const childrenCount = await db.menu.count({
      where: {
        parentId: id,
      },
    })

    if (childrenCount > 0) {
      throw new BadRequestException('该菜单下有子菜单，无法删除')
    }

    // 检查是否有角色正在使用该菜单
    const roleMenuCount = await db.roleMenu.count({
      where: {
        menuId: id,
      },
    })

    if (roleMenuCount > 0) {
      throw new BadRequestException('该菜单正在被角色使用，无法删除')
    }

    // 软删除
    const ret = await db.menu.delete({
      where: { id },
    })

    return ret.id
  }

  /**
   * 获取菜单树结构
   */
  async getMenuTree(): Promise<MenuResponseWithChildren[]> {
    const [err, menus] = await tryit(db.menu.findMany)({
      where: {
        enabled: 1,
      },
      include: {
        microApp: true,
      },
      orderBy: [{ sort: 'asc' }, { createdAt: 'asc' }],
    })

    if (err) {
      throw new InternalServerErrorException('查询菜单失败')
    }

    return arrayToTree(menus, null) as MenuResponseWithChildren[]
  }

  /**
   * 根据路径查找菜单
   */
  async findByPath(path: string) {
    const [err, menu] = await tryit(db.menu.findFirst)({
      where: {
        path,
      },
    })

    if (err) {
      throw new InternalServerErrorException('查询菜单失败')
    }

    return menu
  }

  /**
   * 检查菜单路径是否存在
   */
  async isPathExists(path: string, excludeId?: string): Promise<boolean> {
    const where = {
      path,
      ...(excludeId && { id: { not: excludeId } }),
    }

    const count = await db.menu.count({ where })
    return count > 0
  }

  /**
   * 获取启用的菜单列表
   */
  async getEnabledMenus() {
    const [err, menus] = await tryit(db.menu.findMany)({
      where: {
        enabled: 1,
      },
      include: {
        microApp: true,
      },
      orderBy: [{ sort: 'asc' }, { createdAt: 'asc' }],
    })

    if (err) {
      throw new InternalServerErrorException('查询菜单失败')
    }

    return menus
  }

  /**
   * 批量更新菜单状态
   */
  async batchUpdateStatus(ids: string[], enabled: number, operator: Operator) {
    try {
      await db.menu.updateMany({
        where: {
          id: { in: ids },
        },
        data: {
          enabled,
          updatedBy: operator.account,
          updaterName: operator.name,
        },
      })

      return ids
    } catch (error) {
      throw new InternalServerErrorException('批量更新菜单状态失败')
    }
  }
}

// 创建单例实例
export const menuService = new MenuService()
