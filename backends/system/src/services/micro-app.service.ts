import { AppException, InternalServerErrorException, NotFoundException } from '@/core/exceptions'
import { tryit } from 'radash'

import { db } from '@/db'
import { CreateMicroApp, MicroAppPageQuery, MicroAppQuery, UpdateMicroApp } from '@/schemas/micro-app.schema'
import { Any } from '@/types'
import { Operator } from '@/utils'
import { parseOrderBy } from '@/utils/parse-order-by'

// 微应用管理服务
export class MicroAppService {
  /**
   * 根据ID查找微应用
   */
  async findById(id: string) {
    const [err, microApp] = await tryit(db.microApp.findUnique)({ where: { id } })

    if (err) {
      throw new InternalServerErrorException('查询应用失败')
    }

    if (!microApp) {
      throw new NotFoundException('应用不存在')
    }

    return microApp
  }

  /**
   * 查找微应用列表
   */
  async findList(params?: MicroAppQuery) {
    const [err, microApps] = await tryit(db.microApp.findMany)({ where: params })

    if (err) {
      throw new InternalServerErrorException('查询应用失败')
    }

    return microApps
  }

  /**
   * 分页查询微应用
   */
  async findPage(params: MicroAppPageQuery) {
    const {
      size,
      current,
      orderBy,
      createdAtStart,
      createdAtEnd,
      updatedAtStart,
      updatedAtEnd,
      name,
      code,
      authType,
      enabled,
    } = params
    const orderByClause = parseOrderBy(orderBy)

    const where: Any = {
      authType,
      enabled,
      code: { contains: code },
      name: { contains: name },
      createdAt: { gte: createdAtStart, lte: createdAtEnd },
      updatedAt: { gte: updatedAtStart, lte: updatedAtEnd },
    }

    const [err, microApps] = await tryit(db.microApp.findPage.bind(db.microApp))(
      { where },
      { current, size, orderBy: orderByClause }
    )

    if (err) {
      throw new InternalServerErrorException('查询应用失败')
    }

    return microApps
  }

  /**
   * 创建微应用
   */
  async create(data: CreateMicroApp, operator: Operator) {
    try {
      // 检查应用编码是否已存在
      const existed = await db.microApp.findFirst({
        where: {
          code: data.code,
        },
      })

      if (existed) {
        throw new AppException('应用编码已存在')
      }

      // 创建应用
      const microApp = await db.microApp.create({
        data: {
          ...data,
          createdBy: operator.account,
          creatorName: operator.name,
          updatedBy: operator.account,
          updaterName: operator.name,
        },
      })

      return microApp
    } catch (error) {
      if (error instanceof AppException) throw error
      throw new InternalServerErrorException('创建应用失败')
    }
  }

  /**
   * 更新微应用
   */
  async update(id: string, data: UpdateMicroApp, operator: Operator) {
    try {
      const old = await db.microApp.findUnique({ where: { id } })

      if (!old) {
        throw new NotFoundException('应用不存在')
      }

      // 如果更新编码，检查是否与其他应用冲突
      if (data.code && data.code !== old.code) {
        const existed = await db.microApp.findFirst({
          where: {
            code: data.code,
            id: { not: id },
          },
        })

        if (existed) {
          throw new AppException('应用编码已存在')
        }
      }

      return await db.microApp.update({
        where: { id },
        data: {
          ...data,
          updatedBy: operator.account,
          updaterName: operator.name,
        },
      })
    } catch (error) {
      if (error instanceof AppException) throw error
      throw new InternalServerErrorException('应用信息更新失败')
    }
  }

  /**
   * 删除微应用
   */
  async delete(id: string, operator: Operator) {
    const [err, microApp] = await tryit(db.microApp.findUnique)({ where: { id } })

    if (err) {
      throw new InternalServerErrorException('查询应用失败')
    }

    if (!microApp) {
      throw new NotFoundException('应用不存在')
    }

    // 检查是否有菜单正在使用该微应用
    const menuCount = await db.menu.count({
      where: {
        microId: id,
      },
    })

    if (menuCount > 0) {
      throw new AppException('该应用正在被菜单使用，无法删除')
    }

    await db.microApp.delete({
      where: { id },
    })

    return id
  }

  /**
   * 根据编码查找微应用
   */
  async findByCode(code: string) {
    const [err, microApp] = await tryit(db.microApp.findFirst)({
      where: {
        code,
      },
    })

    if (err) {
      throw new InternalServerErrorException('查询应用失败')
    }

    return microApp
  }

  /**
   * 检查应用编码是否存在
   */
  async isCodeExists(code: string, excludeId?: string): Promise<boolean> {
    const where = {
      code,
      ...(excludeId && { id: { not: excludeId } }),
    }

    const count = await db.microApp.count({ where })
    return count > 0
  }

  /**
   * 获取启用的微应用列表
   */
  async getEnabledApps() {
    const [err, microApps] = await tryit(db.microApp.findMany)({
      where: {
        enabled: 1,
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    if (err) {
      throw new InternalServerErrorException('查询应用失败')
    }

    return microApps
  }

  /**
   * 批量更新微应用状态
   */
  async batchUpdateStatus(ids: string[], enabled: number, operator: Operator) {
    try {
      await db.microApp.updateMany({
        where: {
          id: { in: ids },
        },
        data: {
          enabled,
          updatedBy: operator.account,
          updaterName: operator.name,
        },
      })

      return ids
    } catch (error) {
      throw new InternalServerErrorException('批量更新应用状态失败')
    }
  }
}

// 创建单例实例
export const microAppService = new MicroAppService()
