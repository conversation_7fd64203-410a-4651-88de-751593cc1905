// RBAC 用户角色关联服务

import { db } from '@/db'
import type { AuditFields, UserEffectiveRoles } from '@/schemas/auth.schema'
import type { Role } from '@prisma/client'
import { cacheService } from './cache.service'
import { roleService } from './role.service'

export class UserRoleService {
  /**
   * 分配全局角色给用户
   */
  async assignGlobalRoles(userId: string, roleIds: string[], auditFields: AuditFields): Promise<void> {
    // 验证用户是否存在
    const user = await db.user.findUnique({
      where: { id: userId },
    })
    if (!user) {
      throw new Error('User not found')
    }

    // 验证角色是否都存在且启用
    const roles = await db.role.findMany({
      where: {
        id: { in: roleIds },
        enabled: 1,
      },
    })
    if (roles.length !== roleIds.length) {
      throw new Error('Some roles not found or disabled')
    }

    // 获取已有的角色分配
    const existingUserRoles = await db.userRole.findMany({
      where: {
        userId,
        roleId: { in: roleIds },
      },
    })
    const existingRoleIds = new Set(existingUserRoles.map((ur) => ur.roleId))

    // 过滤出需要新增的角色ID
    const newRoleIds = roleIds.filter((id) => !existingRoleIds.has(id))

    // 批量创建新的角色分配
    if (newRoleIds.length > 0) {
      await db.userRole.createMany({
        data: newRoleIds.map((roleId) => ({
          userId,
          roleId,
          ...auditFields,
        })),
      })
    }

    // 清除用户缓存
    await cacheService.invalidateUserCache(userId)
    await cacheService.invalidateUserRolesCache(userId)
  }

  /**
   * 移除用户的全局角色
   */
  async removeGlobalRoles(userId: string, roleIds: string[]): Promise<void> {
    await db.userRole.deleteMany({
      where: {
        userId,
        roleId: { in: roleIds },
      },
    })

    // 清除用户缓存
    await cacheService.invalidateUserCache(userId)
    await cacheService.invalidateUserRolesCache(userId)
  }

  /**
   * 替换用户的全局角色
   */
  async replaceGlobalRoles(userId: string, roleIds: string[], auditFields: AuditFields): Promise<void> {
    // 删除现有角色
    await db.userRole.deleteMany({
      where: { userId },
    })

    // 添加新角色
    if (roleIds.length > 0) {
      await this.assignGlobalRoles(userId, roleIds, auditFields)
    }
  }

  /**
   * 获取用户的全局角色
   */
  async getUserGlobalRoles(userId: string): Promise<Role[]> {
    // 尝试从缓存获取
    const cached = await cacheService.getUserRolesCache(userId)
    if (cached) return cached

    const userRoles = await db.userRole.findMany({
      where: {
        userId,
      },
      include: {
        role: true,
      },
    })

    const roles = userRoles.map((ur) => ur.role).filter((r) => r !== null && r.enabled === 1) as Role[]

    // 缓存结果
    await cacheService.setUserRolesCache(userId, roles)

    return roles
  }

  /**
   * 获取用户的全局角色权限
   */
  async getUserGlobalPermissions(userId: string): Promise<string[]> {
    const globalRoles = await this.getUserGlobalRoles(userId)
    const permissions: string[] = []

    for (const role of globalRoles) {
      const rolePermissions = await roleService.getRoleEffectivePermissions(role.id)
      permissions.push(...rolePermissions.map((p) => p.code))
    }

    // 去重
    return Array.from(new Set(permissions))
  }

  /**
   * 获取用户的有效角色（全局角色 + 部门角色）
   */
  async getUserEffectiveRoles(userId: string): Promise<UserEffectiveRoles> {
    // 获取全局角色
    const globalRoles = await this.getUserGlobalRoles(userId)

    // 获取部门角色
    const departmentRoles = await db.departmentMemberRole.findMany({
      where: {
        userId,
      },
      include: {
        role: true,
        member: {
          include: {
            department: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    })

    // 按部门分组角色
    const departmentRoleMap = new Map<string, { departmentId: string; departmentName: string; roles: Role[] }>()

    for (const deptRole of departmentRoles) {
      if (!deptRole.role || !deptRole.member.department) continue
      if (deptRole.role.enabled !== 1) continue

      const deptId = deptRole.member.department.id
      const deptName = deptRole.member.department.name

      if (!departmentRoleMap.has(deptId)) {
        departmentRoleMap.set(deptId, {
          departmentId: deptId,
          departmentName: deptName,
          roles: [],
        })
      }

      departmentRoleMap.get(deptId)!.roles.push(deptRole.role)
    }

    return {
      globalRoles,
      departmentRoles: Array.from(departmentRoleMap.values()),
    }
  }

  /**
   * 获取用户的所有有效权限（全局权限 + 部门权限）
   */
  async getUserEffectivePermissions(userId: string): Promise<Set<string>> {
    // 尝试从缓存获取
    const cached = await cacheService.getUserPermissionsCache(userId)
    if (cached) return cached

    // 获取全局权限
    const globalPermissions = await this.getUserGlobalPermissions(userId)

    // 获取部门权限
    const departmentPermissions = await this.getUserDepartmentPermissions(userId)

    // 合并权限
    const allPermissions = new Set([...globalPermissions, ...departmentPermissions])

    // 缓存结果
    await cacheService.setUserPermissionsCache(userId, Array.from(allPermissions))

    return allPermissions
  }

  /**
   * 获取用户的部门权限
   */
  async getUserDepartmentPermissions(userId: string): Promise<string[]> {
    const departmentRoles = await db.departmentMemberRole.findMany({
      where: {
        userId,
      },
      include: {
        role: true,
      },
    })

    const permissions: string[] = []

    for (const deptRole of departmentRoles) {
      if (!deptRole.role || deptRole.role.enabled !== 1) continue

      const rolePermissions = await roleService.getRoleEffectivePermissions(deptRole.role.id)
      permissions.push(...rolePermissions.map((p) => p.code))
    }

    // 去重
    return Array.from(new Set(permissions))
  }

  /**
   * 检查用户是否拥有指定的全局角色
   */
  async hasGlobalRole(userId: string, roleCode: string): Promise<boolean> {
    const globalRoles = await this.getUserGlobalRoles(userId)
    return globalRoles.some((role) => role.code === roleCode)
  }

  /**
   * 检查用户是否拥有指定的部门角色
   */
  async hasDepartmentRole(userId: string, roleCode: string, departmentId?: string): Promise<boolean> {
    const whereClause = {
      userId,
      role: {
        code: roleCode,
        enabled: 1,
      },
      ...(departmentId && { departmentId }),
    }

    const count = await db.departmentMemberRole.count({ where: whereClause })
    return count > 0
  }

  /**
   * 获取拥有指定角色的用户列表
   */
  async getUsersByRole(roleCode: string): Promise<string[]> {
    const role = await roleService.getRoleByCode(roleCode)
    if (!role) return []

    const userRoles = await db.userRole.findMany({
      where: {
        roleId: role.id,
      },
      select: {
        userId: true,
      },
    })

    return userRoles.map((ur) => ur.userId)
  }

  /**
   * 获取用户在指定部门的角色
   */
  async getUserDepartmentRoles(userId: string, departmentId: string): Promise<Role[]> {
    const departmentRoles = await db.departmentMemberRole.findMany({
      where: {
        userId,
        departmentId,
      },
      include: {
        role: true,
      },
    })

    return departmentRoles.map((dr) => dr.role).filter((r) => r !== null && r.enabled === 1) as Role[]
  }

  /**
   * 检查用户是否有系统管理员权限
   */
  async isSystemAdmin(userId: string): Promise<boolean> {
    return await this.hasGlobalRole(userId, 'SUPER_ADMIN')
  }

  /**
   * 检查用户是否有部门管理员权限
   */
  async isDepartmentAdmin(userId: string, departmentId?: string): Promise<boolean> {
    return (
      (await this.hasGlobalRole(userId, 'SUPER_ADMIN')) ||
      (await this.hasDepartmentRole(userId, 'DEPT_ADMIN', departmentId)) ||
      (await this.hasDepartmentRole(userId, 'SUB_DEPT_ADMIN', departmentId))
    )
  }
}

// 创建单例实例
export const userRoleService = new UserRoleService()
