// 权限缓存服务
// 使用内存缓存作为简单实现，生产环境可替换为 Redis

import type { CacheOptions } from '@/schemas/auth.schema'
import type { Permission, Role } from '@prisma/client'

interface CacheItem<T> {
  data: T
  expiredAt: number
}

export class CacheService {
  private cache = new Map<string, CacheItem<unknown>>()
  private defaultTTL = 300 // 5分钟

  /**
   * 获取用户权限缓存
   */
  async getUserPermissionsCache(userId: string): Promise<Set<string> | null> {
    const key = `user:permissions:${userId}`
    const item = this.cache.get(key)

    if (!item || Date.now() > item.expiredAt) {
      this.cache.delete(key)
      return null
    }

    return new Set(item.data as string[])
  }

  /**
   * 设置用户权限缓存
   */
  async setUserPermissionsCache(userId: string, permissions: string[], options: CacheOptions = {}): Promise<void> {
    const key = `user:permissions:${userId}`
    const ttl = options.ttl ?? this.defaultTTL
    const expiredAt = Date.now() + ttl * 1000

    this.cache.set(key, {
      data: permissions,
      expiredAt,
    })
  }

  /**
   * 清除用户权限缓存
   */
  async invalidateUserCache(userId: string): Promise<void> {
    const key = `user:permissions:${userId}`
    this.cache.delete(key)
  }

  /**
   * 获取角色权限缓存
   */
  async getRolePermissionsCache(roleId: string): Promise<Permission[] | null> {
    const key = `role:permissions:${roleId}`
    const item = this.cache.get(key)

    if (!item || Date.now() > item.expiredAt) {
      this.cache.delete(key)
      return null
    }

    return item.data as Permission[]
  }

  /**
   * 设置角色权限缓存
   */
  async setRolePermissionsCache(roleId: string, permissions: Permission[], options: CacheOptions = {}): Promise<void> {
    const key = `role:permissions:${roleId}`
    const ttl = options.ttl ?? this.defaultTTL * 6 // 角色权限缓存时间更长
    const expiredAt = Date.now() + ttl * 1000

    this.cache.set(key, {
      data: permissions,
      expiredAt,
    })
  }

  /**
   * 清除角色权限缓存
   */
  async invalidateRoleCache(roleId: string): Promise<void> {
    const key = `role:permissions:${roleId}`
    this.cache.delete(key)
  }

  /**
   * 获取用户角色缓存
   */
  async getUserRolesCache(userId: string): Promise<Role[] | null> {
    const key = `user:roles:${userId}`
    const item = this.cache.get(key)

    if (!item || Date.now() > item.expiredAt) {
      this.cache.delete(key)
      return null
    }

    return item.data as Role[]
  }

  /**
   * 设置用户角色缓存
   */
  async setUserRolesCache(userId: string, roles: Role[], options: CacheOptions = {}): Promise<void> {
    const key = `user:roles:${userId}`
    const ttl = options.ttl ?? this.defaultTTL
    const expiredAt = Date.now() + ttl * 1000

    this.cache.set(key, {
      data: roles,
      expiredAt,
    })
  }

  /**
   * 清除用户角色缓存
   */
  async invalidateUserRolesCache(userId: string): Promise<void> {
    const key = `user:roles:${userId}`
    this.cache.delete(key)
  }

  /**
   * 批量清除用户缓存
   */
  async invalidateUsersCaches(userIds: string[]): Promise<void> {
    for (const userId of userIds) {
      await this.invalidateUserCache(userId)
      await this.invalidateUserRolesCache(userId)
    }
  }

  /**
   * 批量清除角色缓存
   */
  async invalidateRolesCaches(roleIds: string[]): Promise<void> {
    for (const roleId of roleIds) {
      await this.invalidateRoleCache(roleId)
    }

    // 角色权限变更时，需要清除所有用户权限缓存
    await this.invalidateAllUserPermissions()
  }

  /**
   * 清除所有用户权限缓存
   */
  async invalidateAllUserPermissions(): Promise<void> {
    const keysToDelete: string[] = []

    for (const key of this.cache.keys()) {
      if (key.startsWith('user:permissions:') || key.startsWith('user:roles:')) {
        keysToDelete.push(key)
      }
    }

    for (const key of keysToDelete) {
      this.cache.delete(key)
    }
  }

  /**
   * 清除所有缓存
   */
  async clearAll(): Promise<void> {
    this.cache.clear()
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    const now = Date.now()
    let validCount = 0
    let expiredCount = 0

    for (const item of this.cache.values()) {
      if (now > item.expiredAt) {
        expiredCount++
      } else {
        validCount++
      }
    }

    return {
      total: this.cache.size,
      valid: validCount,
      expired: expiredCount,
    }
  }

  /**
   * 清理过期缓存
   */
  cleanupExpired(): void {
    const now = Date.now()
    const keysToDelete: string[] = []

    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiredAt) {
        keysToDelete.push(key)
      }
    }

    for (const key of keysToDelete) {
      this.cache.delete(key)
    }
  }
}

// 创建单例实例
export const cacheService = new CacheService()

// 定期清理过期缓存
setInterval(() => {
  cacheService.cleanupExpired()
}, 60000) // 每分钟清理一次
