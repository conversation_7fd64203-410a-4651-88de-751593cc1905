// RBAC 权限管理服务

import { db } from '@/db'
import type { AuditFields, PaginatedResult } from '@/schemas/auth.schema'
import type { CreatePermission, PermissionQuery, PermissionTreeResponse, UpdatePermission } from '@/schemas/permission.schema'
import type { Permission } from '@prisma/client'

export class PermissionService {
  /**
   * 创建权限
   */
  async createPermission(data: CreatePermission, auditFields: AuditFields): Promise<Permission> {
    return await db.permission.create({
      data: {
        ...data,
        ...auditFields,
      },
    })
  }

  /**
   * 更新权限
   */
  async updatePermission(id: string, data: UpdatePermission, auditFields: Partial<AuditFields>): Promise<Permission> {
    return await db.permission.update({
      where: { id },
      data: {
        ...data,
        updatedBy: auditFields.updatedBy,
        updaterName: auditFields.updaterName,
      },
    })
  }

  /**
   * 删除权限
   */
  async deletePermission(id: string): Promise<void> {
    await db.permission.delete({
      where: { id },
    })
  }

  /**
   * 根据ID获取权限
   */
  async getPermissionById(id: string): Promise<Permission | null> {
    return await db.permission.findUnique({
      where: { id },
    })
  }

  /**
   * 根据编码获取权限
   */
  async getPermissionByCode(code: string): Promise<Permission | null> {
    return await db.permission.findFirst({
      where: {
        code,
      },
    })
  }

  /**
   * 获取权限列表（分页）
   */
  async getPermissions(query: PermissionQuery & { page?: number; pageSize?: number }): Promise<PaginatedResult<Permission>> {
    const { page = 1, pageSize = 20, code, name, category, resource, action } = query

    const where = {
      ...(code && { code: { contains: code } }),
      ...(name && { name: { contains: name } }),
      ...(category && { category }),
      ...(resource && { resource }),
      ...(action && { action }),
    }

    const [data, total] = await Promise.all([
      db.permission.findMany({
        where,
        skip: (page - 1) * pageSize,
        take: pageSize,
        orderBy: [{ category: 'asc' }, { resource: 'asc' }, { action: 'asc' }, { code: 'asc' }],
      }),
      db.permission.count({ where }),
    ])

    return {
      data,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    }
  }

  /**
   * 根据分类获取权限
   */
  async getPermissionsByCategory(category: string): Promise<Permission[]> {
    return await db.permission.findMany({
      where: {
        category,
      },
      orderBy: [{ resource: 'asc' }, { action: 'asc' }, { code: 'asc' }],
    })
  }

  /**
   * 获取权限树结构
   */
  async getPermissionTree(): Promise<PermissionTreeResponse[]> {
    const permissions = await db.permission.findMany({
      orderBy: [{ category: 'asc' }, { resource: 'asc' }, { action: 'asc' }, { code: 'asc' }],
    })

    return this.buildPermissionTree(permissions)
  }

  /**
   * 构建权限树结构
   */
  private buildPermissionTree(permissions: Permission[]): PermissionTreeResponse[] {
    const tree: PermissionTreeResponse[] = []
    const categoryMap = new Map<string, PermissionTreeResponse>()
    const resourceMap = new Map<string, PermissionTreeResponse>()

    for (const permission of permissions) {
      const { category, resource } = permission

      // 创建分类节点
      if (category && !categoryMap.has(category)) {
        const categoryNode: PermissionTreeResponse = {
          id: `category-${category}`,
          code: category,
          name: this.getCategoryDisplayName(category),
          category,
          resource: null,
          action: null,
          description: `${this.getCategoryDisplayName(category)}权限分类`,
          createdBy: 'system',
          creatorName: 'system',
          createdAt: new Date(),
          updatedBy: 'system',
          updaterName: 'system',
          updatedAt: new Date(),
          children: [],
        }
        categoryMap.set(category, categoryNode)
        tree.push(categoryNode)
      }

      // 创建资源节点
      const resourceKey = `${category}-${resource}`
      if (resource && !resourceMap.has(resourceKey)) {
        const resourceNode: PermissionTreeResponse = {
          id: `resource-${resourceKey}`,
          code: `${category}:${resource}`,
          name: this.getResourceDisplayName(resource),
          category,
          resource,
          action: null,
          description: `${this.getResourceDisplayName(resource)}资源权限`,
          createdBy: 'system',
          creatorName: 'system',
          createdAt: new Date(),
          updatedBy: 'system',
          updaterName: 'system',
          updatedAt: new Date(),
          children: [],
        }
        resourceMap.set(resourceKey, resourceNode)

        // 添加到分类节点下
        const categoryNode = categoryMap.get(category!)
        if (categoryNode) {
          categoryNode.children = categoryNode.children || []
          categoryNode.children.push(resourceNode)
        }
      }

      // 添加具体权限到资源节点下
      const resourceNode = resourceMap.get(resourceKey)
      if (resourceNode) {
        resourceNode.children = resourceNode.children || []
        resourceNode.children.push({
          ...permission,
          children: [],
        })
      } else if (category) {
        // 如果没有资源，直接添加到分类下
        const categoryNode = categoryMap.get(category)
        if (categoryNode) {
          categoryNode.children = categoryNode.children || []
          categoryNode.children.push({
            ...permission,
            children: [],
          })
        }
      } else {
        // 没有分类的权限直接添加到根级
        tree.push({
          ...permission,
          children: [],
        })
      }
    }

    return tree
  }

  /**
   * 获取分类显示名称
   */
  private getCategoryDisplayName(category: string): string {
    const categoryNames: Record<string, string> = {
      system: '系统管理',
      business: '业务管理',
      data: '数据权限',
      menu: '菜单权限',
    }
    return categoryNames[category] || category
  }

  /**
   * 获取资源显示名称
   */
  private getResourceDisplayName(resource: string): string {
    const resourceNames: Record<string, string> = {
      user: '用户管理',
      role: '角色管理',
      permission: '权限管理',
      department: '部门管理',
      menu: '菜单管理',
      project: '项目管理',
      task: '任务管理',
      scope: '数据范围',
    }
    return resourceNames[resource] || resource
  }

  /**
   * 批量创建权限
   */
  async createPermissionsBatch(permissions: CreatePermission[], auditFields: AuditFields): Promise<Permission[]> {
    const data = permissions.map((permission) => ({
      ...permission,
      ...auditFields,
    }))

    await db.permission.createMany({
      data,
      skipDuplicates: true,
    })

    // 返回创建的权限
    const codes = permissions.map((p) => p.code)
    return await db.permission.findMany({
      where: {
        code: { in: codes },
      },
    })
  }

  /**
   * 检查权限编码是否存在
   */
  async isPermissionCodeExists(code: string, excludeId?: string): Promise<boolean> {
    const where = {
      code,
      ...(excludeId && { id: { not: excludeId } }),
    }

    const count = await db.permission.count({ where })
    return count > 0
  }

  /**
   * 获取所有权限编码
   */
  async getAllPermissionCodes(): Promise<string[]> {
    const permissions = await db.permission.findMany({
      select: { code: true },
    })
    return permissions.map((p) => p.code)
  }

  /**
   * 根据权限编码批量获取权限
   */
  async getPermissionsByCodes(codes: string[]): Promise<Permission[]> {
    return await db.permission.findMany({
      where: {
        code: { in: codes },
      },
    })
  }
}

// 创建单例实例
export const permissionService = new PermissionService()
