// RBAC 菜单权限服务

import type { Menu, MicroApp } from '@prisma/client'
import { db } from '../db'
import { MicroAppResponse } from '../schemas/micro-app.schema'
import { userRoleService } from './user-role.service'

// 菜单树节点类型 - 基于 Prisma 生成的 Menu 类型扩展
interface MenuTreeNode extends Menu {
  microApp?: {
    id: string
    name: string
    code: string
    url: string
  } | null
  children?: MenuTreeNode[]
}

// 数据库查询返回的菜单类型
interface MenuWithMicroApp extends Menu {
  microApp?: MicroApp | null
}

export class MenuPermissionService {
  /**
   * 为角色分配菜单权限
   */
  async assignMenusToRole(roleId: string, menuIds: string[]): Promise<void> {
    // 验证角色是否存在
    const role = await db.role.findUnique({
      where: { id: roleId },
    })
    if (!role) {
      throw new Error('Role not found')
    }

    // 验证菜单是否都存在
    const menus = await db.menu.findMany({
      where: {
        id: { in: menuIds },
      },
    })
    if (menus.length !== menuIds.length) {
      throw new Error('Some menus not found')
    }

    // 删除现有的菜单权限
    await db.roleMenu.deleteMany({
      where: { roleId },
    })

    // 创建新的菜单权限
    if (menuIds.length > 0) {
      await db.roleMenu.createMany({
        data: menuIds.map((menuId) => ({
          roleId,
          menuId,
          createdBy: 'system',
          creatorName: 'system',
          updatedBy: 'system',
          updaterName: 'system',
        })),
      })
    }
  }

  /**
   * 从角色移除菜单权限
   */
  async removeMenusFromRole(roleId: string, menuIds: string[]): Promise<void> {
    await db.roleMenu.deleteMany({
      where: {
        roleId,
        menuId: { in: menuIds },
      },
    })
  }

  /**
   * 获取角色的菜单权限
   */
  async getRoleMenus(roleId: string) {
    return await db.roleMenu.findMany({
      where: {
        roleId,
      },
      include: {
        menu: {
          include: {
            microApp: true,
          },
        },
      },
      orderBy: {
        createdAt: 'asc',
      },
    })
  }

  /**
   * 获取用户可访问的菜单
   */
  async getUserMenus(userId: string): Promise<MenuTreeNode[]> {
    // 1. 获取用户所有角色
    const userEffectiveRoles = await userRoleService.getUserEffectiveRoles(userId)
    const allRoles = [...userEffectiveRoles.globalRoles, ...userEffectiveRoles.departmentRoles.flatMap((dr) => dr.roles)]
    const roleIds = allRoles.map((role) => role.id)

    if (roleIds.length === 0) {
      return []
    }

    // 2. 查询角色关联的菜单
    const menus = await db.menu.findMany({
      where: {
        enabled: 1,
        hiddenInMenu: 0,
        roleMenus: {
          some: {
            roleId: { in: roleIds },
          },
        },
      },
      include: {
        microApp: true,
      },
      orderBy: { sort: 'asc' },
    })

    // 3. 构建菜单树
    return this.buildMenuTree(menus)
  }

  /**
   * 检查用户菜单访问权限
   */
  async checkMenuAccess(userId: string, menuPath: string): Promise<boolean> {
    const userMenus = await this.getUserMenus(userId)

    // 递归检查菜单路径
    function hasMenuPath(menus: MenuTreeNode[], path: string): boolean {
      return menus.some((menu: MenuTreeNode) => {
        if (menu.path === path) return true
        if (menu.children && menu.children.length > 0) {
          return hasMenuPath(menu.children, path)
        }
        return false
      })
    }

    return hasMenuPath(userMenus, menuPath)
  }

  /**
   * 检查用户是否可以访问指定菜单ID
   */
  async checkMenuIdAccess(userId: string, menuId: string): Promise<boolean> {
    // 获取用户所有角色
    const userEffectiveRoles = await userRoleService.getUserEffectiveRoles(userId)
    const allRoles = [...userEffectiveRoles.globalRoles, ...userEffectiveRoles.departmentRoles.flatMap((dr) => dr.roles)]
    const roleIds = allRoles.map((role) => role.id)

    if (roleIds.length === 0) {
      return false
    }

    // 检查是否有该菜单的权限
    const count = await db.roleMenu.count({
      where: {
        roleId: { in: roleIds },
        menuId,
      },
    })

    return count > 0
  }

  /**
   * 获取用户可访问的微应用列表
   * 直接通过角色权限查询，避免通过菜单收集的低效方式
   */
  async getUserMicroApps(userId: string): Promise<MicroAppResponse[]> {
    // 1. 获取用户所有角色
    const userEffectiveRoles = await userRoleService.getUserEffectiveRoles(userId)
    const allRoles = [...userEffectiveRoles.globalRoles, ...userEffectiveRoles.departmentRoles.flatMap((dr) => dr.roles)]
    const roleIds = allRoles.map((role) => role.id)

    if (roleIds.length === 0) {
      return []
    }

    // 2. 通过角色菜单权限查询用户可访问的微应用
    const microApps = await db.microApp.findMany({
      where: {
        enabled: 1,
        menus: {
          some: {
            enabled: 1,
            roleMenus: {
              some: {
                roleId: { in: roleIds },
              },
            },
          },
        },
      },
      orderBy: { sort: 'asc' },
    })

    // 3. 去重（一个微应用可能被多个菜单关联）
    const uniqueMicroApps = microApps.filter((app, index, self) => index === self.findIndex((a) => a.id === app.id))

    // 4. 转换为响应格式（排除 deletedAt 字段）
    return uniqueMicroApps.map(({ deletedAt: _, ...app }) => app)
  }

  /**
   * 构建菜单树结构
   */
  private buildMenuTree(menus: MenuWithMicroApp[], parentId: string | null = null): MenuTreeNode[] {
    const children = menus.filter((menu) => menu.parentId === parentId)

    return children.map((menu) => ({
      ...menu,
      microApp: menu.microApp
        ? {
            id: menu.microApp.id,
            name: menu.microApp.name,
            code: menu.microApp.code,
            url: menu.microApp.url,
          }
        : undefined,
      children: this.buildMenuTree(menus, menu.id),
    }))
  }

  /**
   * 获取菜单的所有子菜单ID
   */
  async getMenuDescendantIds(menuId: string): Promise<string[]> {
    const allMenus = await db.menu.findMany({
      select: { id: true, parentId: true },
    })

    const descendantIds: string[] = []

    function findDescendants(parentId: string) {
      const children = allMenus.filter((menu) => menu.parentId === parentId)
      for (const child of children) {
        descendantIds.push(child.id)
        findDescendants(child.id)
      }
    }

    findDescendants(menuId)
    return descendantIds
  }

  /**
   * 批量检查菜单访问权限
   */
  async batchCheckMenuAccess(userId: string, menuIds: string[]): Promise<Record<string, boolean>> {
    // 获取用户所有角色
    const userEffectiveRoles = await userRoleService.getUserEffectiveRoles(userId)
    const allRoles = [...userEffectiveRoles.globalRoles, ...userEffectiveRoles.departmentRoles.flatMap((dr) => dr.roles)]
    const roleIds = allRoles.map((role) => role.id)

    const results: Record<string, boolean> = {}

    if (roleIds.length === 0) {
      // 如果用户没有任何角色，所有菜单都无权限
      for (const menuId of menuIds) {
        results[menuId] = false
      }
      return results
    }

    // 查询用户有权限的菜单
    const accessibleMenus = await db.roleMenu.findMany({
      where: {
        roleId: { in: roleIds },
        menuId: { in: menuIds },
      },
      select: { menuId: true },
    })

    const accessibleMenuIds = new Set(accessibleMenus.map((rm) => rm.menuId))

    // 构建结果
    for (const menuId of menuIds) {
      results[menuId] = accessibleMenuIds.has(menuId)
    }

    return results
  }

  /**
   * 获取角色可分配的菜单列表（排除已分配的）
   */
  async getAssignableMenus(roleId: string) {
    // 获取已分配的菜单
    const assignedMenus = await db.roleMenu.findMany({
      where: { roleId },
      select: { menuId: true },
    })
    const assignedMenuIds = assignedMenus.map((rm) => rm.menuId)

    // 获取所有可用菜单（排除已分配的）
    return await db.menu.findMany({
      where: {
        enabled: 1,
        id: { notIn: assignedMenuIds },
      },
      include: {
        microApp: true,
      },
      orderBy: [{ sort: 'asc' }, { name: 'asc' }],
    })
  }

  /**
   * 同步角色菜单权限（替换现有权限）
   */
  async syncRoleMenus(roleId: string, menuIds: string[]): Promise<void> {
    await db.$transaction(async (tx) => {
      // 删除现有权限
      await tx.roleMenu.deleteMany({
        where: { roleId },
      })

      // 创建新权限
      if (menuIds.length > 0) {
        await tx.roleMenu.createMany({
          data: menuIds.map((menuId) => ({
            roleId,
            menuId,
            createdBy: 'system',
            creatorName: 'system',
            updatedBy: 'system',
            updaterName: 'system',
          })),
        })
      }
    })
  }

  /**
   * 获取菜单的访问统计
   */
  async getMenuAccessStats(menuId: string): Promise<{
    totalRoles: number
    totalUsers: number
    roles: Array<{ id: string; name: string; code: string }>
  }> {
    // 获取有权限访问该菜单的角色
    const roleMenus = await db.roleMenu.findMany({
      where: {
        menuId,
      },
      include: {
        role: {
          select: { id: true, name: true, code: true },
        },
      },
    })

    const roles = roleMenus.map((rm) => rm.role).filter((r) => r !== null)
    const roleIds = roles.map((r) => r.id)

    // 统计有这些角色的用户数量（简化查询）
    const userRoles = await db.userRole.findMany({
      where: {
        roleId: { in: roleIds },
      },
      select: { userId: true },
    })

    const uniqueUserIds = new Set(userRoles.map((ur) => ur.userId))

    return {
      totalRoles: roles.length,
      totalUsers: uniqueUserIds.size,
      roles,
    }
  }
}

// 创建单例实例
export const menuPermissionService = new MenuPermissionService()
