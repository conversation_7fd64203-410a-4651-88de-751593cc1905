// RBAC 角色管理服务

import { db } from '@/db'
import type { AuditFields, PaginatedResult } from '@/schemas/auth.schema'
import type { CreateRole, RoleQuery, RoleTreeResponse, UpdateRole } from '@/schemas/role.schema'
import type { Permission, Role } from '@prisma/client'
import { cacheService } from './cache.service'

export class RoleService {
  /**
   * 创建角色
   */
  async createRole(data: CreateRole, auditFields: AuditFields): Promise<Role> {
    const { permissionIds, menuIds, ...roleData } = data

    // 生成树路径
    let treePath = roleData.code
    if (roleData.parentId) {
      const parent = await this.getRoleById(roleData.parentId)
      if (parent) {
        treePath = `${parent.treePath}.${roleData.code}`
      }
    }

    // 创建角色
    const role = await db.role.create({
      data: {
        ...roleData,
        treePath,
        ...auditFields,
      },
    })

    // 分配权限
    if (permissionIds && permissionIds.length > 0) {
      await this.assignPermissions(role.id, permissionIds, auditFields)
    }

    // 分配菜单
    if (menuIds && menuIds.length > 0) {
      await this.assignMenus(role.id, menuIds, auditFields)
    }

    // 清除相关缓存
    await cacheService.invalidateRoleCache(role.id)

    return role
  }

  /**
   * 更新角色
   */
  async updateRole(id: string, data: UpdateRole, auditFields: Partial<AuditFields>): Promise<Role> {
    const { permissionIds, menuIds, ...roleData } = data

    // 如果更新了父级或编码，需要重新计算树路径
    if (roleData.parentId !== undefined || roleData.code) {
      const currentRole = await this.getRoleById(id)
      if (!currentRole) {
        throw new Error('Role not found')
      }

      let treePath = roleData.code || currentRole.code
      if (roleData.parentId) {
        const parent = await this.getRoleById(roleData.parentId)
        if (parent) {
          treePath = `${parent.treePath}.${treePath}`
        }
      }
      ;(roleData as UpdateRole & { treePath: string }).treePath = treePath
    }

    // 更新角色基本信息
    const role = await db.role.update({
      where: { id },
      data: {
        ...roleData,
        updatedBy: auditFields.updatedBy,
        updaterName: auditFields.updaterName,
      },
    })

    // 更新权限
    if (permissionIds !== undefined) {
      await this.replacePermissions(id, permissionIds, auditFields)
    }

    // 更新菜单
    if (menuIds !== undefined) {
      await this.replaceMenus(id, menuIds, auditFields)
    }

    // 清除相关缓存
    await cacheService.invalidateRoleCache(id)
    await cacheService.invalidateAllUserPermissions()

    return role
  }

  /**
   * 删除角色
   */
  async deleteRole(id: string): Promise<void> {
    // 检查是否有子角色
    const childrenCount = await db.role.count({
      where: {
        parentId: id,
      },
    })

    if (childrenCount > 0) {
      throw new Error('Cannot delete role with children')
    }

    // 检查是否有用户使用此角色
    const userRoleCount = await db.userRole.count({
      where: {
        roleId: id,
      },
    })

    if (userRoleCount > 0) {
      throw new Error('Cannot delete role that is assigned to users')
    }

    // 软删除角色
    await db.role.delete({
      where: { id },
    })

    // 清除相关缓存
    await cacheService.invalidateRoleCache(id)
    await cacheService.invalidateAllUserPermissions()
  }

  /**
   * 根据ID获取角色
   */
  async getRoleById(id: string): Promise<Role | null> {
    return await db.role.findUnique({
      where: { id },
    })
  }

  /**
   * 根据编码获取角色
   */
  async getRoleByCode(code: string): Promise<Role | null> {
    return await db.role.findFirst({
      where: {
        code,
      },
    })
  }

  /**
   * 获取角色列表（分页）
   */
  async getRoles(query: RoleQuery & { page?: number; pageSize?: number }): Promise<PaginatedResult<Role>> {
    const { page = 1, pageSize = 20, code, name, type, enabled, parentId } = query

    const where = {
      ...(code && { code: { contains: code } }),
      ...(name && { name: { contains: name } }),
      ...(type !== undefined && { type }),
      ...(enabled !== undefined && { enabled }),
      ...(parentId !== undefined && { parentId }),
    }

    const [data, total] = await Promise.all([
      db.role.findMany({
        where,
        skip: (page - 1) * pageSize,
        take: pageSize,
        orderBy: [{ type: 'asc' }, { treePath: 'asc' }],
      }),
      db.role.count({ where }),
    ])

    return {
      data,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    }
  }

  /**
   * 获取角色树结构
   */
  async getRoleTree(): Promise<RoleTreeResponse[]> {
    const roles = await db.role.findMany({
      orderBy: { treePath: 'asc' },
    })

    return this.buildRoleTree(roles)
  }

  /**
   * 构建角色树结构
   */
  private buildRoleTree(roles: Role[], parentId: string | null = null): RoleTreeResponse[] {
    const children = roles.filter((role) => role.parentId === parentId)

    return children.map((role) => ({
      ...role,
      children: this.buildRoleTree(roles, role.id),
    }))
  }

  /**
   * 获取角色的所有子角色
   */
  async getRoleChildren(parentId: string): Promise<Role[]> {
    return await db.role.findMany({
      where: {
        parentId,
      },
      orderBy: { treePath: 'asc' },
    })
  }

  /**
   * 获取角色的所有祖先角色
   */
  async getRoleAncestors(roleId: string): Promise<Role[]> {
    const role = await this.getRoleById(roleId)
    if (!role) return []

    const pathParts = role.treePath.split('.')
    const ancestorCodes = []

    for (let i = 0; i < pathParts.length - 1; i++) {
      ancestorCodes.push(pathParts.slice(0, i + 1).join('.'))
    }

    if (ancestorCodes.length === 0) return []

    return await db.role.findMany({
      where: {
        treePath: { in: ancestorCodes },
      },
      orderBy: { treePath: 'asc' },
    })
  }

  /**
   * 分配权限给角色
   */
  async assignPermissions(roleId: string, permissionIds: string[], auditFields: Partial<AuditFields>): Promise<void> {
    const data = permissionIds.map((permissionId) => ({
      roleId,
      permissionId,
      createdBy: auditFields.createdBy || 'system',
      creatorName: auditFields.creatorName || 'system',
      updatedBy: auditFields.updatedBy || 'system',
      updaterName: auditFields.updaterName || 'system',
    }))

    await db.rolePermission.createMany({
      data,
      skipDuplicates: true,
    })

    // 清除缓存
    await cacheService.invalidateRoleCache(roleId)
    await cacheService.invalidateAllUserPermissions()
  }

  /**
   * 移除角色权限
   */
  async removePermissions(roleId: string, permissionIds: string[]): Promise<void> {
    await db.rolePermission.deleteMany({
      where: {
        roleId,
        permissionId: { in: permissionIds },
      },
    })

    // 清除缓存
    await cacheService.invalidateRoleCache(roleId)
    await cacheService.invalidateAllUserPermissions()
  }

  /**
   * 替换角色权限
   */
  async replacePermissions(roleId: string, permissionIds: string[], auditFields: Partial<AuditFields>): Promise<void> {
    // 删除现有权限
    await db.rolePermission.deleteMany({
      where: { roleId },
    })

    // 添加新权限
    if (permissionIds.length > 0) {
      await this.assignPermissions(roleId, permissionIds, auditFields)
    }
  }

  /**
   * 获取角色权限
   */
  async getRolePermissions(roleId: string): Promise<Permission[]> {
    // 尝试从缓存获取
    const cached = await cacheService.getRolePermissionsCache(roleId)
    if (cached) return cached

    const rolePermissions = await db.rolePermission.findMany({
      where: {
        roleId,
      },
      include: {
        permission: true,
      },
    })

    const permissions = rolePermissions.map((rp) => rp.permission).filter((p) => p !== null) as Permission[]

    // 缓存结果
    await cacheService.setRolePermissionsCache(roleId, permissions)

    return permissions
  }

  /**
   * 分配菜单给角色
   */
  async assignMenus(roleId: string, menuIds: string[], auditFields: Partial<AuditFields>): Promise<void> {
    const data = menuIds.map((menuId) => ({
      roleId,
      menuId,
      createdBy: auditFields.createdBy || 'system',
      creatorName: auditFields.creatorName || 'system',
      updatedBy: auditFields.updatedBy || 'system',
      updaterName: auditFields.updaterName || 'system',
    }))

    await db.roleMenu.createMany({
      data,
      skipDuplicates: true,
    })
  }

  /**
   * 替换角色菜单
   */
  async replaceMenus(roleId: string, menuIds: string[], auditFields: Partial<AuditFields>): Promise<void> {
    // 删除现有菜单
    await db.roleMenu.deleteMany({
      where: { roleId },
    })

    // 添加新菜单
    if (menuIds.length > 0) {
      await this.assignMenus(roleId, menuIds, auditFields)
    }
  }

  /**
   * 获取角色菜单
   */
  async getRoleMenus(roleId: string) {
    const roleMenus = await db.roleMenu.findMany({
      where: {
        roleId,
      },
      include: {
        menu: {
          include: {
            microApp: true,
          },
        },
      },
    })

    return roleMenus.map((rm) => rm.menu).filter((m) => m !== null)
  }

  /**
   * 检查角色编码是否存在
   */
  async isRoleCodeExists(code: string, excludeId?: string): Promise<boolean> {
    const where = {
      code,
      ...(excludeId && { id: { not: excludeId } }),
    }

    const count = await db.role.count({ where })
    return count > 0
  }

  /**
   * 获取角色的有效权限（包括继承的权限）
   */
  async getRoleEffectivePermissions(roleId: string): Promise<Permission[]> {
    const role = await this.getRoleById(roleId)
    if (!role) return []

    // 获取当前角色权限
    const currentPermissions = await this.getRolePermissions(roleId)

    // 获取祖先角色权限
    const ancestors = await this.getRoleAncestors(roleId)
    const ancestorPermissions: Permission[] = []

    for (const ancestor of ancestors) {
      const permissions = await this.getRolePermissions(ancestor.id)
      ancestorPermissions.push(...permissions)
    }

    // 合并去重
    const allPermissions = [...currentPermissions, ...ancestorPermissions]
    const uniquePermissions = allPermissions.filter(
      (permission, index, self) => index === self.findIndex((p) => p.id === permission.id)
    )

    return uniquePermissions
  }
}

// 创建单例实例
export const roleService = new RoleService()
