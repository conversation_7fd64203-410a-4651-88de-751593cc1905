import { ForbiddenException, UnauthorizedException } from '@/core/exceptions'
import { db } from '@/db'
import { MiddlewareHandler } from 'hono'
import { tryit } from 'radash'

// 获取用户的所有权限代码
async function getUserEffectivePermissions(userId: string): Promise<Set<string>> {
  const [err, userWithRoles] = await tryit(db.user.findUnique)({
    where: { id: userId },
    include: {
      userRoles: {
        where: {
          role: {
            enabled: 1,
          },
        },
        include: {
          role: {
            include: {
              rolePermissions: {
                include: {
                  permission: true,
                },
              },
            },
          },
        },
      },
      memberships: {
        include: {
          departmentMemberRoles: {
            where: {
              role: {
                enabled: 1,
              },
            },
            include: {
              role: {
                include: {
                  rolePermissions: {
                    include: {
                      permission: true,
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  })

  if (err || !userWithRoles) {
    return new Set<string>()
  }

  const permissions = new Set<string>()

  // 添加用户全局角色的权限
  userWithRoles.userRoles.forEach((userRole) => {
    userRole.role?.rolePermissions.forEach((rp) => {
      if (rp.permission) {
        permissions.add(rp.permission.code)
      }
    })
  })

  // 添加用户在各部门中角色的权限
  userWithRoles.memberships.forEach((membership) => {
    membership.departmentMemberRoles.forEach((memberRole) => {
      memberRole.role.rolePermissions.forEach((rp) => {
        if (rp.permission) {
          permissions.add(rp.permission.code)
        }
      })
    })
  })

  return permissions
}

// 创建需要特定权限的中间件
export function requirePermission(requiredPermissionCode: string | string[]): MiddlewareHandler {
  return async (c, next) => {
    const user = c.get('user')
    const session = c.get('session')

    if (!user || !session) {
      throw new UnauthorizedException('Authentication required')
    }

    const userPermissions = await getUserEffectivePermissions(user.id)

    const hasPermission = Array.isArray(requiredPermissionCode)
      ? requiredPermissionCode.every((p) => userPermissions.has(p))
      : userPermissions.has(requiredPermissionCode)

    if (!hasPermission) {
      const message = Array.isArray(requiredPermissionCode)
        ? `Missing required permissions: ${requiredPermissionCode.join(', ')}`
        : `Missing required permission: ${requiredPermissionCode}`
      throw new ForbiddenException(message)
    }

    return next()
  }
}

// 创建需要特定角色的中间件
export function requireRole(requiredRoleCode: string | string[]): MiddlewareHandler {
  return async (c, next) => {
    const user = c.get('user')
    const session = c.get('session')

    if (!user || !session) {
      throw new UnauthorizedException('Authentication required')
    }

    const [err, userRoles] = await tryit(db.userRole.findMany)({
      where: {
        userId: user.id,
        role: {
          code: typeof requiredRoleCode === 'string' ? requiredRoleCode : { in: requiredRoleCode },
          enabled: 1,
        },
      },
    })

    if (err || !userRoles.length) {
      const message = Array.isArray(requiredRoleCode)
        ? `Missing required roles: ${requiredRoleCode.join(', ')}`
        : `Missing required role: ${requiredRoleCode}`
      throw new ForbiddenException(message)
    }

    return next()
  }
}

// 默认的权限中间件，用于记录用户权限到请求上下文中
export function permissionMiddleware(): MiddlewareHandler {
  return async function (c, next) {
    const user = c.get('user')
    const session = c.get('session')

    if (!user || !session) {
      return next()
    }

    const permissions = await getUserEffectivePermissions(user.id)
    c.set('permissions', permissions)

    return next()
  }
}
