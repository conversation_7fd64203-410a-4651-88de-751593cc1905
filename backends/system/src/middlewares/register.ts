import { Env, Hono } from 'hono'
import { contextStorage } from 'hono/context-storage'
import { cors } from 'hono/cors'
import { prettyJSON } from 'hono/pretty-json'
import { requestId } from 'hono/request-id'
import { BlankEnv, BlankSchema, Schema } from 'hono/types'
import { nanoid } from 'nanoid'

import { AppContext } from '@/types'
import { authMiddleware } from './auth.middleware'
import { registerErrorHandler } from './error.handler'
import { loggerMiddleware } from './logger.middleware'
import { permissionMiddleware } from './permission.middleware'

export function registerHttpMiddleware<
  _E extends Env = BlankEnv,
  S extends Schema = BlankSchema,
  BasePath extends string = '/',
>(app: Hono<AppContext, S, BasePath>) {
  registerErrorHandler(app)

  // FIXME: 跨域配置有问题，影响 better-auth client 的 options 请求，临时配置，等有缘人解决
  app.use('*', cors({ origin: ['*', 'http://localhost:3000', 'http://localhost:3003'], credentials: true }))
  // app.use(cors())

  app.use(requestId({ generator: () => nanoid(16) }))

  app.use(contextStorage())

  app.use(prettyJSON())

  app.use(loggerMiddleware())

  app.use(authMiddleware())

  app.use(permissionMiddleware())
}
