import { MiddlewareHandler } from 'hono'

import { auth } from '@/auth/server'
import { UserWithRoles } from '@/types'

export function authMiddleware(): MiddlewareHandler {
  return async function (c, next) {
    const session = await auth.api.getSession({ headers: c.req.raw.headers })

    if (!session) {
      c.set('user', undefined)
      c.set('session', undefined)
      return next()
    }

    c.set('user', session.user as UserWithRoles)
    c.set('session', session.session)
    return next()
  }
}
