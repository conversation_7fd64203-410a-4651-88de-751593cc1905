import { MiddlewareH<PERSON>ler } from 'hono'

import { env } from '@/env'
import logger from '@/utils/logger'

export function loggerMiddleware(): MiddlewareHandler {
  return async function (c, next) {
    const { method, url } = c.req
    const start = Date.now()
    logger.info(`[REQUEST][${method}][${url}]`)

    await next()

    const delta = Date.now() - start

    const status = c.res.status

    if (status >= 400) {
      logger.error(`[RESPONSE][${method}][${url}] - ${status} in ${delta}ms`)
      return
    }

    if (env.NODE_ENV === 'development') {
      // 开发环境，打印响应体，只打印 json 类型的响应体
      const contentType = c.res.headers.get('Content-Type')
      if (contentType && contentType.includes('application/json')) {
        const respText = await new Response(c.res.clone().body).text()
        logger.info(`[RESPONSE][${method}][${url}] - ${status} in ${delta}ms\n${respText}`)
      } else {
        logger.info(`[RESPONSE][${method}][${url}] - ${status} in ${delta}ms`)
      }
    } else {
      logger.info(`[RESPONSE][${method}][${url}] - ${status} in ${delta}ms`)
    }
  }
}
