import { AppException } from '@/core/exceptions/app.exception'
import { InternalServerErrorException } from '@/core/exceptions/internal-serve-error.exception'
import { AppContext } from '@/types'
import logger from '@/utils/logger'
import { Hono } from 'hono'
import { HTTPException } from 'hono/http-exception'

export function registerErrorHandler(app: Hono<AppContext>) {
  app.onError((err, c) => {
    let exception: AppException
    const requestId = c.get('requestId')

    if (err instanceof AppException) {
      exception = err
    } else if (err instanceof HTTPException) {
      exception = new AppException(err.message, err.status ?? 500, null)
    } else if (err instanceof Error) {
      logger.error('Unhandled Error:', err)
      exception = new InternalServerErrorException(err.message ?? 'An unexpected error occurred.')
    } else {
      logger.error('Unknown Error Type:', err)
      exception = new InternalServerErrorException('An unknown error occurred.')
    }

    // 统一返回 http status 200
    c.status(200)

    return c.json({
      code: exception.status,
      message: exception.message,
      data: exception.data,
      timestamp: new Date().getTime(),
      path: c.req.path,
      requestId,
    })
  })
}
