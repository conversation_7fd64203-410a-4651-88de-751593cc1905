import { fail } from '@/core/response/send'
import type { PermissionCheckMode, RoleScope } from '@/schemas/auth.schema'
import { authorizationService } from '@/services/authorization.service'
import { createMiddleware } from 'hono/factory'

// RBAC 权限检查中间件

/**
 * 权限检查中间件
 */
export const requirePermissions = (permissions: string | string[], mode: PermissionCheckMode = 'ALL') => {
  return createMiddleware(async (c, next) => {
    const user = c.get('user')
    if (!user?.id) {
      return fail(c, 'Authentication required', 401)
    }

    const requiredPerms = Array.isArray(permissions) ? permissions : [permissions]

    // 使用 AuthorizationService 进行权限检查
    const hasPermission = await authorizationService.checkPermissions(user.id, requiredPerms, mode)

    if (!hasPermission) {
      return fail(c, 'Insufficient permissions', 403, {
        required: requiredPerms,
        mode,
      })
    }

    await next()
  })
}

/**
 * 角色检查中间件
 */
export const requireRoles = (roles: string | string[], scope: RoleScope = 'ANY') => {
  return createMiddleware(async (c, next) => {
    const user = c.get('user')
    if (!user?.id) {
      return fail(c, 'Authentication required', 401)
    }

    const roleArray = Array.isArray(roles) ? roles : [roles]

    let hasRole = false
    for (const role of roleArray) {
      if (await authorizationService.checkRole(user.id, role, scope)) {
        hasRole = true
        break
      }
    }

    if (!hasRole) {
      return fail(c, 'Insufficient role permissions', 403, {
        required: roleArray,
        scope,
      })
    }

    await next()
  })
}

/**
 * 数据权限检查中间件
 */
export const requireDataScope = (resource: string, action: string = 'read') => {
  return createMiddleware(async (c, next) => {
    const user = c.get('user')
    if (!user?.id) {
      return fail(c, 'Authentication required', 401)
    }

    const context = {
      departmentId: c.req.param('deptId') || c.req.param('departmentId'),
      userId: c.req.param('userId'),
    }

    const hasDataAccess = await authorizationService.checkDataPermission(user.id, resource, action, context)
    if (!hasDataAccess) {
      return fail(c, 'Insufficient data access permissions', 403, {
        resource,
        action,
        context: {
          departmentId: context.departmentId,
          userId: context.userId,
        },
      })
    }

    await next()
  })
}

/**
 * 菜单权限检查中间件（用于API路由保护）
 */
export const requireMenuAccess = (menuPath: string) => {
  return createMiddleware(async (c, next) => {
    const user = c.get('user')
    if (!user?.id) {
      return fail(c, 'Authentication required', 401)
    }

    // 这里需要实现菜单权限检查逻辑
    // 暂时简化为检查基础权限
    const hasMenuAccess = await authorizationService.checkPermission(user.id, 'system:menu:access')
    if (!hasMenuAccess) {
      return fail(c, 'Menu access denied', 403, {
        menuPath,
      })
    }

    await next()
  })
}

/**
 * 系统管理员权限检查中间件
 */
export const requireSystemAdmin = () => {
  return createMiddleware(async (c, next) => {
    const user = c.get('user')
    if (!user?.id) {
      return fail(c, 'Authentication required', 401)
    }

    const isSystemAdmin = await authorizationService.isSystemAdmin(user.id)
    if (!isSystemAdmin) {
      return fail(c, 'System administrator access required', 403)
    }

    await next()
  })
}

/**
 * 部门管理员权限检查中间件
 */
export const requireDepartmentAdmin = (departmentIdParam?: string) => {
  return createMiddleware(async (c, next) => {
    const user = c.get('user')
    if (!user?.id) {
      return fail(c, 'Authentication required', 401)
    }

    const departmentId = departmentIdParam || c.req.param('deptId') || c.req.param('departmentId')
    const isDepartmentAdmin = await authorizationService.isDepartmentAdmin(user.id, departmentId)

    if (!isDepartmentAdmin) {
      return fail(c, 'Department administrator access required', 403, {
        departmentId,
      })
    }

    await next()
  })
}

/**
 * 组合权限检查中间件
 */
export const requireAnyPermission = (permissions: string[]) => {
  return requirePermissions(permissions, 'ANY')
}

/**
 * 组合角色检查中间件
 */
export const requireAnyRole = (roles: string[], scope: RoleScope = 'ANY') => {
  return requireRoles(roles, scope)
}

/**
 * 自定义权限检查中间件
 */
export const requireCustomPermission = (
  checkFunction: (userId: string, context: Record<string, unknown>) => Promise<boolean>,
  errorMessage: string = 'Access denied'
) => {
  return createMiddleware(async (c, next) => {
    const user = c.get('user')
    if (!user?.id) {
      return fail(c, 'Authentication required', 401)
    }

    const context = {
      params: c.req.param(),
      query: c.req.query(),
      headers: c.req.header(),
      req: c.req,
    }

    const hasPermission = await checkFunction(user.id, context)
    if (!hasPermission) {
      return fail(c, errorMessage, 403)
    }

    await next()
  })
}

/**
 * 批量权限检查中间件
 */
export const requireBatchPermissions = (
  permissionChecks: Array<{
    permissions: string[]
    mode?: PermissionCheckMode
    errorMessage?: string
  }>
) => {
  return createMiddleware(async (c, next) => {
    const user = c.get('user')
    if (!user?.id) {
      return fail(c, 'Authentication required', 401)
    }

    for (const check of permissionChecks) {
      const hasPermission = await authorizationService.checkPermissions(user.id, check.permissions, check.mode || 'ALL')

      if (!hasPermission) {
        return fail(c, check.errorMessage || 'Insufficient permissions', 403, {
          required: check.permissions,
          mode: check.mode || 'ALL',
        })
      }
    }

    await next()
  })
}

/**
 * 权限信息注入中间件（将用户权限信息注入到上下文中）
 */
export const injectUserPermissions = () => {
  return createMiddleware(async (c, next) => {
    const user = c.get('user')
    if (user?.id) {
      try {
        const userPermissions = await authorizationService.getUserEffectivePermissions(user.id)
        const userDataScope = await authorizationService.getUserDataScope(user.id)

        c.set('userPermissions', Array.from(userPermissions))
        c.set('userDataScope', userDataScope)
      } catch (error) {
        console.error('Failed to inject user permissions:', error)
      }
    }

    await next()
  })
}
