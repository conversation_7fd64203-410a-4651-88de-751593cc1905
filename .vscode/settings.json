{"eslint.workingDirectories": [{"mode": "auto"}], "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnPaste": false, "editor.formatOnSave": false, "emmet.showExpandedAbbreviation": "never", "prettier.enable": true, "typescript.tsdk": "node_modules/typescript/lib", "[ignore]": {"editor.defaultFormatter": "foxundermoon.shell-format"}, "[dotenv]": {"editor.defaultFormatter": "foxundermoon.shell-format"}, "[properties]": {"editor.defaultFormatter": "foxundermoon.shell-format"}, "oxc.enable": true, "svg.preview.background": "transparent", "[svg]": {"editor.defaultFormatter": "jock.svg"}, "css.customData": [".vscode/tailwind.json"], "cSpell.ignoreWords": ["CVTE", "umij<PERSON>", "radash", "tryit", "autoincrement", "pageable", "ox<PERSON>", "dotenvx", "hono", "turbopack", "oklch", "sonner", "microapp", "<PERSON><PERSON>", "Afterhidden", "Aftershow", "Beforeshow", "shadcn", "frontends"], "files.associations": {"*.css": "tailwindcss"}, "tailwindCSS.classFunctions": ["clsx", "cn", "tw", "cva", "extendVariants"], "tailwindCSS.classAttributes": ["class", "className", "ngClass", "class:list", "classNames", "rootClassName"], "tailwindCSS.experimental.configFile": {"packages/design-system/src/styles/globals.css": ["packages/design-system/src/**", "packages/layouts/src/**", "packages/fe-helper/src/**"], "frontends/base-app/src/app/globals.css": "frontends/base-app/src/**"}, "[prisma]": {"editor.defaultFormatter": "Prisma.prisma"}}