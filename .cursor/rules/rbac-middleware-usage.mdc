---
description:
globs:
alwaysApply: false
---
# RBAC 中间件使用指南

## 中间件文件位置

- [backends/system/src/middlewares/rbac.ts](mdc:backends/system/src/middlewares/rbac.ts) - 权限中间件核心实现
- [backends/system/src/middlewares/auth.ts](mdc:backends/system/src/middlewares/auth.ts) - 认证中间件
- [backends/system/src/middlewares/index.ts](mdc:backends/system/src/middlewares/index.ts) - 中间件导出

## 核心中间件

### 1. requirePermissions - 权限检查中间件

用于检查用户是否拥有指定的功能权限。

```typescript
// 单个权限检查
app.get('/users', 
  requirePermissions('system:user:list'),
  async (c) => { /* 业务逻辑 */ }
);

// 多个权限检查 - 全部需要 (AND)
app.post('/users', 
  requirePermissions(['system:user:create', 'data:scope:department'], 'ALL'),
  async (c) => { /* 业务逻辑 */ }
);

// 多个权限检查 - 任一即可 (OR)
app.get('/dashboard', 
  requirePermissions(['system:admin:view', 'business:manager:view'], 'ANY'),
  async (c) => { /* 业务逻辑 */ }
);
```

### 2. requireRoles - 角色检查中间件

用于检查用户是否拥有指定的角色。

```typescript
// 全局角色检查
app.post('/admin-action',
  requireRoles('SUPER_ADMIN', 'GLOBAL'),
  async (c) => { /* 业务逻辑 */ }
);

// 部门角色检查
app.post('/dept-action',
  requireRoles('DEPT_ADMIN', 'DEPARTMENT'),
  async (c) => { /* 业务逻辑 */ }
);

// 任意范围角色检查
app.get('/manager-view',
  requireRoles(['SUPER_ADMIN', 'DEPT_ADMIN'], 'ANY'),
  async (c) => { /* 业务逻辑 */ }
);
```

### 3. requireDataScope - 数据权限检查中间件

用于检查用户对特定资源的数据访问权限。

```typescript
// 用户数据权限检查
app.get('/users/:userId',
  requireDataScope('system:user', 'read'),
  async (c) => { /* 业务逻辑 */ }
);

// 部门数据权限检查
app.get('/departments/:deptId/members',
  requireDataScope('department', 'read'),
  async (c) => { /* 业务逻辑 */ }
);

// 项目数据权限检查
app.put('/projects/:projectId',
  requireDataScope('business:project', 'update'),
  async (c) => { /* 业务逻辑 */ }
);
```

### 4. requireMenuAccess - 菜单权限检查中间件

用于保护特定页面的 API 访问权限。

```typescript
// 管理后台菜单权限
app.get('/admin/dashboard',
  requireMenuAccess('/admin/dashboard'),
  async (c) => { /* 业务逻辑 */ }
);

// 用户管理菜单权限
app.get('/admin/users',
  requireMenuAccess('/admin/users'),
  async (c) => { /* 业务逻辑 */ }
);
```

## 中间件组合使用

### 基础组合 - 权限 + 数据范围

```typescript
app.put('/users/:userId',
  requirePermissions('system:user:update'),    // 功能权限
  requireDataScope('system:user', 'update'),   // 数据权限
  async (c) => {
    const userId = c.req.param('userId');
    const userData = await c.req.json();
    // 更新用户逻辑
  }
);
```

### 复杂组合 - 权限 + 角色 + 数据范围

```typescript
app.put('/users/:userId/roles',
  requirePermissions('system:user:update'),           // 基础权限
  requireRoles(['SYSTEM_ADMIN', 'DEPT_ADMIN'], 'ANY'), // 角色检查
  requireDataScope('system:user', 'update'),          // 数据权限
  async (c) => {
    const userId = c.req.param('userId');
    const { roleIds } = await c.req.json();
    // 分配角色逻辑
  }
);
```

### 部门管理组合

```typescript
app.post('/departments/:deptId/members/:userId/roles',
  requirePermissions('system:department:update'),     // 部门管理权限
  requireDataScope('department', 'update'),           // 部门数据权限
  async (c) => {
    const { deptId, userId } = c.req.params();
    const { roleIds } = await c.req.json();
    const currentUser = c.get('user');
    
    // 额外的业务逻辑检查
    const deptRoleService = c.get('departmentRoleService');
    const canAssignRoles = await deptRoleService.canAssignRoles(
      currentUser.id, 
      deptId, 
      roleIds
    );
    
    if (!canAssignRoles) {
      throw new ForbiddenError('Cannot assign roles that you do not possess');
    }
    
    // 分配部门角色逻辑
  }
);
```

## 权限继承和通配符

### 权限通配符匹配规则

```typescript
// 用户拥有的权限 -> 可以访问的资源
'*'                    -> 所有权限
'system:*'             -> 所有系统管理权限
'system:user:*'        -> 所有用户管理权限
'business:project:*'   -> 所有项目管理权限

// 示例：用户拥有 'system:user:*' 权限
app.get('/users',           // ✅ 匹配 'system:user:list'
  requirePermissions('system:user:list'),
  handler
);

app.post('/users',          // ✅ 匹配 'system:user:create'
  requirePermissions('system:user:create'),
  handler
);

app.delete('/users/:id',    // ✅ 匹配 'system:user:delete'
  requirePermissions('system:user:delete'),
  handler
);
```

### 角色继承示例

```typescript
// 角色层级：SUPER_ADMIN -> DEPT_ADMIN -> SUB_DEPT_ADMIN

// 用户拥有 SUB_DEPT_ADMIN 角色，自动继承父角色权限
app.get('/admin-panel',
  requireRoles('DEPT_ADMIN'),  // ✅ SUB_DEPT_ADMIN 继承了 DEPT_ADMIN
  handler
);
```

## 数据权限范围控制

### 部门数据权限示例

```typescript
// 获取部门成员列表 - 自动检查部门访问权限
app.get('/departments/:deptId/members',
  requirePermissions('system:department:list'),
  requireDataScope('department', 'read'),
  async (c) => {
    const deptId = c.req.param('deptId');
    // DataScopeService 会自动检查用户是否有权限访问该部门
    // 父级部门管理员可以访问子部门数据
    const members = await departmentService.getDepartmentMembers(deptId);
    return c.json({ data: members });
  }
);
```

### 用户数据权限示例

```typescript
// 获取用户详情 - 检查用户数据访问权限
app.get('/users/:userId',
  requirePermissions('system:user:list'),
  requireDataScope('system:user', 'read'),
  async (c) => {
    const userId = c.req.param('userId');
    // 会检查：
    // 1. 是否是用户本人 (data:scope:self)
    // 2. 是否有部门数据权限 (data:scope:department)
    // 3. 是否有全部数据权限 (data:scope:all)
    const user = await userService.getUserById(userId);
    return c.json({ data: user });
  }
);
```

## 错误处理

### 权限错误类型

```typescript
// 未认证错误 - 401
throw new UnauthorizedError('Authentication required');

// 权限不足错误 - 403
throw new ForbiddenError('Missing required permissions: system:user:create');

// 角色不足错误 - 403
throw new ForbiddenError('Missing required roles: SUPER_ADMIN');

// 数据权限不足错误 - 403
throw new ForbiddenError('Insufficient data access permissions for system:user');

// 菜单权限不足错误 - 403
throw new ForbiddenError('Access denied to menu: /admin/dashboard');
```

## 性能优化建议

### 1. 权限缓存

```typescript
// 权限检查会自动使用缓存
// 缓存策略：
// - 用户权限缓存：5分钟
// - 角色权限缓存：30分钟
// - 菜单权限缓存：1小时
```

### 2. 批量权限检查

```typescript
// 避免在循环中进行权限检查
// ❌ 不好的做法
for (const user of users) {
  const hasPermission = await authService.checkPermission(currentUserId, 'system:user:view');
  if (hasPermission) {
    // 处理用户
  }
}

// ✅ 好的做法
const hasPermission = await authService.checkPermission(currentUserId, 'system:user:view');
if (hasPermission) {
  for (const user of users) {
    // 处理用户
  }
}
```

### 3. 权限预检查

```typescript
// 在路由级别进行权限检查，而不是在业务逻辑中
app.get('/users',
  requirePermissions('system:user:list'),  // 路由级别检查
  async (c) => {
    // 这里不需要再次检查权限
    const users = await userService.getUsers();
    return c.json({ data: users });
  }
);
```

## 常见使用模式

### 1. CRUD 操作权限模式

```typescript
const userRoutes = new Hono();

// 列表查看
userRoutes.get('/', 
  requirePermissions('system:user:list'),
  getUserList
);

// 详情查看
userRoutes.get('/:id', 
  requirePermissions('system:user:list'),
  requireDataScope('system:user', 'read'),
  getUserDetail
);

// 创建
userRoutes.post('/', 
  requirePermissions('system:user:create'),
  requireDataScope('system:user', 'create'),
  createUser
);

// 更新
userRoutes.put('/:id', 
  requirePermissions('system:user:update'),
  requireDataScope('system:user', 'update'),
  updateUser
);

// 删除
userRoutes.delete('/:id', 
  requirePermissions('system:user:delete'),
  requireDataScope('system:user', 'delete'),
  deleteUser
);
```

### 2. 管理员操作模式

```typescript
const adminRoutes = new Hono();

// 系统配置 - 只有超级管理员
adminRoutes.post('/system-config',
  requireRoles('SUPER_ADMIN', 'GLOBAL'),
  updateSystemConfig
);

// 用户管理 - 系统管理员或部门管理员
adminRoutes.post('/users/:userId/roles',
  requireRoles(['SYSTEM_ADMIN', 'DEPT_ADMIN'], 'ANY'),
  requirePermissions('system:user:update'),
  assignUserRoles
);
```

### 3. 部门管理模式

```typescript
const deptRoutes = new Hono();

// 查看部门信息 - 需要部门访问权限
deptRoutes.get('/:deptId',
  requirePermissions('system:department:list'),
  requireDataScope('department', 'read'),
  getDepartmentInfo
);

// 管理部门成员 - 需要部门管理权限
deptRoutes.post('/:deptId/members',
  requirePermissions('system:department:update'),
  requireDataScope('department', 'update'),
  addDepartmentMember
);

// 分配部门角色 - 需要角色分配权限
deptRoutes.post('/:deptId/members/:userId/roles',
  requirePermissions('system:department:update'),
  requireDataScope('department', 'update'),
  assignDepartmentRoles
);
```

## 调试和监控

### 权限检查日志

```typescript
// 在开发环境启用详细的权限检查日志
// 可以在中间件中添加日志记录用户权限检查过程
```

### 权限检查失败分析

```typescript
// 当权限检查失败时，记录详细信息：
// - 用户ID
// - 请求的权限
// - 用户当前拥有的权限
// - 失败原因
```
