---
description: 
globs: 
alwaysApply: false
---
# 微前端架构

本项目实现了基于多种技术的微前端架构，支持多种微应用集成方式。

## 核心实现

### 微应用框架
- **微应用核心**: [packages/micro-app/src/microapp](mdc:packages/micro-app/src/microapp) - 微前端核心实现
- **iframe集成**: [packages/micro-app/src/iframe](mdc:packages/micro-app/src/iframe) - 基于iframe的微应用集成
- **无界集成**: [packages/micro-app/src/wujie](mdc:packages/micro-app/src/wujie) - 基于无界(wujie)的微应用集成

### 主应用路由
- **微应用路由器**: [frontends/host/app/micro/[microapp]/[[...paths]]/page.tsx](mdc:frontends/host/app/micro/[microapp]/[[...paths]]/page.tsx) - 微应用的动态路由

## 微应用示例

### UmiJS示例应用
- **入口文件**: [frontends/microapp-demo-umi/src/app.tsx](mdc:frontends/microapp-demo-umi/src/app.tsx) - 应用入口
- **布局组件**: [frontends/microapp-demo-umi/src/layouts](mdc:frontends/microapp-demo-umi/src/layouts) - 应用布局
- **页面组件**: [frontends/microapp-demo-umi/src/pages](mdc:frontends/microapp-demo-umi/src/pages) - 应用页面
  - **权限页面**: [frontends/microapp-demo-umi/src/pages/Access](mdc:frontends/microapp-demo-umi/src/pages/Access) - 权限演示
  - **首页**: [frontends/microapp-demo-umi/src/pages/Home](mdc:frontends/microapp-demo-umi/src/pages/Home) - 应用首页
  - **表格示例**: [frontends/microapp-demo-umi/src/pages/Table](mdc:frontends/microapp-demo-umi/src/pages/Table) - 表格组件示例

### Vite示例应用
- **入口文件**: [frontends/microapp-demo-vite/src/main.ts](mdc:frontends/microapp-demo-vite/src/main.ts) - 应用入口
- **页面组件**: [frontends/microapp-demo-vite/src/pages](mdc:frontends/microapp-demo-vite/src/pages) - 应用页面

## 数据通信

### 应用间通信
- **事件总线**: [packages/micro-app/src/microapp/eventBus.ts](mdc:packages/micro-app/src/microapp/eventBus.ts) - 应用间通信事件总线
- **数据共享**: [packages/micro-app/src/microapp/store.ts](mdc:packages/micro-app/src/microapp/store.ts) - 应用间数据共享

### 安全防护
- **沙箱隔离**: [packages/micro-app/src/microapp/sandbox.ts](mdc:packages/micro-app/src/microapp/sandbox.ts) - JavaScript沙箱隔离
- **样式隔离**: [packages/micro-app/src/microapp/styleIsolation.ts](mdc:packages/micro-app/src/microapp/styleIsolation.ts) - CSS样式隔离

## 集成与部署

### 后端集成
- **微应用注册**: [backends/system/src/routes/micro-app](mdc:backends/system/src/routes/micro-app) - 微应用注册和管理API
- **权限管理**: [backends/system/src/routes/permission](mdc:backends/system/src/routes/permission) - 微应用权限管理

### 加载优化
- **资源预加载**: [packages/micro-app/src/microapp/prefetch.ts](mdc:packages/micro-app/src/microapp/prefetch.ts) - 微应用资源预加载
- **懒加载**: [packages/micro-app/src/microapp/lazyLoad.ts](mdc:packages/micro-app/src/microapp/lazyLoad.ts) - 微应用懒加载
