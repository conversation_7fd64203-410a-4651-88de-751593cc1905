---
description:
globs:
alwaysApply: false
---
# RBAC 系统实施指南

## 实施概览

本指南基于 [backends/system/docs/RBAC.md](mdc:backends/system/docs/RBAC.md) 设计方案，提供分阶段的实施步骤和最佳实践。

## 实施阶段规划

### 第一阶段：基础 RBAC 框架 (1-2周)

#### 1.1 Service 层开发

**核心服务实现**：
- [backends/system/src/services/authorization.service.ts](mdc:backends/system/src/services/authorization.service.ts) - 权限检查核心逻辑
- [backends/system/src/services/role.service.ts](mdc:backends/system/src/services/role.service.ts) - 角色管理服务
- [backends/system/src/services/permission.service.ts](mdc:backends/system/src/services/permission.service.ts) - 权限管理服务
- [backends/system/src/services/user-role.service.ts](mdc:backends/system/src/services/user-role.service.ts) - 用户角色关联服务
- [backends/system/src/services/cache.service.ts](mdc:backends/system/src/services/cache.service.ts) - 权限缓存管理

**实施优先级**：
1. `AuthorizationService` - 权限检查核心（最高优先级）
2. `RoleService` - 角色管理
3. `PermissionService` - 权限管理
4. `UserRoleService` - 用户角色关联
5. `CacheService` - 缓存优化

#### 1.2 API 路由开发

**基础 API 实现**：
- [backends/system/src/routes/role/index.ts](mdc:backends/system/src/routes/role/index.ts) - 角色管理 API
- [backends/system/src/routes/permission/index.ts](mdc:backends/system/src/routes/permission/index.ts) - 权限管理 API
- [backends/system/src/routes/user/index.ts](mdc:backends/system/src/routes/user/index.ts) - 用户角色分配 API

#### 1.3 中间件开发

**权限中间件实现**：
- [backends/system/src/middlewares/rbac.ts](mdc:backends/system/src/middlewares/rbac.ts) - 权限检查中间件
  - `requirePermissions` - 功能权限检查
  - `requireRoles` - 角色检查

#### 1.4 数据库初始化

**种子数据准备**：
- [backends/system/prisma/seeds/permissions.ts](mdc:backends/system/prisma/seeds/permissions.ts) - 基础权限数据
- [backends/system/prisma/seeds/roles.ts](mdc:backends/system/prisma/seeds/roles.ts) - 系统角色数据

### 第二阶段：部门权限体系 (2-3周)

#### 2.1 部门权限 Service

**部门相关服务**：
- [backends/system/src/services/department.service.ts](mdc:backends/system/src/services/department.service.ts) - 部门管理服务
- [backends/system/src/services/department-member.service.ts](mdc:backends/system/src/services/department-member.service.ts) - 部门成员管理
- [backends/system/src/services/department-role.service.ts](mdc:backends/system/src/services/department-role.service.ts) - 部门角色管理
- [backends/system/src/services/data-scope.service.ts](mdc:backends/system/src/services/data-scope.service.ts) - 数据权限范围检查

#### 2.2 部门权限 API

**部门管理 API**：
- [backends/system/src/routes/department/index.ts](mdc:backends/system/src/routes/department/index.ts) - 部门管理 API
  - 部门成员管理
  - 部门角色分配
  - 多级部门管理

#### 2.3 权限合并逻辑

**权限整合**：
- 全局权限 + 部门权限合并算法
- 权限缓存策略优化
- 数据权限范围检查

### 第三阶段：高级特性 (2-3周)

#### 3.1 菜单权限系统

**菜单权限实现**：
- [backends/system/src/services/menu-permission.service.ts](mdc:backends/system/src/services/menu-permission.service.ts) - 菜单权限服务
- [backends/system/src/routes/menu/index.ts](mdc:backends/system/src/routes/menu/index.ts) - 菜单管理 API
- [backends/system/src/routes/auth/index.ts](mdc:backends/system/src/routes/auth/index.ts) - 用户菜单 API

#### 3.2 数据权限中间件

**数据权限控制**：
- `requireDataScope` 中间件实现
- 部门数据权限检查逻辑
- 用户数据权限验证

#### 3.3 性能优化

**缓存和性能**：
- 多层级权限缓存
- 批量权限检查
- 权限预加载机制

### 第四阶段：监控与审计 (1-2周)

#### 4.1 权限审计

**审计功能**：
- 权限变更日志
- 用户操作审计
- 权限使用统计

#### 4.2 监控告警

**系统监控**：
- 权限异常监控
- 性能指标监控
- 安全事件告警

## 实施最佳实践

### 1. 开发规范

#### 1.1 代码组织

```typescript
// 服务层结构示例
src/services/
├── authorization.service.ts     // 权限检查核心
├── role.service.ts             // 角色管理
├── permission.service.ts       // 权限管理
├── user-role.service.ts        // 用户角色
├── department.service.ts       // 部门管理
├── department-member.service.ts // 部门成员
├── department-role.service.ts  // 部门角色
├── data-scope.service.ts       // 数据权限
├── menu-permission.service.ts  // 菜单权限
├── cache.service.ts            // 缓存管理
└── __tests__/                  // 测试文件
    ├── authorization.service.test.ts
    ├── role.service.test.ts
    └── ...
```

#### 1.2 接口设计

```typescript
// 统一的服务接口定义
// src/types/services.ts
export interface AuthorizationService {
  checkPermission(userId: string, permission: string): Promise<boolean>;
  checkPermissions(userId: string, permissions: string[], mode: 'ALL' | 'ANY'): Promise<boolean>;
  getUserEffectivePermissions(userId: string): Promise<Set<string>>;
}

export interface RoleService {
  createRole(data: CreateRoleRequest): Promise<Role>;
  updateRole(id: string, data: UpdateRoleRequest): Promise<Role>;
  deleteRole(id: string): Promise<void>;
  getRolePermissions(roleId: string): Promise<Permission[]>;
}
```

#### 1.3 错误处理

```typescript
// 统一的错误类型
// src/core/exceptions/rbac.exceptions.ts
export class PermissionDeniedError extends Error {
  constructor(permission: string) {
    super(`Permission denied: ${permission}`);
    this.name = 'PermissionDeniedError';
  }
}

export class RoleNotFoundError extends Error {
  constructor(roleId: string) {
    super(`Role not found: ${roleId}`);
    this.name = 'RoleNotFoundError';
  }
}
```

### 2. 测试策略

#### 2.1 单元测试

```typescript
// 服务层单元测试示例
// src/services/__tests__/authorization.service.test.ts
describe('AuthorizationService', () => {
  let authService: AuthorizationService;
  let mockUserRoleService: jest.Mocked<UserRoleService>;
  let mockCacheService: jest.Mocked<CacheService>;

  beforeEach(() => {
    mockUserRoleService = createMockUserRoleService();
    mockCacheService = createMockCacheService();
    authService = new AuthorizationService(
      mockUserRoleService,
      mockCacheService
    );
  });

  describe('checkPermission', () => {
    it('should return true for exact permission match', async () => {
      mockUserRoleService.getUserGlobalPermissions.mockResolvedValue(['system:user:list']);
      
      const result = await authService.checkPermission('user1', 'system:user:list');
      
      expect(result).toBe(true);
    });

    it('should return true for wildcard permission match', async () => {
      mockUserRoleService.getUserGlobalPermissions.mockResolvedValue(['system:user:*']);
      
      const result = await authService.checkPermission('user1', 'system:user:create');
      
      expect(result).toBe(true);
    });
  });
});
```

#### 2.2 集成测试

```typescript
// API 集成测试示例
// src/routes/__tests__/role.integration.test.ts
describe('Role API Integration', () => {
  let app: Hono;
  let testDb: PrismaClient;

  beforeAll(async () => {
    testDb = await setupTestDatabase();
    app = createTestApp(testDb);
  });

  describe('POST /api/system/roles', () => {
    it('should create role with proper permissions', async () => {
      const roleData = {
        code: 'TEST_ROLE',
        name: 'Test Role',
        permissionIds: ['perm1', 'perm2']
      };

      const response = await request(app)
        .post('/api/system/roles')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(roleData)
        .expect(201);

      expect(response.body.data.code).toBe('TEST_ROLE');
    });
  });
});
```

### 3. 部署策略

#### 3.1 数据库迁移

```bash
# 开发环境
npm run db:migrate:dev

# 生产环境
npm run db:migrate:deploy

# 种子数据
npm run db:seed
```

#### 3.2 环境配置

```typescript
// 环境变量配置
// .env
DATABASE_URL="postgresql://user:password@localhost:5432/rbac_db"
REDIS_URL="redis://localhost:6379"
JWT_SECRET="your-jwt-secret"

# 权限缓存配置
PERMISSION_CACHE_TTL=300  # 5分钟
ROLE_CACHE_TTL=1800      # 30分钟
MENU_CACHE_TTL=3600      # 1小时
```

### 4. 性能优化

#### 4.1 缓存策略

```typescript
// 权限缓存实现示例
export class CacheService {
  private redis: Redis;

  async getUserPermissionsCache(userId: string): Promise<Set<string> | null> {
    const key = `user:permissions:${userId}`;
    const cached = await this.redis.get(key);
    return cached ? new Set(JSON.parse(cached)) : null;
  }

  async setUserPermissionsCache(userId: string, permissions: string[]): Promise<void> {
    const key = `user:permissions:${userId}`;
    const ttl = process.env.PERMISSION_CACHE_TTL || 300;
    await this.redis.setex(key, ttl, JSON.stringify(permissions));
  }
}
```

#### 4.2 数据库优化

```sql
-- 权限查询优化索引
CREATE INDEX CONCURRENTLY idx_user_roles_lookup 
ON user_roles(user_id, deleted_at) 
WHERE deleted_at = -1;

CREATE INDEX CONCURRENTLY idx_role_permissions_lookup 
ON role_permissions(role_id, deleted_at) 
WHERE deleted_at = -1;

-- 部门层级查询优化
CREATE INDEX CONCURRENTLY idx_departments_tree_path_gin 
ON departments USING gin(tree_path gin_trgm_ops);
```

### 5. 安全考虑

#### 5.1 权限提升防护

```typescript
// 权限提升检查示例
export class RoleService {
  async assignRole(managerId: string, userId: string, roleId: string): Promise<void> {
    // 检查管理员是否有权限分配该角色
    const managerRoles = await this.getUserRoles(managerId);
    const targetRole = await this.getRoleById(roleId);
    
    const canAssign = await this.checkRoleAssignmentPermission(managerRoles, targetRole);
    if (!canAssign) {
      throw new PermissionDeniedError('Cannot assign role that you do not possess');
    }
    
    // 执行角色分配
    await this.userRoleService.assignRole(userId, roleId);
  }
}
```

#### 5.2 数据权限验证

```typescript
// 数据权限检查示例
export class DataScopeService {
  async checkDepartmentDataAccess(userId: string, targetDeptId: string): Promise<boolean> {
    const userDepartments = await this.getUserDepartments(userId);
    const targetDepartment = await this.getDepartmentById(targetDeptId);
    
    // 检查是否有权限访问目标部门
    return userDepartments.some(dept => 
      this.isParentDepartment(dept.treePath, targetDepartment.treePath) ||
      dept.id === targetDeptId
    );
  }
}
```

## 常见问题和解决方案

### 1. 权限检查性能问题

**问题**：频繁的权限检查导致性能下降

**解决方案**：
- 实施多层级缓存策略
- 批量权限检查
- 权限预加载

```typescript
// 批量权限检查优化
export class AuthorizationService {
  async checkMultiplePermissions(userId: string, permissions: string[]): Promise<Map<string, boolean>> {
    const userPermissions = await this.getUserEffectivePermissions(userId);
    const results = new Map<string, boolean>();
    
    permissions.forEach(permission => {
      results.set(permission, this.checkPermissionMatch(userPermissions, permission));
    });
    
    return results;
  }
}
```

### 2. 部门权限复杂性

**问题**：多级部门权限管理复杂

**解决方案**：
- 使用树路径优化查询
- 实施权限继承机制
- 清晰的权限范围定义

```typescript
// 部门权限继承实现
export class DepartmentService {
  async getAccessibleDepartments(userId: string): Promise<Department[]> {
    const userDepartments = await this.getUserDepartments(userId);
    const accessibleDepts: Department[] = [];
    
    for (const dept of userDepartments) {
      // 添加当前部门
      accessibleDepts.push(dept);
      
      // 添加所有子部门
      const childDepts = await this.getDescendantDepartments(dept.id);
      accessibleDepts.push(...childDepts);
    }
    
    return accessibleDepts;
  }
}
```

### 3. 权限缓存一致性

**问题**：权限变更后缓存不一致

**解决方案**：
- 实施缓存失效策略
- 使用事件驱动的缓存更新
- 定期缓存刷新

```typescript
// 缓存失效策略
export class RoleService {
  async updateRole(roleId: string, data: UpdateRoleRequest): Promise<Role> {
    const role = await this.prisma.role.update({
      where: { id: roleId },
      data
    });
    
    // 清除相关缓存
    await this.invalidateRoleCache(roleId);
    await this.invalidateUsersCache(roleId);
    
    return role;
  }
  
  private async invalidateUsersCache(roleId: string): Promise<void> {
    const userIds = await this.getUserIdsByRole(roleId);
    await this.cacheService.invalidateUsersCaches(userIds);
  }
}
```

## 监控和维护

### 1. 性能监控

```typescript
// 权限检查性能监控
export class AuthorizationService {
  async checkPermission(userId: string, permission: string): Promise<boolean> {
    const startTime = Date.now();
    
    try {
      const result = await this.doCheckPermission(userId, permission);
      
      // 记录性能指标
      const duration = Date.now() - startTime;
      this.metricsService.recordPermissionCheckDuration(duration);
      
      return result;
    } catch (error) {
      this.metricsService.recordPermissionCheckError(error);
      throw error;
    }
  }
}
```

### 2. 审计日志

```typescript
// 权限变更审计
export class RoleService {
  async assignPermissions(roleId: string, permissionIds: string[]): Promise<void> {
    const currentUser = this.getCurrentUser();
    
    await this.prisma.$transaction(async (tx) => {
      // 执行权限分配
      await this.doAssignPermissions(tx, roleId, permissionIds);
      
      // 记录审计日志
      await this.auditService.logPermissionAssignment({
        operatorId: currentUser.id,
        roleId,
        permissionIds,
        timestamp: new Date(),
        action: 'ASSIGN_PERMISSIONS'
      });
    });
  }
}
```

### 3. 健康检查

```typescript
// RBAC 系统健康检查
export class RBACHealthCheck {
  async checkHealth(): Promise<HealthStatus> {
    const checks = await Promise.allSettled([
      this.checkDatabaseConnection(),
      this.checkCacheConnection(),
      this.checkPermissionIntegrity(),
      this.checkRoleHierarchy()
    ]);
    
    return {
      status: checks.every(check => check.status === 'fulfilled') ? 'healthy' : 'unhealthy',
      checks: checks.map((check, index) => ({
        name: this.checkNames[index],
        status: check.status,
        error: check.status === 'rejected' ? check.reason : undefined
      }))
    };
  }
}
```

## 总结

RBAC 系统的成功实施需要：

1. **分阶段实施**：从基础功能开始，逐步增加复杂特性
2. **完善测试**：单元测试、集成测试、性能测试全覆盖
3. **性能优化**：合理的缓存策略和数据库优化
4. **安全考虑**：权限提升防护和数据权限验证
5. **监控维护**：完善的监控、审计和健康检查机制

通过遵循本指南的实施步骤和最佳实践，可以构建一个稳定、高效、安全的企业级 RBAC 权限控制系统。
