---
description:
globs:
alwaysApply: false
---
# Project Structure Guide

This is a monorepo project managed by Turborepo, containing multiple packages, frontends and backends.

## Core Components

### Frontends
- **Host Application**: [frontends/host](mdc:frontends/host) - Next.js based main application that hosts micro-apps
- **Demo Micro-apps**: 
  - [frontends/microapp-demo-umi](mdc:frontends/microapp-demo-umi) - UmiJS based demo micro-app
  - [frontends/microapp-demo-vite](mdc:frontends/microapp-demo-vite) - Vite based demo micro-app

### Backends
- **Gateway**: [backends/gateway](mdc:backends/gateway) - API Gateway service
- **System**: [backends/system](mdc:backends/system) - Core system service with user management, permissions etc.

### Shared Packages
- **Design System**: [packages/design-system](mdc:packages/design-system) - Shared UI components and styles
- **Micro-app**: [packages/micro-app](mdc:packages/micro-app) - Micro-frontend implementation
- **FE Helper**: [packages/fe-helper](mdc:packages/fe-helper) - Frontend utilities and providers
- **UI**: [packages/ui](mdc:packages/ui) - Base UI components
- **ESLint Config**: [packages/eslint-config](mdc:packages/eslint-config) - Shared ESLint configuration
- **TypeScript Config**: [packages/typescript-config](mdc:packages/typescript-config) - Shared TypeScript configuration

## Key Configuration Files
- [turbo.json](mdc:turbo.json) - Turborepo configuration
- [package.json](mdc:package.json) - Root package configuration
- [pnpm-workspace.yaml](mdc:pnpm-workspace.yaml) - PNPM workspace configuration
- [docker-compose.yml](mdc:docker-compose.yml) - Docker compose configuration
- [eslint.config.mjs](mdc:eslint.config.mjs) - ESLint configuration
