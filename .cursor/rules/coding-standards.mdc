---
description: 
globs: 
alwaysApply: false
---
# 编码规范

本项目采用严格的编码规范和最佳实践，确保代码质量和一致性。

## TypeScript规范

### 类型定义
- **类型优先**: 始终为变量、函数参数和返回值定义明确的类型
- **避免any**: 禁止使用`any`类型，除非特殊情况并添加注释说明
- **接口定义**: [backends/system/src/types](mdc:backends/system/src/types) - 统一的类型定义位置
- **类型声明**: [frontends/host/types](mdc:frontends/host/types) - 前端类型声明文件

### 类型配置
- **基础配置**: [tsconfig.json](mdc:tsconfig.json) - 基础TypeScript配置
- **应用配置**: [tsconfig.app.json](mdc:tsconfig.app.json) - 应用特定配置
- **Node配置**: [tsconfig.node.json](mdc:tsconfig.node.json) - Node.js特定配置

## 代码风格

### 格式化规则
- **Prettier**: [.prettierrc](mdc:.prettierrc) - 代码格式化规则
- **忽略文件**: [.prettierignore](mdc:.prettierignore) - 格式化忽略文件

### 代码质量
- **ESLint**: [eslint.config.mjs](mdc:eslint.config.mjs) - 代码质量检查规则
- **Oxlint**: [.oxlintrc.json](mdc:.oxlintrc.json) - 高性能代码检查配置

## 提交规范

### 提交信息
- **Commitlint**: [commitlint.config.cjs](mdc:commitlint.config.cjs) - 提交信息规范
- **Husky**: [.husky](mdc:.husky) - Git钩子配置

### 提交格式
- 格式: `<type>(<scope>): <subject>`
- 类型:
  - `feat`: 新功能
  - `fix`: 错误修复
  - `docs`: 文档变更
  - `style`: 代码格式变更
  - `refactor`: 代码重构
  - `perf`: 性能优化
  - `test`: 测试相关
  - `build`: 构建相关
  - `ci`: CI配置变更
  - `chore`: 其他变更

## 架构原则

### 前端原则
- **组件设计**: 遵循可复用、单一职责原则
- **状态管理**: 明确的状态管理策略和数据流向
- **响应式设计**: 支持多设备和屏幕尺寸

### 后端原则
- **RESTful API**: 遵循REST风格的API设计
- **中间件**: 统一的请求处理和响应格式
- **错误处理**: 标准化的错误处理和日志记录

## 测试规范

### 单元测试
- **测试框架**: Jest和React Testing Library
- **命名规范**: `*.test.ts`或`*.spec.ts`
- **测试覆盖**: 关键组件和工具函数需有单元测试

### 集成测试
- **API测试**: 核心API端点需有集成测试
- **环境配置**: 使用专门的测试环境和数据库
