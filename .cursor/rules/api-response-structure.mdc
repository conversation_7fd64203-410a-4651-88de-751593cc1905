---
description: 
globs: 
alwaysApply: false
---
# API 响应统一结构规范

所有API接口必须使用统一的响应结构，确保前后端数据交互的一致性和可预测性。

## 响应结构定义

### 基础响应接口
```typescript
interface ApiResponse<T = any> {
  /** 状态码 */
  code: number
  /** 响应消息 */
  message: string
  /** 响应数据 */
  data: T
}
```

### 核心文件位置
- **响应类型定义**: [backends/system/src/core/response/types.ts](mdc:backends/system/src/core/response/types.ts)
- **响应工具函数**: [backends/system/src/core/response/send.ts](mdc:backends/system/src/core/response/send.ts)

## 使用规范

### 1. 成功响应
使用 `success()` 函数创建成功响应：
```typescript
import { success } from '@/core/response/send'

// 返回数据
return success(c, userData, '获取用户信息成功')

// 默认成功消息
return success(c, userData) // message默认为'success'
```

### 2. 失败响应
使用 `fail()` 函数创建失败响应：
```typescript
import { fail } from '@/core/response/send'

// 自定义错误码和消息
return fail(c, '用户不存在', 404)

// 默认错误码500
return fail(c, '服务器内部错误')

// 带错误数据
return fail(c, '验证失败', 400, validationErrors)
```

### 3. 自定义响应
使用 `createResponse()` 和 `send()` 函数：
```typescript
import { createResponse, send } from '@/core/response/send'

const response = createResponse(201, '创建成功', newUser)
return send(c, response, 201)
```

## 状态码规范

### 成功状态码
- `200` - 操作成功
- `201` - 创建成功
- `204` - 删除成功（无内容返回）

### 客户端错误状态码
- `400` - 请求参数错误
- `401` - 未授权
- `403` - 禁止访问
- `404` - 资源不存在
- `409` - 资源冲突
- `422` - 请求参数验证失败

### 服务器错误状态码
- `500` - 服务器内部错误
- `502` - 网关错误
- `503` - 服务不可用

## 响应数据验证

系统会自动验证响应数据格式：
```typescript
// 在路由中设置响应schema
c.set('respSchema', UserOutputSchema)

// send函数会自动验证响应数据
return success(c, userData) // 自动验证userData是否符合UserOutputSchema
```

## 强制要求

1. **所有API接口必须使用统一响应结构**
2. **禁止直接使用 `c.json()` 返回响应**，必须使用响应工具函数
3. **状态码必须与HTTP状态码保持一致**
4. **错误消息必须清晰明确，便于前端处理**
5. **成功响应的data字段不能为null或undefined**（除非业务需要）

## 示例

### 正确示例
```typescript
// ✅ 正确：使用统一响应结构
export async function getUser(c: Context) {
  const user = await userService.findById(id)
  if (!user) {
    return fail(c, '用户不存在', 404)
  }
  return success(c, user, '获取用户信息成功')
}
```

### 错误示例
```typescript
// ❌ 错误：直接返回json
export async function getUser(c: Context) {
  const user = await userService.findById(id)
  return c.json({ user }) // 不符合统一响应结构
}

// ❌ 错误：不一致的响应格式
export async function getUser(c: Context) {
  return c.json({
    status: 'success',
    result: user,
    msg: 'ok'
  })
}
```

## 异常处理

系统异常会自动转换为统一响应格式：
```typescript
// 抛出异常会被全局异常处理器捕获并转换为统一响应
throw new BadRequestException('参数错误')
// 自动转换为: { code: 400, message: '参数错误', data: null }
```
