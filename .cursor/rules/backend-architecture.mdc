---
description:
globs:
alwaysApply: false
---
# Backend System Architecture

The backend system service is built with a modular architecture focusing on clean separation of concerns.

## Core Components

### Database Layer
- **Prisma Schema**: [backends/system/prisma/schema.prisma](mdc:backends/system/prisma/schema.prisma) - Database schema definition
- **DB Utils**: [backends/system/src/db/utils](mdc:backends/system/src/db/utils) - Database utility functions
- **DB Extensions**: [backends/system/src/db/extensions](mdc:backends/system/src/db/extensions) - Database extension functions
- **Migrations**: [backends/system/prisma/migrations](mdc:backends/system/prisma/migrations) - Database migrations
- **Seeds**: [backends/system/prisma/seeds](mdc:backends/system/prisma/seeds) - Database seed data

### API Routes
- **User Management**: [backends/system/src/routes/user](mdc:backends/system/src/routes/user) - User CRUD operations
- **Role Management**: [backends/system/src/routes/role](mdc:backends/system/src/routes/role) - Role and permissions
- **Menu Management**: [backends/system/src/routes/menu](mdc:backends/system/src/routes/menu) - Menu structure
- **Department**: [backends/system/src/routes/department](mdc:backends/system/src/routes/department) - Department management
- **Micro-app**: [backends/system/src/routes/micro-app](mdc:backends/system/src/routes/micro-app) - Micro-app registration
- **API Resource**: [backends/system/src/routes/api-resource](mdc:backends/system/src/routes/api-resource) - API resource management
- **Common**: [backends/system/src/routes/common](mdc:backends/system/src/routes/common) - Common API endpoints
- **Permission**: [backends/system/src/routes/permission](mdc:backends/system/src/routes/permission) - Permission management

### Middleware
- **Core Middlewares**: [backends/system/src/middlewares](mdc:backends/system/src/middlewares) - Request processing middlewares

### Core System Components
- **Exception Handling**: [backends/system/src/core/exceptions](mdc:backends/system/src/core/exceptions) - Error handling utilities
- **OpenAPI**: [backends/system/src/core/openapi](mdc:backends/system/src/core/openapi) - OpenAPI specification
- **Response Handling**: [backends/system/src/core/response](mdc:backends/system/src/core/response) - Response formatting utilities

### Utils & Helpers
- **Data Processing**: [backends/system/src/utils/data](mdc:backends/system/src/utils/data) - Data transformation helpers
- **Polyfills**: [backends/system/src/utils/polyfills](mdc:backends/system/src/utils/polyfills) - Polyfills for compatibility

## Schema Validation
- **Common Schemas**: [backends/system/src/schemas/common](mdc:backends/system/src/schemas/common) - Shared validation schemas
- **Zod Schemas**: [backends/system/src/gen/zod](mdc:backends/system/src/gen/zod) - Generated type-safe schemas
  - **Input Schemas**: [backends/system/src/gen/zod/inputTypeSchemas](mdc:backends/system/src/gen/zod/inputTypeSchemas) - Input validation schemas
  - **Model Schemas**: [backends/system/src/gen/zod/modelSchema](mdc:backends/system/src/gen/zod/modelSchema) - Model validation schemas
  - **Output Schemas**: [backends/system/src/gen/zod/outputTypeSchemas](mdc:backends/system/src/gen/zod/outputTypeSchemas) - Output validation schemas
