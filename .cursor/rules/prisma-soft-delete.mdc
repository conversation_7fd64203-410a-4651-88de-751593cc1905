---
description: 
globs: 
alwaysApply: false
---
# Prisma 软删除扩展使用指南

## 概述

项目使用 `prisma-extension-soft-delete` 扩展实现软删除功能，自动处理删除操作和查询过滤。

## 核心原理

### 软删除扩展配置

- 位置：`backends/system/src/db/index.ts`
- 支持的模型：User, Role, Permission, Menu, UserRole, RolePermission, RoleMenu, DepartmentMemberRole
- 删除字段：`deletedAt`
- 未删除值：`-1`
- 已删除值：时间戳（毫秒）

### 自动处理机制

1. **删除操作转换**：`delete` → `update`，`deleteMany` → `updateMany`
2. **查询自动过滤**：所有查询自动添加 `deletedAt: -1` 条件
3. **关联查询处理**：`include` 和嵌套查询也会自动过滤已删除记录

## 开发规范

### ✅ 正确做法

#### 1. 普通查询 - 无需手动添加 deletedAt 条件

```typescript
// ✅ 正确 - 扩展会自动过滤已删除记录
const users = await db.user.findMany({
  where: {
    name: { contains: '<PERSON>' },
    enabled: 1
  },
  include: {
    userRoles: {
      include: { role: true }
    }
  }
})

// ✅ 正确 - 单条查询也会自动过滤
const user = await db.user.findUnique({
  where: { id: userId }
})
```

#### 2. 软删除操作

```typescript
// ✅ 正确 - 使用标准删除方法，扩展会自动转换为更新操作
await db.user.delete({
  where: { id: userId }
})

// ✅ 正确 - 批量软删除
await db.user.deleteMany({
  where: { 
    departmentId: deptId 
  }
})

// ✅ 正确 - 使用软删除扩展的标准删除方法（推荐）
await db.user.delete({
  where: { id: userId }
})

// ✅ 正确 - 手动软删除（仅在需要记录额外操作者信息时使用）
// 注意：这种方式会绕过软删除扩展，需要手动设置 deletedAt
await db.user.update({
  where: { id: userId },
  data: {
    deletedAt: Date.now(),
    updatedBy: operator.account,
    updaterName: operator.name
  }
})
```

#### 3. 显式查询已删除记录

```typescript
// ✅ 正确 - 显式查询已删除记录
const deletedUsers = await db.user.findMany({
  where: {
    deletedAt: { not: -1 }  // 查询已删除的记录
  }
})

// ✅ 正确 - 查询所有记录（包括已删除）
const allUsers = await db.$queryRaw`
  SELECT * FROM sys_user
`
```

### ❌ 错误做法

#### 1. 手动添加 deletedAt 条件（多余）

```typescript
// ❌ 错误 - 不需要手动添加 deletedAt 条件
const users = await db.user.findMany({
  where: {
    name: { contains: 'John' },
    deletedAt: -1  // 多余的条件，扩展会自动添加
  }
})
```

#### 2. 硬删除操作

```typescript
// ❌ 错误 - 避免使用原生 SQL 进行硬删除
await db.$executeRaw`DELETE FROM sys_user WHERE id = ${userId}`

// ❌ 错误 - 绕过软删除扩展
await db.user.delete({
  where: { id: userId }
}).$queryRaw  // 不要这样做
```

#### 3. 在关联查询中手动过滤

```typescript
// ❌ 错误 - 关联查询中不需要手动过滤
const posts = await db.post.findMany({
  include: {
    comments: {
      where: {
        deletedAt: -1  // 多余，扩展会自动处理
      }
    }
  }
})
```

## 数据库模式要求

### 必需字段

所有支持软删除的模型必须包含 `deletedAt` 字段：

```prisma
model User {
  id        String   @id @default(nanoid())
  // ... 其他字段
  deletedAt Int?     @default(-1) @map("deleted_at") /// 删除标记, -1 表示未删除，数字表示时间戳
  
  @@unique([email, deletedAt])  // 唯一约束需要包含 deletedAt
  @@map("sys_user")
}
```

### 唯一约束处理

对于有唯一约束的字段，需要将 `deletedAt` 包含在约束中：

```prisma
// ✅ 正确 - 复合唯一约束
@@unique([email, deletedAt])
@@unique([code, deletedAt])

// ❌ 错误 - 单独的唯一约束可能导致软删除后无法创建同名记录
@@unique([email])
```

## 特殊场景处理

### 1. 恢复已删除记录

```typescript
// 恢复软删除的记录
await db.user.updateMany({
  where: {
    id: userId,
    deletedAt: { not: -1 }
  },
  data: {
    deletedAt: -1,
    updatedBy: operator.account,
    updaterName: operator.name
  }
})
```

### 2. 级联软删除

```typescript
// 删除用户时，同时软删除相关记录
await db.$transaction(async (tx) => {
  // 软删除用户
  await tx.user.delete({ where: { id: userId } })
  
  // 软删除用户角色关联
  await tx.userRole.deleteMany({ where: { userId } })
  
  // 软删除部门成员关系
  await tx.departmentMember.deleteMany({ where: { userId } })
})
```

### 3. 统计查询

```typescript
// 统计活跃用户数量（自动排除已删除）
const activeUserCount = await db.user.count()

// 统计已删除用户数量
const deletedUserCount = await db.user.count({
  where: {
    deletedAt: { not: -1 }
  }
})
```

## 性能优化建议

### 1. 索引优化

```sql
-- 为 deletedAt 字段添加索引
CREATE INDEX idx_user_deleted_at ON sys_user(deleted_at);

-- 为复合查询添加复合索引
CREATE INDEX idx_user_dept_deleted ON sys_user(department_id, deleted_at);
```

### 2. 定期清理

```typescript
// 定期硬删除长期软删除的记录（可选）
const threeMonthsAgo = Date.now() - (90 * 24 * 60 * 60 * 1000)

await db.$executeRaw`
  DELETE FROM sys_user 
  WHERE deleted_at > 0 AND deleted_at < ${threeMonthsAgo}
`
```

## 调试和监控

### 1. 查看软删除扩展状态

```typescript
// 检查扩展是否正确应用
console.log('Soft delete extension applied:', !!db._extensions)
```

### 2. 监控软删除操作

```typescript
// 在服务层添加日志
async delete(id: string, operator: Operator) {
  logger.info(`Soft deleting user ${id} by ${operator.account}`)
  
  const result = await db.user.delete({ where: { id } })
  
  logger.info(`User ${id} soft deleted successfully`)
  return result
}
```

## 注意事项

1. **扩展自动处理**：不要手动添加 `deletedAt` 查询条件，扩展会自动处理
2. **唯一约束**：包含 `deletedAt` 字段在唯一约束中，避免软删除后的数据冲突
3. **关联查询**：扩展会自动处理 `include` 和嵌套查询中的软删除过滤
4. **性能考虑**：为 `deletedAt` 字段添加适当的数据库索引
5. **数据一致性**：使用事务确保相关记录的软删除操作原子性

## 相关文件

- 扩展配置：`backends/system/src/db/index.ts`
- 扩展实现：`backends/system/src/db/extensions/soft-delete.extension.ts`
- 数据模型：`backends/system/prisma/schema.prisma`
- 使用示例：`backends/system/src/services/*.service.ts`
