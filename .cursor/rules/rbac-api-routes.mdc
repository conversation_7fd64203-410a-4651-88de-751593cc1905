---
description: 
globs: 
alwaysApply: false
---
# RBAC API 路由设计指南

## API 路由结构

基于 RESTful 设计原则，RBAC 系统的 API 路由采用层次化结构，清晰地反映资源关系。

```
/api/system/
├── roles/                    # 角色管理
├── permissions/              # 权限管理
├── users/                    # 用户管理
│   └── {userId}/roles/       # 用户角色分配
├── departments/              # 部门管理
│   └── {deptId}/
│       ├── members/          # 部门成员管理
│       └── roles/            # 部门角色管理
├── menus/                    # 菜单管理
└── auth/
    └── me/                   # 当前用户信息
        ├── permissions/      # 我的权限
        ├── roles/           # 我的角色
        └── menus/           # 我的菜单
```

## 路由文件位置

### 核心路由文件
- [backends/system/src/routes/role/index.ts](mdc:backends/system/src/routes/role/index.ts) - 角色管理路由
- [backends/system/src/routes/permission/index.ts](mdc:backends/system/src/routes/permission/index.ts) - 权限管理路由
- [backends/system/src/routes/user/index.ts](mdc:backends/system/src/routes/user/index.ts) - 用户管理路由
- [backends/system/src/routes/department/index.ts](mdc:backends/system/src/routes/department/index.ts) - 部门管理路由
- [backends/system/src/routes/menu/index.ts](mdc:backends/system/src/routes/menu/index.ts) - 菜单管理路由
- [backends/system/src/routes/auth/index.ts](mdc:backends/system/src/routes/auth/index.ts) - 认证相关路由

### 路由入口文件
- [backends/system/src/routes/index.ts](mdc:backends/system/src/routes/index.ts) - 路由汇总和注册

## 角色管理 API

### 基础 CRUD 操作

```typescript
// 角色列表 - 支持分页、筛选、排序
GET    /api/system/roles
Query: {
  page?: number;           // 页码
  pageSize?: number;       // 每页数量
  keyword?: string;        // 关键词搜索
  type?: number;          // 角色类型筛选
  enabled?: number;       // 启用状态筛选
  parentId?: string;      // 父角色筛选
}
Response: PaginatedResult<Role>

// 角色详情
GET    /api/system/roles/{roleId}
Response: Role

// 创建角色
POST   /api/system/roles
Body: CreateRoleRequest
Response: Role

// 更新角色
PUT    /api/system/roles/{roleId}
Body: UpdateRoleRequest
Response: Role

// 删除角色
DELETE /api/system/roles/{roleId}
Response: { message: string }
```

### 角色权限管理

```typescript
// 获取角色权限
GET    /api/system/roles/{roleId}/permissions
Response: Permission[]

// 设置角色权限（覆盖）
PUT    /api/system/roles/{roleId}/permissions
Body: { permissionIds: string[] }
Response: { message: string }

// 添加角色权限
POST   /api/system/roles/{roleId}/permissions
Body: { permissionIds: string[] }
Response: { message: string }

// 移除角色权限
DELETE /api/system/roles/{roleId}/permissions
Body: { permissionIds: string[] }
Response: { message: string }
```

### 角色菜单管理

```typescript
// 获取角色菜单
GET    /api/system/roles/{roleId}/menus
Response: Menu[]

// 设置角色菜单
PUT    /api/system/roles/{roleId}/menus
Body: { menuIds: string[] }
Response: { message: string }
```

### 角色层级管理

```typescript
// 角色树结构
GET    /api/system/roles/tree
Response: RoleTreeNode[]

// 添加子角色
POST   /api/system/roles/{roleId}/children
Body: CreateRoleRequest
Response: Role

// 获取子角色列表
GET    /api/system/roles/{roleId}/children
Response: Role[]

// 获取角色祖先链
GET    /api/system/roles/{roleId}/ancestors
Response: Role[]
```

## 用户角色管理 API

### 全局角色管理

```typescript
// 获取用户全局角色
GET    /api/system/users/{userId}/roles
Response: Role[]

// 分配全局角色
POST   /api/system/users/{userId}/roles
Body: { roleIds: string[] }
Response: { message: string }

// 移除全局角色
DELETE /api/system/users/{userId}/roles
Body: { roleIds: string[] }
Response: { message: string }

// 获取用户所有权限
GET    /api/system/users/{userId}/permissions
Response: Permission[]

// 获取用户有效角色（全局+部门）
GET    /api/system/users/{userId}/effective-roles
Response: UserEffectiveRoles
```

## 部门权限管理 API

### 部门成员管理

```typescript
// 部门成员列表
GET    /api/system/departments/{deptId}/members
Query: {
  page?: number;
  pageSize?: number;
  type?: DepartmentMemberType;  // 成员类型筛选
}
Response: PaginatedResult<DepartmentMember>

// 添加部门成员
POST   /api/system/departments/{deptId}/members
Body: {
  userId: string;
  type: DepartmentMemberType;
}
Response: { message: string }

// 移除部门成员
DELETE /api/system/departments/{deptId}/members/{userId}
Response: { message: string }

// 更新成员类型
PUT    /api/system/departments/{deptId}/members/{userId}
Body: { type: DepartmentMemberType }
Response: { message: string }
```

### 部门成员角色管理

```typescript
// 获取成员部门角色
GET    /api/system/departments/{deptId}/members/{userId}/roles
Response: Role[]

// 分配部门角色
POST   /api/system/departments/{deptId}/members/{userId}/roles
Body: { roleIds: string[] }
Response: { message: string }

// 移除部门角色
DELETE /api/system/departments/{deptId}/members/{userId}/roles
Body: { roleIds: string[] }
Response: { message: string }
```

### 部门权限查询

```typescript
// 获取可分配的角色列表
GET    /api/system/departments/{deptId}/assignable-roles
Response: Role[]

// 获取子部门列表
GET    /api/system/departments/{deptId}/children
Response: Department[]

// 获取所有下级部门（递归）
GET    /api/system/departments/{deptId}/descendants
Response: Department[]

// 获取当前用户可管理的部门列表
GET    /api/system/departments/manageable
Response: Department[]

// 获取部门数据权限范围
GET    /api/system/departments/{deptId}/data-scope
Response: {
  scope: string;
  accessibleDepartments: string[];
  accessibleUsers: string[];
}

// 验证对部门的访问权限
POST   /api/system/departments/{deptId}/validate-access
Body: {
  action: string;  // read, update, delete
  resource?: string;
}
Response: { hasAccess: boolean; reason?: string }
```

## 当前用户权限 API

### 用户信息和权限

```typescript
// 当前用户信息
GET    /api/system/auth/me/profile
Response: UserProfile

// 当前用户所有权限
GET    /api/system/auth/me/permissions
Response: {
  globalPermissions: string[];
  departmentPermissions: {
    departmentId: string;
    departmentName: string;
    permissions: string[];
  }[];
  allPermissions: string[];
}

// 当前用户所有角色
GET    /api/system/auth/me/roles
Response: {
  globalRoles: Role[];
  departmentRoles: {
    departmentId: string;
    departmentName: string;
    roles: Role[];
  }[];
}

// 当前用户可访问菜单
GET    /api/system/auth/me/menus
Response: MenuTreeNode[]

// 当前用户所属部门
GET    /api/system/auth/me/departments
Response: {
  departments: Department[];
  manageableDepartments: Department[];
}
```

### 权限检查 API

```typescript
// 检查单个权限
POST   /api/system/auth/me/check-permission
Body: { permission: string }
Response: { hasPermission: boolean }

// 批量检查权限
POST   /api/system/auth/me/check-permissions
Body: { 
  permissions: string[];
  mode: 'ALL' | 'ANY';
}
Response: { 
  hasPermission: boolean;
  results: { [permission: string]: boolean };
}

// 检查角色
POST   /api/system/auth/me/check-role
Body: { 
  role: string;
  scope?: 'GLOBAL' | 'DEPARTMENT';
}
Response: { hasRole: boolean }

// 检查菜单访问权限
POST   /api/system/auth/me/check-menu-access
Body: { menuPath: string }
Response: { hasAccess: boolean }
```

## 权限管理 API

### 权限 CRUD

```typescript
// 权限列表
GET    /api/system/permissions
Query: {
  page?: number;
  pageSize?: number;
  category?: string;    // 权限分类筛选
  keyword?: string;     // 关键词搜索
}
Response: PaginatedResult<Permission>

// 权限详情
GET    /api/system/permissions/{permissionId}
Response: Permission

// 创建权限
POST   /api/system/permissions
Body: CreatePermissionRequest
Response: Permission

// 更新权限
PUT    /api/system/permissions/{permissionId}
Body: UpdatePermissionRequest
Response: Permission

// 删除权限
DELETE /api/system/permissions/{permissionId}
Response: { message: string }
```

### 权限分类管理

```typescript
// 按分类获取权限
GET    /api/system/permissions/by-category/{category}
Response: Permission[]

// 权限树结构
GET    /api/system/permissions/tree
Response: PermissionTreeNode[]

// 获取所有权限分类
GET    /api/system/permissions/categories
Response: string[]
```

## 菜单管理 API

### 菜单 CRUD

```typescript
// 菜单列表
GET    /api/system/menus
Query: {
  microAppId?: string;  // 微应用筛选
  enabled?: number;     // 启用状态筛选
  parentId?: string;    // 父菜单筛选
}
Response: Menu[]

// 菜单树结构
GET    /api/system/menus/tree
Response: MenuTreeNode[]

// 菜单详情
GET    /api/system/menus/{menuId}
Response: Menu

// 创建菜单
POST   /api/system/menus
Body: CreateMenuRequest
Response: Menu

// 更新菜单
PUT    /api/system/menus/{menuId}
Body: UpdateMenuRequest
Response: Menu

// 删除菜单
DELETE /api/system/menus/{menuId}
Response: { message: string }
```

## 请求/响应类型定义

### 请求类型

```typescript
// 创建角色请求
interface CreateRoleRequest {
  code: string;                    // 角色编码
  name: string;                    // 角色名称
  type?: number;                   // 角色类型 0:系统 1:业务
  parentId?: string;               // 父角色ID
  dataScope?: number;              // 数据权限范围
  enabled?: number;                // 是否启用
  description?: string;            // 角色描述
  permissionIds?: string[];        // 权限ID列表
  menuIds?: string[];              // 菜单ID列表
}

// 更新角色请求
interface UpdateRoleRequest {
  name?: string;
  dataScope?: number;
  enabled?: number;
  description?: string;
}

// 创建权限请求
interface CreatePermissionRequest {
  code: string;                    // 权限编码
  name: string;                    // 权限名称
  category?: string;               // 权限分类
  resource?: string;               // 资源标识
  action?: string;                 // 操作标识
  description?: string;            // 权限描述
}
```

### 响应类型

```typescript
// 用户有效角色响应
interface UserEffectiveRoles {
  userId: string;
  globalRoles: Role[];             // 全局角色
  departmentRoles: {               // 部门角色
    departmentId: string;
    departmentName: string;
    roles: Role[];
  }[];
  allRoles: Role[];               // 所有角色（去重）
}

// 用户权限响应
interface UserPermissionsResponse {
  userId: string;
  globalPermissions: string[];     // 全局权限
  departmentPermissions: {         // 部门权限
    departmentId: string;
    departmentName: string;
    permissions: string[];
  }[];
  allPermissions: string[];       // 所有权限（去重）
  dataScope: string[];            // 数据权限范围
}

// 角色树节点
interface RoleTreeNode {
  id: string;
  code: string;
  name: string;
  type: number;
  enabled: number;
  children?: RoleTreeNode[];
}

// 菜单树节点
interface MenuTreeNode {
  id: string;
  name: string;
  path?: string;
  icon?: string;
  sort: number;
  children?: MenuTreeNode[];
}
```

## 错误响应格式

### 标准错误响应

```typescript
interface ErrorResponse {
  error: {
    code: string;                  // 错误码
    message: string;               // 错误信息
    details?: any;                 // 详细信息
  };
  timestamp: string;               // 时间戳
  path: string;                    // 请求路径
}

// 常见错误码
const ERROR_CODES = {
  UNAUTHORIZED: 'UNAUTHORIZED',           // 401 - 未认证
  FORBIDDEN: 'FORBIDDEN',                 // 403 - 权限不足
  NOT_FOUND: 'NOT_FOUND',                // 404 - 资源不存在
  VALIDATION_ERROR: 'VALIDATION_ERROR',   // 400 - 参数验证错误
  ROLE_IN_USE: 'ROLE_IN_USE',            // 400 - 角色正在使用中
  PERMISSION_DENIED: 'PERMISSION_DENIED', // 403 - 权限被拒绝
  DEPARTMENT_NOT_EMPTY: 'DEPARTMENT_NOT_EMPTY', // 400 - 部门不为空
};
```

## API 使用示例

### 分配用户角色

```typescript
// 1. 获取可分配的角色列表
const roles = await fetch('/api/system/roles?type=1&enabled=1');

// 2. 分配全局角色
await fetch(`/api/system/users/${userId}/roles`, {
  method: 'POST',
  body: JSON.stringify({ roleIds: ['role1', 'role2'] })
});

// 3. 分配部门角色
await fetch(`/api/system/departments/${deptId}/members/${userId}/roles`, {
  method: 'POST',
  body: JSON.stringify({ roleIds: ['role3', 'role4'] })
});
```

### 检查用户权限

```typescript
// 1. 获取用户所有权限
const permissions = await fetch(`/api/system/users/${userId}/permissions`);

// 2. 检查特定权限
const hasPermission = await fetch('/api/system/auth/me/check-permission', {
  method: 'POST',
  body: JSON.stringify({ permission: 'system:user:create' })
});

// 3. 批量检查权限
const permissionResults = await fetch('/api/system/auth/me/check-permissions', {
  method: 'POST',
  body: JSON.stringify({ 
    permissions: ['system:user:create', 'system:user:update'],
    mode: 'ALL'
  })
});
```

### 部门权限管理

```typescript
// 1. 获取部门成员
const members = await fetch(`/api/system/departments/${deptId}/members`);

// 2. 添加部门成员
await fetch(`/api/system/departments/${deptId}/members`, {
  method: 'POST',
  body: JSON.stringify({ 
    userId: 'user123',
    type: 'MEMBER'
  })
});

// 3. 分配部门角色
await fetch(`/api/system/departments/${deptId}/members/${userId}/roles`, {
  method: 'POST',
  body: JSON.stringify({ roleIds: ['dept_role1'] })
});

// 4. 验证部门访问权限
const accessCheck = await fetch(`/api/system/departments/${deptId}/validate-access`, {
  method: 'POST',
  body: JSON.stringify({ action: 'read' })
});
```

## 性能优化建议

### 1. 分页查询

```typescript
// 大数据量列表使用分页
GET /api/system/roles?page=1&pageSize=20
```

### 2. 字段筛选

```typescript
// 只返回必要字段
GET /api/system/roles?fields=id,name,code
```

### 3. 缓存策略

```typescript
// 权限相关数据设置适当的缓存头
Cache-Control: max-age=300  // 5分钟缓存
```

### 4. 批量操作

```typescript
// 批量分配角色而不是逐个分配
POST /api/system/users/batch-assign-roles
Body: {
  assignments: [
    { userId: 'user1', roleIds: ['role1', 'role2'] },
    { userId: 'user2', roleIds: ['role3'] }
  ]
}
```
