---
description:
globs:
alwaysApply: false
---
# Project Structure Guide

This is a monorepo project managed by Turborepo, containing multiple applications and packages.

## Main Components

### Backends
- `backends/gateway`: API Gateway service
- `backends/system`: Main system backend service with Prisma ORM integration

### Frontends
- `frontends/host`: Main Next.js host application
- `frontends/microapp-demo-nextjs`: Next.js based micro-frontend demo
- `frontends/microapp-demo-umi`: UmiJS based micro-frontend demo
- `frontends/microapp-demo-vite`: Vite based micro-frontend demo

### Packages
- `packages/design-system`: Shared UI components and design system
- `packages/eslint-config`: Shared ESLint configuration
- `packages/fe-helper`: Frontend utilities and helpers
- `packages/micro-app`: Micro-frontend framework implementation
- `packages/typescript-config`: Shared TypeScript configuration
- `packages/ui`: Common UI components

## Key Configuration Files
- [package.json](mdc:package.json): Root package configuration and scripts
- [turbo.json](mdc:turbo.json): Turborepo configuration
- [pnpm-workspace.yaml](mdc:pnpm-workspace.yaml): PNPM workspace configuration
