---
description: 
globs: 
alwaysApply: false
---
# RBAC Service 层架构指南

## Service 层设计原则

RBAC 系统采用分层服务架构，每个服务专注于特定的功能领域，遵循单一职责原则。

## 核心服务架构

### 服务依赖关系图
```
AuthorizationService (权限检查核心)
├── UserRoleService (用户角色管理)
├── DepartmentRoleService (部门角色管理)
├── DataScopeService (数据权限范围)
├── CacheService (权限缓存)
└── PermissionService (权限管理)

UserRoleService
├── RoleService (角色管理)
└── CacheService

DepartmentRoleService
├── RoleService
├── DepartmentMemberService (部门成员)
└── AuthorizationService

DataScopeService
├── DepartmentService (部门管理)
├── DepartmentMemberService
└── UserRoleService

MenuPermissionService (菜单权限)
├── RoleService
├── UserRoleService
└── DepartmentRoleService
```

## 服务文件位置

### 核心服务文件
- [backends/system/src/services/authorization.service.ts](mdc:backends/system/src/services/authorization.service.ts) - 权限检查核心逻辑
- [backends/system/src/services/role.service.ts](mdc:backends/system/src/services/role.service.ts) - 角色管理服务
- [backends/system/src/services/permission.service.ts](mdc:backends/system/src/services/permission.service.ts) - 权限管理服务
- [backends/system/src/services/user-role.service.ts](mdc:backends/system/src/services/user-role.service.ts) - 用户角色关联服务
- [backends/system/src/services/department.service.ts](mdc:backends/system/src/services/department.service.ts) - 部门管理服务
- [backends/system/src/services/department-member.service.ts](mdc:backends/system/src/services/department-member.service.ts) - 部门成员管理
- [backends/system/src/services/department-role.service.ts](mdc:backends/system/src/services/department-role.service.ts) - 部门角色管理
- [backends/system/src/services/data-scope.service.ts](mdc:backends/system/src/services/data-scope.service.ts) - 数据权限范围检查
- [backends/system/src/services/menu-permission.service.ts](mdc:backends/system/src/services/menu-permission.service.ts) - 菜单权限管理
- [backends/system/src/services/cache.service.ts](mdc:backends/system/src/services/cache.service.ts) - 权限缓存管理

### 服务接口定义
- [backends/system/src/types/services.ts](mdc:backends/system/src/types/services.ts) - 所有服务接口定义

## 服务职责说明

### AuthorizationService - 权限检查核心
```typescript
interface AuthorizationService {
  // 权限检查
  checkPermission(userId: string, permission: string): Promise<boolean>;
  checkPermissions(userId: string, permissions: string[], mode: 'ALL' | 'ANY'): Promise<boolean>;
  checkRole(userId: string, role: string, scope?: 'GLOBAL' | 'DEPARTMENT'): Promise<boolean>;
  
  // 数据权限检查
  checkDataPermission(userId: string, resource: string, action: string, context: any): Promise<boolean>;
  getUserDataScope(userId: string): Promise<string>;
  
  // 用户有效权限
  getUserEffectivePermissions(userId: string): Promise<Set<string>>;
}
```

### RoleService - 角色管理
```typescript
interface RoleService {
  // 角色 CRUD
  createRole(data: CreateRoleRequest): Promise<Role>;
  updateRole(id: string, data: UpdateRoleRequest): Promise<Role>;
  deleteRole(id: string): Promise<void>;
  getRoleById(id: string): Promise<Role | null>;
  getRoles(query: RoleQueryParams): Promise<PaginatedResult<Role>>;
  
  // 角色层级管理
  getRoleTree(): Promise<RoleTreeNode[]>;
  getRoleChildren(parentId: string): Promise<Role[]>;
  getRoleAncestors(roleId: string): Promise<Role[]>;
  
  // 角色权限管理
  assignPermissions(roleId: string, permissionIds: string[]): Promise<void>;
  removePermissions(roleId: string, permissionIds: string[]): Promise<void>;
  getRolePermissions(roleId: string): Promise<Permission[]>;
}
```

### UserRoleService - 用户角色关联
```typescript
interface UserRoleService {
  // 全局角色管理
  assignGlobalRoles(userId: string, roleIds: string[]): Promise<void>;
  removeGlobalRoles(userId: string, roleIds: string[]): Promise<void>;
  getUserGlobalRoles(userId: string): Promise<Role[]>;
  
  // 用户权限查询
  getUserEffectiveRoles(userId: string): Promise<UserEffectiveRoles>;
  getUserGlobalPermissions(userId: string): Promise<string[]>;
}
```

### DepartmentRoleService - 部门角色管理
```typescript
interface DepartmentRoleService {
  // 部门角色分配
  assignDepartmentRoles(deptId: string, userId: string, roleIds: string[]): Promise<void>;
  removeDepartmentRoles(deptId: string, userId: string, roleIds: string[]): Promise<void>;
  getUserDepartmentRoles(userId: string, deptId: string): Promise<Role[]>;
  getUserDepartmentPermissions(userId: string): Promise<string[]>;
  
  // 权限检查
  canAssignRoles(managerId: string, deptId: string, roleIds: string[]): Promise<boolean>;
}
```

### DataScopeService - 数据权限范围
```typescript
interface DataScopeService {
  // 数据权限检查
  checkDepartmentDataAccess(userId: string, targetDeptId: string): Promise<boolean>;
  checkSubordinateDataAccess(userId: string, targetDeptId: string): Promise<boolean>;
  checkSelfDataAccess(userId: string, targetUserId: string): Promise<boolean>;
  
  // 权限范围查询
  getUserAccessibleDepartments(userId: string): Promise<string[]>;
  getUserAccessibleUsers(userId: string): Promise<string[]>;
}
```

## 服务使用示例

### 权限检查服务使用
```typescript
// 在中间件中使用
export const requirePermissions = (permissions: string | string[], mode: 'ALL' | 'ANY' = 'ALL') => {
  return createMiddleware(async (c, next) => {
    const user = c.get('user');
    const authService = c.get('authorizationService') as AuthorizationService;
    
    const requiredPerms = Array.isArray(permissions) ? permissions : [permissions];
    const hasPermission = await authService.checkPermissions(user.id, requiredPerms, mode);
    
    if (!hasPermission) {
      throw new ForbiddenError(`Missing required permissions: ${requiredPerms.join(', ')}`);
    }
    
    await next();
  });
};
```

### 部门角色分配示例
```typescript
// 在路由处理器中使用
app.post('/:deptId/members/:userId/roles',
  requirePermissions('system:department:update'),
  async (c) => {
    const { deptId, userId } = c.req.params();
    const { roleIds } = await c.req.json();
    const currentUser = c.get('user');
    
    const deptRoleService = c.get('departmentRoleService') as DepartmentRoleService;
    
    // 检查当前用户是否可以分配这些角色
    const canAssignRoles = await deptRoleService.canAssignRoles(
      currentUser.id, 
      deptId, 
      roleIds
    );
    
    if (!canAssignRoles) {
      throw new ForbiddenError('Cannot assign roles that you do not possess');
    }
    
    await deptRoleService.assignDepartmentRoles(deptId, userId, roleIds);
    return c.json({ message: 'Roles assigned successfully' });
  }
);
```

## 服务实现注意事项

### 1. 依赖注入
- 服务间通过构造函数注入依赖
- 避免循环依赖
- 使用接口而非具体实现

### 2. 错误处理
- 统一的错误类型定义
- 详细的错误信息
- 适当的错误码

### 3. 缓存策略
- 权限数据缓存
- 缓存失效机制
- 批量缓存操作

### 4. 性能优化
- 批量数据库操作
- 查询优化
- 懒加载

### 5. 事务处理
- 关键操作使用数据库事务
- 回滚机制
- 数据一致性保证

## 测试策略

### 单元测试
- 每个服务独立测试
- Mock 依赖服务
- 覆盖所有公共方法

### 集成测试
- 服务间交互测试
- 数据库集成测试
- 权限检查流程测试

### 测试文件位置
- [backends/system/src/services/__tests__/](mdc:backends/system/src/services/__tests__) - 服务测试目录
