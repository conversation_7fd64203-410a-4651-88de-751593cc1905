---
description: 
globs: 
alwaysApply: false
---
# 设计系统

本项目使用一套统一的设计系统，提供一致的用户界面体验和组件共享。

## 设计系统包结构

### 核心组件
- **UI组件**: [packages/design-system/src/components/ui](mdc:packages/design-system/src/components/ui) - 核心UI组件
  - **按钮**: [packages/design-system/src/components/ui/button.tsx](mdc:packages/design-system/src/components/ui/button.tsx) - 按钮组件
  - **表单控件**: [packages/design-system/src/components/ui/form.tsx](mdc:packages/design-system/src/components/ui/form.tsx) - 表单控件
  - **对话框**: [packages/design-system/src/components/ui/dialog.tsx](mdc:packages/design-system/src/components/ui/dialog.tsx) - 对话框组件
  - **下拉菜单**: [packages/design-system/src/components/ui/dropdown.tsx](mdc:packages/design-system/src/components/ui/dropdown.tsx) - 下拉菜单组件
  - **表格**: [packages/design-system/src/components/ui/table.tsx](mdc:packages/design-system/src/components/ui/table.tsx) - 表格组件

### 样式系统
- **全局样式**: [packages/design-system/src/styles/global.css](mdc:packages/design-system/src/styles/global.css) - 全局CSS样式
- **主题配置**: [packages/design-system/src/styles/theme.ts](mdc:packages/design-system/src/styles/theme.ts) - 主题配置
- **动画**: [packages/design-system/src/styles/animations.css](mdc:packages/design-system/src/styles/animations.css) - 动画效果
- **媒体查询**: [packages/design-system/src/styles/media-queries.ts](mdc:packages/design-system/src/styles/media-queries.ts) - 响应式设计断点

### 自定义钩子
- **UI钩子**: [packages/design-system/src/hooks](mdc:packages/design-system/src/hooks) - UI相关的React钩子
  - **useTheme**: [packages/design-system/src/hooks/useTheme.ts](mdc:packages/design-system/src/hooks/useTheme.ts) - 主题管理钩子
  - **useMediaQuery**: [packages/design-system/src/hooks/useMediaQuery.ts](mdc:packages/design-system/src/hooks/useMediaQuery.ts) - 媒体查询钩子
  - **useForm**: [packages/design-system/src/hooks/useForm.ts](mdc:packages/design-system/src/hooks/useForm.ts) - 表单处理钩子

### 工具函数
- **样式工具**: [packages/design-system/src/lib/style-utils.ts](mdc:packages/design-system/src/lib/style-utils.ts) - 样式处理工具
- **主题工具**: [packages/design-system/src/lib/theme-utils.ts](mdc:packages/design-system/src/lib/theme-utils.ts) - 主题处理工具

## 基础UI包

### 组件
- **基础组件**: [packages/ui/src](mdc:packages/ui/src) - 原子级UI组件
  - **图标**: [packages/ui/src/icons](mdc:packages/ui/src/icons) - 图标组件
  - **图表**: [packages/ui/src/charts](mdc:packages/ui/src/charts) - 图表组件
  - **交互组件**: [packages/ui/src/interactive](mdc:packages/ui/src/interactive) - 交互式组件

## 设计指南

### 使用规范
- **组件用法**: [packages/design-system/docs](mdc:packages/design-system/docs) - 组件使用文档
- **设计标准**: [packages/design-system/docs/standards.md](mdc:packages/design-system/docs/standards.md) - 设计标准文档
- **色彩系统**: [packages/design-system/docs/colors.md](mdc:packages/design-system/docs/colors.md) - 色彩系统文档

### 主题切换
- **主题配置**: [packages/design-system/src/styles/themes](mdc:packages/design-system/src/styles/themes) - 多主题配置
- **暗色模式**: [packages/design-system/src/styles/themes/dark.ts](mdc:packages/design-system/src/styles/themes/dark.ts) - 暗色主题
- **亮色模式**: [packages/design-system/src/styles/themes/light.ts](mdc:packages/design-system/src/styles/themes/light.ts) - 亮色主题
