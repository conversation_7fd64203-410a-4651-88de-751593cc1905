---
description:
globs:
alwaysApply: false
---
# Development Guidelines

## Environment Requirements
- Node.js >= 20.11.0
- PNPM >= 10.10.0

## Code Style & Quality
- All code must follow the shared ESLint configuration in [packages/eslint-config](mdc:packages/eslint-config)
- TypeScript configuration is inherited from [packages/typescript-config](mdc:packages/typescript-config)
- <PERSON><PERSON><PERSON> is used for code formatting with custom plugins for imports and package.json

## Git Workflow
- Commit messages must follow conventional commit format
- Pre-commit hooks are configured using Husky
- All code changes are linted and formatted before commit

## Development Commands
- `pnpm dev`: Start development servers
- `pnpm build`: Build all packages and applications
- `pnpm lint`: Run linting
- `pnpm test`: Run tests
- `pnpm db:generate`: Generate Prisma client
- `pnpm db:migrate`: Run database migrations

## Micro-Frontend Development
- Host application is in [frontends/host](mdc:frontends/host)
- Micro-apps must follow the structure defined in [packages/micro-app](mdc:packages/micro-app)
- Use shared UI components from [packages/ui](mdc:packages/ui) and [packages/design-system](mdc:packages/design-system)
