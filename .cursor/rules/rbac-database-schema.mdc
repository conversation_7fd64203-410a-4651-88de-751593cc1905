---
description:
globs:
alwaysApply: false
---
# RBAC 数据库模式设计指南

## 数据模型文件位置

- [backends/system/prisma/schema.prisma](mdc:backends/system/prisma/schema.prisma) - 主要数据模型定义
- [backends/system/prisma/migrations/](mdc:backends/system/prisma/migrations/) - 数据库迁移文件
- [backends/system/prisma/seeds/](mdc:backends/system/prisma/seeds/) - 种子数据文件

## 核心数据模型关系

### 权限模型层次图

```
用户 (User)
├── 全局角色 (UserRole) → 角色 (Role) → 权限 (Permission)
└── 部门成员身份 (DepartmentMember)
    └── 部门角色 (DepartmentMemberRole) → 角色 (Role) → 权限 (Permission)
```

### 数据表关系图

```
User (用户表)
├── UserRole (用户全局角色关联表)
│   └── Role (角色表)
│       ├── RolePermission (角色权限关联表)
│       │   └── Permission (权限表)
│       └── RoleMenu (角色菜单关联表)
│           └── Menu (菜单表)
└── DepartmentMember (部门成员表)
    ├── Department (部门表)
    └── DepartmentMemberRole (部门成员角色关联表)
        └── Role (角色表)
```

## 核心模型详解

### User 模型 - 用户表

```prisma
model User {
  id          String   @id @default(cuid())
  email       String   @unique
  name        String?
  avatar      String?
  enabled     Int      @default(1) @db.TinyInt
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  deletedAt   BigInt   @default(-1) @map("deleted_at")

  // 关联关系
  userRoles           UserRole[]           // 全局角色
  departmentMembers   DepartmentMember[]   // 部门成员身份
  
  @@map("users")
}
```

### Role 模型 - 角色表

```prisma
model Role {
  id          String   @id @default(cuid())
  code        String   @unique              // 角色编码
  name        String                        // 角色名称
  type        Int      @default(1) @db.TinyInt  // 0:系统角色 1:业务角色
  parentId    String?  @map("parent_id")    // 父角色ID
  dataScope   Int?     @default(0) @map("data_scope") @db.TinyInt  // 数据权限范围
  enabled     Int      @default(1) @db.TinyInt
  description String?                       // 角色描述
  sort        Int      @default(0)          // 排序
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  deletedAt   BigInt   @default(-1) @map("deleted_at")

  // 自关联 - 角色层级
  parent      Role?    @relation("RoleHierarchy", fields: [parentId], references: [id])
  children    Role[]   @relation("RoleHierarchy")

  // 关联关系
  userRoles              UserRole[]              // 用户全局角色
  departmentMemberRoles  DepartmentMemberRole[]  // 部门成员角色
  rolePermissions        RolePermission[]        // 角色权限
  roleMenus              RoleMenu[]              // 角色菜单

  @@map("roles")
}
```

### Permission 模型 - 权限表

```prisma
model Permission {
  id          String   @id @default(cuid())
  code        String   @unique              // 权限编码 (如: system:user:list)
  name        String                        // 权限名称
  category    String?                       // 权限分类 (system/business/data/menu)
  resource    String?                       // 资源标识
  action      String?                       // 操作标识
  description String?                       // 权限描述
  enabled     Int      @default(1) @db.TinyInt
  sort        Int      @default(0)
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  deletedAt   BigInt   @default(-1) @map("deleted_at")

  // 关联关系
  rolePermissions RolePermission[]

  @@map("permissions")
}
```

### Department 模型 - 部门表

```prisma
model Department {
  id          String   @id @default(cuid())
  name        String                        // 部门名称
  code        String?  @unique              // 部门编码
  parentId    String?  @map("parent_id")    // 父部门ID
  treePath    String   @map("tree_path")    // 树路径 (如: 1.2.3)
  level       Int      @default(1)          // 层级
  enabled     Int      @default(1) @db.TinyInt
  description String?                       // 部门描述
  sort        Int      @default(0)
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  deletedAt   BigInt   @default(-1) @map("deleted_at")

  // 自关联 - 部门层级
  parent      Department?        @relation("DepartmentHierarchy", fields: [parentId], references: [id])
  children    Department[]       @relation("DepartmentHierarchy")

  // 关联关系
  members     DepartmentMember[] // 部门成员

  @@map("departments")
}
```

### UserRole 模型 - 用户全局角色关联表

```prisma
model UserRole {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  roleId    String   @map("role_id")
  createdAt DateTime @default(now()) @map("created_at")
  deletedAt BigInt   @default(-1) @map("deleted_at")

  // 关联关系
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role Role @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId, deletedAt])
  @@map("user_roles")
}
```

### DepartmentMember 模型 - 部门成员表

```prisma
model DepartmentMember {
  id           String   @id @default(cuid())
  userId       String   @map("user_id")
  departmentId String   @map("department_id")
  type         String   @default("MEMBER")    // OWNER:负责人 MANAGER:管理员 MEMBER:成员
  createdAt    DateTime @default(now()) @map("created_at")
  deletedAt    BigInt   @default(-1) @map("deleted_at")

  // 关联关系
  user       User                    @relation(fields: [userId], references: [id], onDelete: Cascade)
  department Department              @relation(fields: [departmentId], references: [id], onDelete: Cascade)
  roles      DepartmentMemberRole[]  // 部门角色

  @@unique([userId, departmentId, deletedAt])
  @@map("department_members")
}
```

### DepartmentMemberRole 模型 - 部门成员角色关联表

```prisma
model DepartmentMemberRole {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  roleId    String   @map("role_id")
  createdAt DateTime @default(now()) @map("created_at")
  deletedAt BigInt   @default(-1) @map("deleted_at")

  // 关联关系
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role Role @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId, deletedAt])
  @@map("department_member_roles")
}
```

### RolePermission 模型 - 角色权限关联表

```prisma
model RolePermission {
  id           String   @id @default(cuid())
  roleId       String   @map("role_id")
  permissionId String   @map("permission_id")
  createdAt    DateTime @default(now()) @map("created_at")
  deletedAt    BigInt   @default(-1) @map("deleted_at")

  // 关联关系
  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId, deletedAt])
  @@map("role_permissions")
}
```

### Menu 模型 - 菜单表

```prisma
model Menu {
  id           String   @id @default(cuid())
  name         String                        // 菜单名称
  path         String?                       // 菜单路径
  icon         String?                       // 菜单图标
  parentId     String?  @map("parent_id")    // 父菜单ID
  microAppId   String?  @map("micro_app_id") // 微应用ID
  enabled      Int      @default(1) @db.TinyInt
  hiddenInMenu Int      @default(0) @map("hidden_in_menu") @db.TinyInt
  sort         Int      @default(0)
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")
  deletedAt    BigInt   @default(-1) @map("deleted_at")

  // 自关联 - 菜单层级
  parent   Menu?  @relation("MenuHierarchy", fields: [parentId], references: [id])
  children Menu[] @relation("MenuHierarchy")

  // 关联关系
  microApp  MicroApp?  @relation(fields: [microAppId], references: [id])
  roleMenus RoleMenu[] // 角色菜单

  @@map("menus")
}
```

### RoleMenu 模型 - 角色菜单关联表

```prisma
model RoleMenu {
  id        String   @id @default(cuid())
  roleId    String   @map("role_id")
  menuId    String   @map("menu_id")
  createdAt DateTime @default(now()) @map("created_at")
  deletedAt BigInt   @default(-1) @map("deleted_at")

  // 关联关系
  role Role @relation(fields: [roleId], references: [id], onDelete: Cascade)
  menu Menu @relation(fields: [menuId], references: [id], onDelete: Cascade)

  @@unique([roleId, menuId, deletedAt])
  @@map("role_menus")
}
```

## 数据权限范围说明

### dataScope 字段值含义

```typescript
enum DataScope {
  ALL = 0,          // 全部数据权限
  DEPARTMENT = 1,   // 本部门数据权限
  SELF = 2,         // 仅个人数据权限
  SUBORDINATE = 3,  // 下级部门数据权限
}
```

### 部门成员类型说明

```typescript
enum DepartmentMemberType {
  OWNER = 'OWNER',       // 部门负责人 - 拥有部门最高权限
  MANAGER = 'MANAGER',   // 部门管理员 - 可以管理部门成员和角色
  MEMBER = 'MEMBER',     // 普通成员 - 基础部门成员
}
```

## 查询优化索引建议

### 核心索引

```sql
-- 用户角色查询优化
CREATE INDEX idx_user_roles_user_id ON user_roles(user_id, deleted_at);
CREATE INDEX idx_user_roles_role_id ON user_roles(role_id, deleted_at);

-- 部门成员查询优化
CREATE INDEX idx_department_members_user_id ON department_members(user_id, deleted_at);
CREATE INDEX idx_department_members_dept_id ON department_members(department_id, deleted_at);

-- 部门成员角色查询优化
CREATE INDEX idx_dept_member_roles_user_id ON department_member_roles(user_id, deleted_at);
CREATE INDEX idx_dept_member_roles_role_id ON department_member_roles(role_id, deleted_at);

-- 角色权限查询优化
CREATE INDEX idx_role_permissions_role_id ON role_permissions(role_id, deleted_at);
CREATE INDEX idx_role_permissions_perm_id ON role_permissions(permission_id, deleted_at);

-- 角色菜单查询优化
CREATE INDEX idx_role_menus_role_id ON role_menus(role_id, deleted_at);
CREATE INDEX idx_role_menus_menu_id ON role_menus(menu_id, deleted_at);

-- 部门层级查询优化
CREATE INDEX idx_departments_parent_id ON departments(parent_id, deleted_at);
CREATE INDEX idx_departments_tree_path ON departments(tree_path, deleted_at);

-- 角色层级查询优化
CREATE INDEX idx_roles_parent_id ON roles(parent_id, deleted_at);
CREATE INDEX idx_roles_type ON roles(type, enabled, deleted_at);

-- 菜单层级查询优化
CREATE INDEX idx_menus_parent_id ON menus(parent_id, deleted_at);
CREATE INDEX idx_menus_micro_app_id ON menus(micro_app_id, deleted_at);
```

## 常用查询模式

### 获取用户所有权限

```sql
-- 获取用户全局角色权限
SELECT DISTINCT p.code
FROM users u
JOIN user_roles ur ON u.id = ur.user_id AND ur.deleted_at = -1
JOIN roles r ON ur.role_id = r.id AND r.enabled = 1 AND r.deleted_at = -1
JOIN role_permissions rp ON r.id = rp.role_id AND rp.deleted_at = -1
JOIN permissions p ON rp.permission_id = p.id AND p.enabled = 1 AND p.deleted_at = -1
WHERE u.id = ? AND u.enabled = 1 AND u.deleted_at = -1;

-- 获取用户部门角色权限
SELECT DISTINCT p.code
FROM users u
JOIN department_member_roles dmr ON u.id = dmr.user_id AND dmr.deleted_at = -1
JOIN roles r ON dmr.role_id = r.id AND r.enabled = 1 AND r.deleted_at = -1
JOIN role_permissions rp ON r.id = rp.role_id AND rp.deleted_at = -1
JOIN permissions p ON rp.permission_id = p.id AND p.enabled = 1 AND p.deleted_at = -1
WHERE u.id = ? AND u.enabled = 1 AND u.deleted_at = -1;
```

### 获取用户可访问菜单

```sql
-- 获取用户可访问的菜单
SELECT DISTINCT m.*
FROM users u
JOIN (
  -- 全局角色菜单
  SELECT ur.user_id, rm.menu_id
  FROM user_roles ur
  JOIN role_menus rm ON ur.role_id = rm.role_id AND rm.deleted_at = -1
  WHERE ur.deleted_at = -1
  
  UNION
  
  -- 部门角色菜单
  SELECT dmr.user_id, rm.menu_id
  FROM department_member_roles dmr
  JOIN role_menus rm ON dmr.role_id = rm.role_id AND rm.deleted_at = -1
  WHERE dmr.deleted_at = -1
) user_menus ON u.id = user_menus.user_id
JOIN menus m ON user_menus.menu_id = m.id 
  AND m.enabled = 1 
  AND m.deleted_at = -1
WHERE u.id = ? AND u.enabled = 1 AND u.deleted_at = -1
ORDER BY m.sort;
```

### 检查部门数据权限

```sql
-- 检查用户是否有权限访问目标部门
SELECT COUNT(*) > 0 as has_access
FROM department_members dm
JOIN departments user_dept ON dm.department_id = user_dept.id
JOIN departments target_dept ON target_dept.id = ?
WHERE dm.user_id = ? 
  AND dm.deleted_at = -1
  AND user_dept.deleted_at = -1
  AND target_dept.deleted_at = -1
  AND (
    -- 同一部门
    user_dept.id = target_dept.id
    OR
    -- 父级部门（目标部门路径以用户部门路径开头）
    target_dept.tree_path LIKE CONCAT(user_dept.tree_path, '.%')
    OR
    target_dept.tree_path = user_dept.tree_path
  );
```

## 数据迁移注意事项

### 软删除处理

- 所有表使用 `deletedAt` 字段实现软删除
- `deletedAt = -1` 表示未删除
- `deletedAt = timestamp` 表示删除时间戳
- 唯一索引需要包含 `deletedAt` 字段

### 数据一致性

- 使用外键约束保证数据一致性
- 级联删除策略：`onDelete: Cascade`
- 关键操作使用数据库事务

### 性能考虑

- 大表分页查询
- 合理使用索引
- 避免深层递归查询
- 权限数据缓存策略

## 种子数据文件

### 初始权限数据

- [backends/system/prisma/seeds/permissions.ts](mdc:backends/system/prisma/seeds/permissions.ts) - 权限种子数据
- [backends/system/prisma/seeds/roles.ts](mdc:backends/system/prisma/seeds/roles.ts) - 角色种子数据
- [backends/system/prisma/seeds/departments.ts](mdc:backends/system/prisma/seeds/departments.ts) - 部门种子数据
- [backends/system/prisma/seeds/menus.ts](mdc:backends/system/prisma/seeds/menus.ts) - 菜单种子数据

### 数据库操作命令

```bash
# 生成 Prisma 客户端
npx prisma generate

# 运行数据库迁移
npx prisma migrate dev

# 重置数据库并运行种子数据
npx prisma migrate reset

# 运行种子数据
npx prisma db seed

# 查看数据库结构
npx prisma studio
```
