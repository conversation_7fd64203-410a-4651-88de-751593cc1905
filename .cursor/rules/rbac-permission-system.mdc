---
description:
globs:
alwaysApply: false
---
# RBAC 企业级权限控制系统

## 核心概念

### 权限模型层次
```
用户 (User)
├── 全局角色 (UserRole) → 角色 (Role) → 权限 (Permission)
└── 部门成员身份 (DepartmentMember)
    └── 部门角色 (DepartmentMemberRole) → 角色 (Role) → 权限 (Permission)
```

### 权限类型分类

#### 菜单权限 (Menu Permissions)
- **微应用权限**：控制用户可以访问哪些微应用
- **页面菜单权限**：控制用户可以看到哪些导航菜单  
- **功能按钮权限**：控制页面内的操作按钮显隐

#### 数据权限 (Data Permissions)
权限键格式：`模块:资源:操作`
- `system:user:list` - 查看用户列表
- `system:user:*` - 用户管理全部权限
- `system:*` - 系统管理全部权限
- `*` - 超级权限

数据范围控制：
- `data:scope:all` - 全部数据权限
- `data:scope:department` - 本部门数据权限
- `data:scope:self` - 仅个人数据权限
- `data:scope:subordinate` - 下级部门数据权限

## 核心文件结构

### 权限系统核心文件
- [backends/system/docs/RBAC.md](mdc:backends/system/docs/RBAC.md) - 完整设计方案
- [backends/system/prisma/schema.prisma](mdc:backends/system/prisma/schema.prisma) - 数据模型定义
- [backends/system/src/middlewares/rbac.ts](mdc:backends/system/src/middlewares/rbac.ts) - 权限中间件
- [backends/system/src/services/rbac.service.ts](mdc:backends/system/src/services/rbac.service.ts) - 权限服务层

### API 路由结构
```
/api/system/
├── roles/                    # 角色管理
├── permissions/              # 权限管理
├── users/{userId}/roles/     # 用户角色分配
├── departments/{deptId}/
│   ├── members/              # 部门成员管理
│   └── roles/                # 部门角色管理
└── auth/me/
    ├── permissions/          # 我的权限
    ├── roles/               # 我的角色
    └── menus/               # 我的菜单
```

## 开发指南

### 权限中间件使用

#### 基础权限检查
```typescript
app.get('/', 
  requirePermissions('system:user:list'),
  async (c) => { /* 业务逻辑 */ }
);
```

#### 多权限检查
```typescript
app.post('/', 
  requirePermissions(['system:user:create', 'data:scope:department'], 'ALL'),
  async (c) => { /* 业务逻辑 */ }
);
```

#### 角色检查
```typescript
app.post('/admin-action',
  requireRoles('SUPER_ADMIN', 'GLOBAL'),
  async (c) => { /* 业务逻辑 */ }
);
```

#### 数据权限检查
```typescript
app.get('/:userId',
  requireDataScope('system:user', 'read'),
  async (c) => { /* 业务逻辑 */ }
);
```

### 权限继承规则

1. **权限通配符匹配**：
   - `*` 匹配所有权限
   - `system:*` 匹配所有 system 开头的权限
   - `system:user:*` 匹配所有用户管理权限

2. **角色继承**：
   - 子角色自动继承父角色的所有权限
   - 用户最终权限 = 全局角色权限 ∪ 部门成员角色权限

3. **部门权限范围**：
   - 父级部门管理员可以管理所有子级部门
   - 部门成员角色仅在该部门内有效

### 安全原则

1. **最小权限原则**：用户默认无权限，需显式授权
2. **权限提升防护**：用户不能分配超过自己权限的角色
3. **数据隔离**：严格的部门数据权限检查
4. **审计追踪**：所有权限变更可追溯

### 性能优化

1. **缓存策略**：
   - 用户权限缓存（5分钟）
   - 角色权限缓存（30分钟）
   - 菜单权限缓存（1小时）

2. **查询优化**：
   - 使用权限查询索引
   - 支持批量权限检查
   - 懒加载权限数据

## 常见使用场景

### 部门管理员分配角色
```typescript
// 检查当前用户是否可以分配这些角色
const canAssignRoles = await departmentService.canAssignRoles(
  currentUser.id, 
  deptId, 
  roleIds
);
```

### 跨部门数据访问
```typescript
// 父级部门管理员访问子部门数据
app.get('/:deptId/projects',
  requirePermissions('business:project:list'),
  requireDataScope('department', 'read'),
  async (c) => { /* 业务逻辑 */ }
);
```

### 菜单权限控制
```typescript
// 前端根据用户菜单权限动态渲染
const { data: menus } = await fetch('/api/system/auth/me/menus');
```

## 注意事项

1. **权限检查顺序**：先检查功能权限，再检查数据权限
2. **部门角色限制**：部门管理员只能分配自己拥有的角色
3. **系统角色保护**：系统角色不允许普通用户修改
4. **权限缓存失效**：权限变更后需要清除相关缓存
