---
description: 
globs: 
alwaysApply: false
---
# Development Toolchain

This project uses a modern development toolchain with Turborepo for monorepo management.

## Package Management

- **PNPM**: Package manager with workspace support [pnpm-workspace.yaml](mdc:pnpm-workspace.yaml)
- **Node.js**: v20.19.1 (as specified in [package.json](mdc:package.json))

## Build & Development

- **Turborepo**: Monorepo build system [turbo.json](mdc:turbo.json)
- **ESLint**: Code linting [eslint.config.mjs](mdc:eslint.config.mjs)
- **Prettier**: Code formatting [.prettierrc](mdc:.prettierrc)
- **Husky**: Git hooks [.husky](mdc:.husky)
- **Commitlint**: Commit message linting [commitlint.config.cjs](mdc:commitlint.config.cjs)
- **Oxlint**: Rust-based JavaScript linter [.oxlintrc.json](mdc:.oxlintrc.json)

## Generator Templates

- **Backend Templates**: [turbo/generators/templates/backend](mdc:turbo/generators/templates/backend)
- **Frontend Templates**: [turbo/generators/templates/frontend](mdc:turbo/generators/templates/frontend)
- **Package Templates**: [turbo/generators/templates/package](mdc:turbo/generators/templates/package)

## Docker Configuration

- **Docker Compose**: [docker-compose.yml](mdc:docker-compose.yml) - Development environment setup

## TypeScript Configuration

- **Root Config**: [tsconfig.json](mdc:tsconfig.json) - Base TypeScript configuration
- **App Config**: [tsconfig.app.json](mdc:tsconfig.app.json) - Application-specific configuration 
- **Node Config**: [tsconfig.node.json](mdc:tsconfig.node.json) - Node.js specific configuration
