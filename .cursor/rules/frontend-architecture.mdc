---
description:
globs:
alwaysApply: false
---
# Frontend Architecture

This project implements a micro-frontend architecture with a Next.js host application and multiple micro-apps.

## Host Application

### Core Structure
- **Micro App Router**: [frontends/host/app/micro/[microapp]/[[...paths]]/page.tsx](mdc:frontends/host/app/micro/[microapp]/[[...paths]]/page.tsx) - Dynamic routing for micro-apps
- **Public Assets**: [frontends/host/public](mdc:frontends/host/public) - Static assets for the host application

## Micro Apps

### UmiJS Demo App
- **Pages**: [frontends/microapp-demo-umi/src/pages](mdc:frontends/microapp-demo-umi/src/pages) - Main application pages
- **Services**: [frontends/microapp-demo-umi/src/services](mdc:frontends/microapp-demo-umi/src/services) - API services
- **Components**: [frontends/microapp-demo-umi/src/components](mdc:frontends/microapp-demo-umi/src/components) - Reusable components
- **Models**: [frontends/microapp-demo-umi/src/models](mdc:frontends/microapp-demo-umi/src/models) - Data models
- **Assets**: [frontends/microapp-demo-umi/src/assets](mdc:frontends/microapp-demo-umi/src/assets) - Static assets
- **Mock Data**: [frontends/microapp-demo-umi/mock](mdc:frontends/microapp-demo-umi/mock) - Mock data for development

### Vite Demo App
- **Pages**: [frontends/microapp-demo-vite/src/pages](mdc:frontends/microapp-demo-vite/src/pages) - Application pages
- **Components**: [frontends/microapp-demo-vite/src/components](mdc:frontends/microapp-demo-vite/src/components) - UI components
- **Assets**: [frontends/microapp-demo-vite/src/assets](mdc:frontends/microapp-demo-vite/src/assets) - Static assets
- **Public**: [frontends/microapp-demo-vite/public](mdc:frontends/microapp-demo-vite/public) - Public static files

## Shared Packages

### Design System
- **UI Components**: [packages/design-system/src/components/ui](mdc:packages/design-system/src/components/ui) - Core UI components
- **Hooks**: [packages/design-system/src/hooks](mdc:packages/design-system/src/hooks) - Shared React hooks
- **Styles**: [packages/design-system/src/styles](mdc:packages/design-system/src/styles) - Global styles and themes
- **Library Helpers**: [packages/design-system/src/lib](mdc:packages/design-system/src/lib) - Utility functions

### Micro-app Framework
- **Core**: [packages/micro-app/src/microapp](mdc:packages/micro-app/src/microapp) - Micro-frontend core implementation
- **Iframe Integration**: [packages/micro-app/src/iframe](mdc:packages/micro-app/src/iframe) - Iframe-based integration
- **Wujie Integration**: [packages/micro-app/src/wujie](mdc:packages/micro-app/src/wujie) - Wujie-based integration

### Frontend Helpers
- **Providers**: [packages/fe-helper/src/providers](mdc:packages/fe-helper/src/providers) - Common context providers
- **Patches**: [packages/fe-helper/src/patch](mdc:packages/fe-helper/src/patch) - Runtime patches and polyfills
