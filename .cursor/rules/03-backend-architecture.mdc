---
description:
globs:
alwaysApply: false
---
# Backend Architecture Guide

## System Service Structure
The main system service is located in [backends/system](mdc:backends/system) with the following key components:

### Core Components
- `src/core`: Core functionality and utilities
  - `exceptions`: Custom error handling
  - `openapi`: OpenAPI/Swagger configuration
  - `response`: Standard response formatting

### Database Layer
- `prisma`: Database schema and migrations
- `src/db`: Database utilities and extensions
- `src/gen/zod`: Auto-generated Zod schemas for type validation

### API Routes
- `src/routes`: API endpoint implementations
  - `auth`: Authentication endpoints
  - `user`: User management
  - `role`: Role management
  - `permission`: Permission management
  - `department`: Department management
  - `menu`: Menu configuration
  - `micro-app`: Micro-frontend management

### Middleware
- `src/middlewares`: Request processing middleware
- `src/auth`: Authentication and authorization

### Utilities
- `src/utils`: Helper functions and utilities
- `src/schemas`: Data validation schemas
- `src/types`: TypeScript type definitions

## Best Practices
- Use Prisma for database operations
- Implement proper error handling using core exceptions
- Follow the standard response format
- Validate all input using Zod schemas
- Implement proper authentication and authorization checks
