{
  "name": "@fe/{{ name }}",
  "version": "0.0.0",
  "private": true,
  "description": "fe.{{ name }} frontend apps",
  "keywords": [
    "frontend",
    "{{ name }}"
    "apps"
  ],
  "license": "ISC",
  "author": "vic",
  "scripts": {
    "build": "next build",
    "clean": "rimraf .turbo .next .cache .deploy dist node_modules",
    "dev": "next dev --port {{#if port}} {{port}} {{else}} 3000 {{/if}}",
    "lint": "oxlint . && eslint .",
    "lint:fix": "prettier --check --write . && oxlint --fix --fix-suggestions . && eslint . --fix",
    "start": "next start",
    "test": "jest",
    "test:e2e": "playwright test",
    "test:watch": "jest --watch"
  },
  "dependencies": {

  },
  "devDependencies": {
    "@types/react": "^19.1.2",
    "@types/react-dom": "^19.1.2",
    "react": "^19.1.0",
    "react-dom": "^19.1.0",
    "typescript": "^5.8.3"
  }
}
