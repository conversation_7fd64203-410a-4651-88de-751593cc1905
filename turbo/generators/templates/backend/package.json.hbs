{"name": "@gaiaui/{{ name }}", "version": "0.0.0", "description": "gaiaui.{{ name }} component", "keywords": ["g<PERSON><PERSON>", "{{ name }}"], "license": "ISC", "author": "vic", "exports": {".": {"types": "./dist/cjs/index.d.ts", "import": "./dist/esm/index.mjs", "require": "./dist/cjs/index.js"}, "./package.json": "./package.json"}, "main": "dist/cjs/index.js", "module": "dist/esm/index.mjs", "files": ["dist"], "scripts": {"build": "tsup src"}, "dependencies": {"@gaiaui/theme-utils": "workspace:*"}, "devDependencies": {"@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.8.3"}}