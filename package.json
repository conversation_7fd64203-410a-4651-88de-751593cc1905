{"name": "devops-next", "version": "0.0.0", "private": true, "license": "UNLICENSED", "author": "Vic", "scripts": {"build": "turbo run build", "clear": "turbo clean && rimraf .turbo .deploy node_modules", "db:generate": "turbo run db:generate", "db:migrate": "turbo run db:migrate", "dev": "turbo run dev", "dp:base-app": "rm -rf .deploy && turbo dp --filter=@fe/base-app", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "prepare": "husky install", "test": "turbo run test", "test:e2e": "turbo run test:e2e"}, "lint-staged": {"*": ["prettier --check --write", "oxlint --fix --fix-suggestions", "eslint --fix"]}, "resolutions": {"@typescript-eslint/typescript-estree": "8.32.0"}, "dependencies": {"devops-next": "link:"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.25.1", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@turbo/gen": "^1.12.4", "@typescript-eslint/eslint-plugin": "^7.1.0", "cross-env": "^7.0.3", "eslint": "^9.25.1", "husky": "^9.1.7", "lint-staged": "^15.5.2", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-packagejson": "^2.5.10", "prettier-plugin-tailwindcss": "^0.6.11", "rimraf": "^6.0.1", "turbo": "^2.5.2"}, "packageManager": "pnpm@10.10.0", "engines": {"node": ">=20.11.0"}, "volta": {"node": "20.19.1"}}