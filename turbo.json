{"$schema": "https://turborepo.com/schema.json", "globalDependencies": ["**/.env.*local"], "ui": "tui", "tasks": {"dev": {"cache": false, "persistent": true}, "dp": {"dependsOn": ["build"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**"]}, "db:generate": {}, "db:migrate": {}, "lint": {}, "lint:fix": {}, "test": {}, "test:e2e": {}, "clean": {}}}