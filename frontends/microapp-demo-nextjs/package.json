{"name": "@fe/microapp-demo-nextjs", "version": "0.0.1", "private": true, "scripts": {"build": "next build", "clean": "rimraf .turbo .next .cache .deploy dist node_modules", "dev": "next dev --port 3003", "lint": "oxlint . && eslint .", "lint:fix": "prettier --check --write . && oxlint --fix --fix-suggestions . && eslint . --fix", "start": "next start --port 3003"}, "lint-staged": {"*": ["prettier --check --write", "oxlint --fix --fix-suggestions", "eslint --fix"]}, "dependencies": {"@repo/design-system": "workspace:*", "@repo/fe-helper": "workspace:*", "@repo/micro-app": "workspace:*", "@tailwindcss/postcss": "^4.1.5", "better-auth": "^1.2.7", "next": "15.3.3", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^4.1.5"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "typescript": "^5"}}