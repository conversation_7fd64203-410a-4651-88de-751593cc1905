import { FlatCompat } from '@eslint/eslintrc'
import nextEslint from '@repo/eslint-config/next.mjs'
import { defineConfig } from 'eslint/config'
import path from 'node:path'
import { fileURLToPath } from 'node:url'

const __dirname = path.dirname(fileURLToPath(import.meta.url))

const compat = new FlatCompat({
  baseDirectory: __dirname,
})

export default defineConfig([
  nextEslint,
  ...compat.config({
    root: true,
    parser: '@typescript-eslint/parser',
  }),
])
