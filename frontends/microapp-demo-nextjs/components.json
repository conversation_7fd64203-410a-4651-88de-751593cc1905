{"$schema": "https://ui.shadcn.com/schema.json", "style": "new-york", "rsc": true, "tsx": true, "tailwind": {"config": "", "css": "../../packages/design-system/src/styles/globals.css", "baseColor": "neutral", "cssVariables": true}, "iconLibrary": "lucide", "aliases": {"components": "@/components", "hooks": "@/hooks", "lib": "@/lib", "utils": "@repo/design-system/ui/lib/utils", "ui": "@repo/design-system/ui/components"}}