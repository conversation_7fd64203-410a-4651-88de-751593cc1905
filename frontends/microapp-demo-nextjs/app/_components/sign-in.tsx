'use client'

import { authClient } from '@/lib/auth-client'
import { But<PERSON> } from '@repo/design-system/components/ui/button'

export function SignIn() {
  const { data: session } = authClient.useSession()

  return (
    <div>
      <span>current user: {session?.user?.email}</span>
      <Button
        onClick={() =>
          authClient.signIn.oauth2({
            providerId: 'cvte-oauth2',
            callbackURL: 'http://localhost:3003',
          })
        }
      >
        Sign In
      </Button>
    </div>
  )
}
