import type { NextConfig } from 'next'

// const basePath = '/micro/microapp-demo-nextjs' // 默认为 '/'
// 静态资源路径前缀
// const assetPrefix = process.env.NODE_ENV === 'production' ? `线上域名${basePath}` : `http://localhost:3003${basePath}`

const nextConfig: NextConfig = {
  // basePath,
  // assetPrefix,
  publicRuntimeConfig: {
    // assetPrefix,
  },
  /* config options here */
  transpilePackages: ['@repo/design-system', '@repo/micro-app', '@repo/fe-helper'],
  headers: async () => {
    return [
      {
        source: '/(.*)',
        headers: [
          { key: 'Access-Control-Allow-Origin', value: '*' }, // replace this your actual origin
        ],
      },
    ]
  },
}

export default nextConfig
