const users = [
  { id: 0, name: '<PERSON><PERSON>', nickName: 'U', gender: '<PERSON><PERSON>' },
  { id: 1, name: '<PERSON>', nickName: 'B', gender: 'FEMALE' },
]

export default {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  'GET /api/v1/queryUserList': (req: any, res: any) => {
    res.json({
      success: true,
      data: { list: users },
      errorCode: 0,
    })
  },
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  'PUT /api/v1/user/': (req: any, res: any) => {
    res.json({
      success: true,
      errorCode: 0,
    })
  },
}
