import Guide from '@/components/Guide'
import { trim } from '@/utils/format'
import { PageContainer } from '@ant-design/pro-components'
import { useModel } from '@umijs/max'
import { Button } from 'antd'
import styles from './index.less'

const HomePage: React.FC = () => {
  const { name } = useModel('global')
  return (
    <PageContainer ghost>
      <div className={styles.container}>
        <Guide name={trim(name)} />
        <Button
          onClick={() => {
            // 获取主应用路由
            //@ts-expect-error - type error
            const baseRouter = window.microApp?.router.getBaseAppRouter()
            if (baseRouter) {
              baseRouter.push('/micro/microapp-demo-vite')
            } else {
              window.location.href = '/micro/microapp-demo-vite'
            }
          }}
        >
          去 vite 应用
        </Button>
        <Button
          onClick={() => {
            // 获取主应用路由
            //@ts-expect-error - type error
            const baseRouter = window.microApp?.router.getBaseAppRouter()
            if (baseRouter) {
              baseRouter.push('/micro/microapp-demo-vite/about')
            } else {
              window.location.href = '/micro/microapp-demo-vite/about'
            }
          }}
        >
          去 vite 应用 about 页面
        </Button>
      </div>
    </PageContainer>
  )
}

export default HomePage
