{"name": "@fe/microapp-demo-umi", "private": true, "author": "vic <<EMAIL>>", "scripts": {"build": "max build", "dev": "max dev", "format": "prettier --cache --write .", "postinstall": "max setup", "lint": "oxlint . && eslint .", "lint:fix": "prettier --check --write . && oxlint --fix --fix-suggestions . && eslint . --fix", "setup": "max setup", "start": "npm run dev"}, "lint-staged": {"*": ["prettier --check --write", "oxlint --fix --fix-suggestions", "eslint --fix"]}, "overrides": {"@types/react": "^18", "@types/react-dom": "^18"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/pro-components": "^2.8.7", "@umijs/max": "^4.4.10", "antd": "^5.25.4"}, "devDependencies": {"@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5.0.3"}}