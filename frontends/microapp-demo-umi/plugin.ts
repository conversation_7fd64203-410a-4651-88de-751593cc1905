import { IApi } from '@umijs/max'

export default (api: IApi) => {
  api.onStart(() => {
    api.logger.info('私有化插件加载')
  })

  api.onGenerateFiles(() => {
    // https://github.com/umijs/umi/issues/11638
    api.writeTmpFile({
      path: 'core/basenameRuntime.ts',
      noPluginDir: true,
      content: `
            export const modifyClientRenderOpts = (ctx) => {
              ctx.basename = window.routerBase;
                const h = ctx.history
                const originPush = h.push
                const originReplace = h.replace
                h.push = (...args) => {
                    // args[0] = ctx.basename + args[0]
                    originPush.apply(h, args)
                }
                h.replace = (...args) => {
                    // args[0] = ctx.basename + args[0]
                    originReplace.apply(h, args)
                }
              return ctx;
            }
          `,
    })
  })

  api.addRuntimePlugin(() => ['@@/core/basenameRuntime.ts'])

  // 中间件支持 cors
  api.addMiddlewares(() => {
    return function cors(req, res, next) {
      res.setHeader('Access-Control-Allow-Origin', req.headers.origin || '*')
      res.setHeader('Access-Control-Allow-Credentials', 'true')
      res.setHeader('Access-Control-Allow-Headers', 'X-Requested-With,Content-Type')
      res.setHeader('Access-Control-Allow-Methods', 'PUT,POST,GET,DELETE,OPTIONS')
      res.setHeader('Content-Type', 'application/json; charset=utf-8')
      next()
    }
  })

  api.modifyConfig((config) => {
    config.runtimePublicPath = {}
    return config
  })

  api.addHTMLHeadScripts(() => ({
    content: `if (window.__MICRO_APP_ENVIRONMENT__) {
    if (window.__MICRO_APP_PUBLIC_PATH__) window.publicPath = window.__MICRO_APP_PUBLIC_PATH__
    window.onmount = () => {console.log('APP RUN IN MICRO APP');window.__MICRO_APP_MOUNTED = true;}
}`,
  }))

  // api.addEntryImports(() => ({
  //   source: 'react-dom',
  //   specifier: `{ unmountComponentAtNode as micro_unmountComponentAtNode }`,
  // }))

  api.addEntryImports(() => ({
    source: 'umi',
    specifier: `{ __getRoot as micro_getRoot }`,
  }))

  api.addEntryCodeAhead(
    () => `
if (window.__MICRO_APP_ENVIRONMENT__) {
  console.log('APP RUN IN MICRO APP...');
  const originRender = render;
  // eslint-disable-next-line no-undef
  window.onmount = () => {
    console.log('MICRO APP MOUNTED...');
    originRender()
  };
  window.onunmount = () => {
    const root = micro_getRoot()
    console.log('MICRO APP UNMOUNTED...', root);
    if (typeof root?.unmount === 'function') {
      root.unmount();
    }
  };
  if (window.__MICRO_APP_MOUNTED) originRender()
  render = () => {}
}
    `
  )
}
