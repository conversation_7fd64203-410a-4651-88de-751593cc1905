import { defineConfig } from '@umijs/max'

export default defineConfig({
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},
  mfsu: false,
  layout: { title: '@umijs/max' },
  runtimePublicPath: {},
  headScripts: [
    `window.routerBase = window.__MICRO_APP_BASE_ROUTE__ || "/"`,
    `window.publicPath = window.__MICRO_APP_PUBLIC_PATH__ || "/"`,
  ],
  routes: [
    {
      path: '/',
      redirect: '/home',
    },
    {
      name: '首页',
      path: '/home',
      component: './Home',
    },
    {
      name: '权限演示',
      path: '/access',
      component: './Access',
    },
    {
      name: ' CRUD 示例',
      path: '/table',
      component: './Table',
    },
  ],
  npmClient: 'pnpm',
})
