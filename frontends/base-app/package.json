{"name": "@fe/base-app", "version": "0.0.1", "private": true, "scripts": {"build": "cross-env RUNNING_ENV=${RUNNING_ENV:-builder} npm run inject:env && cross-env RUNNING_ENV=${RUNNING_ENV:-builder} next build", "clean": "rimraf .turbo .next .cache .deploy dist node_modules", "dev": "cross-env RUNNING_ENV=${RUNNING_ENV:-dev} npm run inject:env && cross-env RUNNING_ENV=${RUNNING_ENV:-dev} next dev", "dp": "pnpm run dp:standalone && cp -r ./.next/standalone/frontends/base-app ../../.deploy && cp -rp ./.next/standalone/node_modules ../../.deploy ", "dp:env": "cp -r ./.env.* ./.next/standalone/frontends/base-app", "dp:pub": "cp -r ./public ./.next/standalone/frontends/base-app", "dp:standalone": "pnpm dp:pub && pnpm dp:static && pnpm dp:env", "dp:static": "cp -r ./.next/static ./.next/standalone/frontends/base-app/.next", "inject:env": "cp .env.${RUNNING_ENV:-prod} .env", "lint": "oxlint . && eslint .", "lint:fix": "prettier --check --write . && oxlint --fix --fix-suggestions . && eslint . --fix", "serve": "npm run inject:env && node ./server.js", "start": "cross-env RUNNING_ENV=${RUNNING_ENV:-fat} npm run inject:env && cross-env RUNNING_ENV=${RUNNING_ENV:-dev} next start"}, "lint-staged": {"*": ["prettier --check --write", "oxlint --fix --fix-suggestions", "eslint --fix"]}, "dependencies": {"@repo/design-system": "workspace:*", "@repo/env": "workspace:*", "@repo/fe-helper": "workspace:*", "@repo/hono-api": "workspace:*", "@repo/layouts": "workspace:*", "@repo/micro-app": "workspace:*", "@repo/mock": "workspace:*", "@tailwindcss/postcss": "^4.1.5", "antd": "^5.25.4", "axios": "^1.9.0", "dayjs": "^1.11.13", "framer-motion": "^12.11.3", "lucide-react": "^0.507.0", "next": "15.3.3", "radash": "^12.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "sonner": "^2.0.5", "swr": "^2.3.3", "tailwindcss": "^4.1.5", "zustand": "^5.0.5"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/types": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "typescript": "^5"}}