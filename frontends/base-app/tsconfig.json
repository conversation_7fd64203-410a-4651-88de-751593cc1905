{"extends": "@repo/typescript-config/nextjs.json", "compilerOptions": {"paths": {"@/*": ["./src/*"], "@repo/design-system/*": ["../../packages/design-system/src/*"], "@repo/layouts/*": ["../../packages/layouts/src/*"], "@repo/micro-app/*": ["../../packages/micro-app/src/*"], "@repo/fe-helper/*": ["../../packages/fe-helper/src/*"], "@repo/env/*": ["../../packages/env/src/*"], "@repo/types/*": ["../../packages/types/src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}