'use client'

import { useContext } from 'react'
import { AccessAuthContext } from './context'

export function useAuth() {
  const ctx = useContext(AccessAuthContext)

  if (!ctx) {
    throw new Error('useAuth must be used within an AuthProvider')
  }

  if (!ctx.user) {
    throw new Error('useAuth must be used within an AuthProvider')
  }

  if (!ctx.jwt) {
    throw new Error('useAuth must be used within an AuthProvider')
  }

  return ctx
}

export function useAuthUser() {
  const { user } = useAuth()
  return user
}

export function useAuthJwt() {
  const { jwt } = useAuth()
  return jwt
}
