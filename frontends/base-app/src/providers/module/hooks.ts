'use client'

import { useContext } from 'react'
import { ModuleContext } from './context'

export function useModuleContext() {
  const ctx = useContext(ModuleContext)
  if (!ctx) {
    throw new Error('useModuleContext must be used within a ModuleProvider.')
  }
  return ctx
}

export function useModule() {
  const { modules } = useModuleContext()
  return { modules }
}

export function useMenus() {
  const { menus } = useModuleContext()
  return menus
}

export function useCurrentModule() {
  const { currentModule } = useModuleContext()
  return currentModule
}

export function useCurrentMenu() {
  const { currentMenu } = useModuleContext()
  return currentMenu
}
