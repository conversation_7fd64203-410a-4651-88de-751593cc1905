'use client'

import { useMemo } from 'react'
import { ModuleContext, ModuleContextValue } from './context'

export interface ModuleProviderProps extends ModuleContextValue {
  children: React.ReactNode
}

export function ModuleProvider(props: ModuleProviderProps) {
  const { modules, menus, currentModule, currentMenu } = props

  const value = useMemo(() => ({ modules, menus, currentModule, currentMenu }), [modules, menus, currentModule, currentMenu])

  return <ModuleContext.Provider value={value}>{props.children}</ModuleContext.Provider>
}
