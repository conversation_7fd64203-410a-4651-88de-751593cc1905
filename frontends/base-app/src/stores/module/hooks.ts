'use client'

import { useMemo } from 'react'
import { useShallow } from 'zustand/react/shallow'

import { createStoreUse } from '@repo/fe-helper/stores'
import { ModuleInfo } from '@repo/types'

import { ModuleStoreContext } from './context'

export const useModuleStore = createStoreUse(ModuleStoreContext)

export const useModuleActions = () => {
  const { setModules } = useModuleStore(useShallow((st) => ({ setModules: st.setModules })))
  return { setModules }
}

export const useModules = () => {
  const { modules } = useModuleStore(useShallow((st) => ({ modules: st.modules })))
  return modules
}

export const useModuleMap = () => {
  const modules = useModules()
  return useMemo(() => {
    return modules.reduce<Record<string, ModuleInfo>>((pre, cur) => {
      pre[cur.code] = cur
      return pre
    }, {})
  }, [modules])
}

export const useCurrentModule = (code: string) => {
  const map = useModuleMap()

  if (!code) {
    return null
  }

  if (!map[code]) {
    return null
  }

  return map[code]
}
