import { ModuleInfo } from '@repo/types'
import { createStore } from 'zustand'

export interface ModuleState {
  modules: ModuleInfo[]
}

export interface ModuleActions {
  setModules: (modules: ModuleInfo[]) => void
}

export type ModuleStore = ModuleState & ModuleActions

export function initialModuleState(state?: Partial<ModuleState>): ModuleState {
  return { modules: [], ...state }
}

export function createModuleStore(state?: Partial<ModuleState>) {
  return createStore<ModuleStore>()((set) => ({
    ...initialModuleState(state),
    setModules: (modules) => set({ modules }),
  }))
}
