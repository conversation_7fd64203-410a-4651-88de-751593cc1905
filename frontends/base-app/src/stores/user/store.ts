import { createStore } from 'zustand'

export interface UserInfo {
  id: string
  account: string
  email: string
  name: string
  avatar: string
}

export interface UserState {
  state: 'authorized' | 'unauthorized'
  user: UserInfo | null
}

export interface UserActions {
  setUser: (user: UserInfo | null) => void
}

export type UserStore = UserState & UserActions

export function initialUserState(state?: Partial<UserState>): UserState {
  return { state: 'unauthorized', user: null, ...state }
}

export function createUserStore(state?: Partial<UserState>) {
  return createStore<UserStore>()((set) => ({
    ...initialUserState(state),
    setUser: (user) => set({ user, state: user ? 'authorized' : 'unauthorized' }),
  }))
}
