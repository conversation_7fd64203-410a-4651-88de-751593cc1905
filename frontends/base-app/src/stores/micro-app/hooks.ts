'use client'

import { useMemo } from 'react'
import { useShallow } from 'zustand/react/shallow'

import { createStoreUse } from '@repo/fe-helper/stores'
import { MicroAppInfo } from '@repo/types'

import { MicroAppStoreContext } from './context'

export const useMicroAppStore = createStoreUse(MicroAppStoreContext)

export const useMicroAppActions = () => {
  const { setApps } = useMicroAppStore(useShallow((st) => ({ setApps: st.setApps })))
  return { setApps }
}

export const useMicroApps = () => {
  const { apps } = useMicroAppStore(useShallow((st) => ({ apps: st.apps })))
  return apps
}

export const useMicroAppMap = () => {
  const apps = useMicroApps()
  return useMemo(() => {
    return apps.reduce<Record<string, MicroAppInfo>>((pre, cur) => {
      pre[cur.code] = cur
      return pre
    }, {})
  }, [apps])
}
