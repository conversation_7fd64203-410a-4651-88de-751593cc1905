import { MicroAppInfo } from '@repo/types'
import { createStore } from 'zustand'

export interface MicroAppState {
  apps: MicroAppInfo[]
}

export interface MicroAppActions {
  setApps: (apps: MicroAppInfo[]) => void
}

export type MicroAppStore = MicroAppState & MicroAppActions

export function initialMicroAppState(state?: Partial<MicroAppState>): MicroAppState {
  return { apps: [], ...state }
}

export function createMicroAppStore(state?: Partial<MicroAppState>) {
  return createStore<MicroAppStore>()((set) => ({
    ...initialMicroAppState(state),
    setApps: (apps) => set({ apps }),
  }))
}
