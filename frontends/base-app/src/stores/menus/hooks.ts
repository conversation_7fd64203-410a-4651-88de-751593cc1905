'use client'

import { useMemo } from 'react'
import { useShallow } from 'zustand/react/shallow'

import { createStoreUse } from '@repo/fe-helper/stores'
import { MenuInfo } from '@repo/types'

import { MenuStoreContext } from './context'

export const useMenuStore = createStoreUse(MenuStoreContext)

export const useMenuActions = () => {
  const { setMenus } = useMenuStore(useShallow((st) => ({ setMenus: st.setMenus })))
  return { setMenus }
}

export const useMenus = () => {
  const { menus } = useMenuStore(useShallow((st) => ({ menus: st.menus })))
  return menus
}

export const useMenuMap = () => {
  const menus = useMenus()
  return useMemo(() => {
    return menus.reduce<Record<string, MenuInfo>>((pre, cur) => {
      pre[cur.code] = cur
      return pre
    }, {})
  }, [menus])
}

export const useCurrentMenu = (code: string) => {
  const map = useMenuMap()

  if (!code) {
    return null
  }

  if (!map[code]) {
    return null
  }

  return map[code]
}
