import { MenuInfo } from '@repo/types'
import { createStore } from 'zustand'

export interface MenuState {
  menus: MenuInfo[]
}

export interface MenuActions {
  setMenus: (Menus: MenuInfo[]) => void
}

export type MenuStore = MenuState & MenuActions

export function initialMenuState(state?: Partial<MenuState>): MenuState {
  return { menus: [], ...state }
}

export function createMenuStore(state?: Partial<MenuState>) {
  return createStore<MenuStore>()((set) => ({
    ...initialMenuState(state),
    setMenus: (menus) => set({ menus }),
  }))
}
