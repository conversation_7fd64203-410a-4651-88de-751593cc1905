import { PropsWithChildren } from 'react'

import { fetchMenus, fetchModules } from '@/fetchs'
import { MenuInfo } from '@repo/types'

import { ModuleClientLayout } from './module-client-layout'

async function ModuleLayout(props: PropsWithChildren<{ params: Promise<{ module: string }> }>) {
  const { module } = await props.params

  const modules = await fetchModules()

  const menus: MenuInfo[] = await fetchMenus(module)

  const mixMenus = menus.map((menu) => ({
    ...menu,
    path: `/${module}/${menu.microapp || 'primitive'}/${menu.path.replace(/^\//g, '')}`,
  }))

  return (
    <ModuleClientLayout menus={mixMenus} modules={modules}>
      {props.children}
    </ModuleClientLayout>
  )
}

export default ModuleLayout
