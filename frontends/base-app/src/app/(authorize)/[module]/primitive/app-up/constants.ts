import { TagProps } from 'antd'
import { listify } from 'radash'

export const APP_STATE: Record<string, string> = {
  '0': '初始化',
  '4': '上线',
  '5': '下线',
  '6': '暂停',
} as const

export const APP_STATE_COLOR: Record<string, TagProps['color']> = {
  '0': 'default',
  '4': 'success',
  '5': 'error',
  '6': 'warning',
} as const

export const APP_TEST_STATE: Record<string, string> = {
  '0': '未测试',
  '1': '测试通过',
  '2': '测试未通过',
} as const

export const APP_TEST_STATE_COLOR: Record<string, TagProps['color']> = {
  '0': 'default',
  '1': 'success',
  '2': 'error',
} as const

export const APP_STATE_OPTIONS = listify(APP_STATE, (value, label) => ({ label, value }))

export const APP_TEST_STATE_OPTIONS = listify(APP_TEST_STATE, (value, label) => ({ label, value }))

export const REGION_MAP: Record<string, string> = {
  tvcn: '国内',
  tv: '海外',
} as const

export const REGION_OPTIONS = listify(REGION_MAP, (value, label) => ({ label, value }))

export const REGION_COLORS: Record<string, TagProps['color']> = {
  tvcn: 'red',
  tv: 'blue',
}
