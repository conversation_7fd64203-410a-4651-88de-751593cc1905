import { request } from '@repo/fe-helper/request'

export async function updateState(_key: string, { arg }: { arg: { id: string; state: string } }) {
  const resp = await request(`/api/proxy/app-store/admin/v1.0/app/is-online/${arg.id}/${arg.state}`)
  return resp.data
}

export async function updateTestState(_key: string, { arg }: { arg: { id: string; state: string } }) {
  const resp = await request(`/api/proxy/app-store/admin/v1.0/app/is-pass/${arg.id}/${arg.state}`)
  return resp.data
}

export async function batchUpdateState(_key: string, { arg }: { arg: { ids: string[]; state: string } }) {
  const resp = await request.post('/api/proxy/app-store/admin/v1.0/app/is-batch-online', arg)
  return resp.data
}

export async function batchUpdateTestState(_key: string, { arg }: { arg: { ids: string[]; state: string } }) {
  const resp = await request.post('/api/proxy/app-store/admin/v1.0/app/is-batch-pass', arg)
  return resp.data
}
