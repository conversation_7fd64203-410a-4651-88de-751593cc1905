'use client'

import dayjs from 'dayjs'

import { App, Image, Tag } from 'antd'
import { ArrowUpFromLine, FlaskConical } from 'lucide-react'
import { tryit } from 'radash'
import { toast } from 'sonner'
import useSWR from 'swr'
import useSWRMutation from 'swr/mutation'

import { Button } from '@repo/design-system/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@repo/design-system/components/ui/dropdown-menu'
import { cn } from '@repo/design-system/lib/utils'
import { BaseTable, BaseTableColumn, COLUMN_OPTIONS_COLUMN, Ellipsis } from '@repo/fe-helper/components'
import { usePaginationSWR } from '@repo/fe-helper/swr'
import { Any } from '@repo/types'

import { batchUpdateState, batchUpdateTestState, updateState, updateTestState } from './api'
import {
  APP_STATE,
  APP_STATE_COLOR,
  APP_STATE_OPTIONS,
  APP_TEST_STATE,
  APP_TEST_STATE_COLOR,
  APP_TEST_STATE_OPTIONS,
  REGION_COLORS,
  REGION_MAP,
  REGION_OPTIONS,
} from './constants'

export function AppUpPageContainer() {
  const { modal } = App.useApp()

  const { swr, data, setData, pagination } = usePaginationSWR(
    ['/api/proxy/app-store/admin/v1.0/app/find-app-list', { method: 'POST', data: { size: 20 } }],
    { keepPreviousData: true }
  )

  const { data: chip } = useSWR(['/api/proxy/app-store/admin/v1.0/tag/find-app-tag', { params: { type: 'chip' } }])
  const { data: sdk } = useSWR(['/api/proxy/app-store/admin/v1.0/tag/find-app-tag', { params: { type: 'sdk' } }])
  const { data: customer } = useSWR(['/api/proxy/app-store/admin/v1.0/tag/find-app-tag', { params: { type: 'customer' } }])

  const updateStateMutation = useSWRMutation('/api/proxy/app-store/admin/v1.0/app/is-online', updateState)
  const updateTestStateMutation = useSWRMutation('/api/proxy/app-store/admin/v1.0/app/is-pass', updateTestState)

  const batchUpdateStateMutation = useSWRMutation('/api/proxy/app-store/admin/v1.0/app/batch-is-online', batchUpdateState)
  const batchUpdateTestStateMutation = useSWRMutation('/api/proxy/app-store/admin/v1.0/app/is-batch-pass', batchUpdateTestState)

  const chipOptions = (chip || []).map((item: Any) => ({ label: item.cTagValue, value: `${item.cTagValue || ''}` }))
  const sdkOptions = (sdk || []).map((item: Any) => ({ label: item.cTagValue, value: `${item.cTagValue || ''}` }))
  const customerOptions = (customer || []).map((item: Any) => ({ label: item.cTagValue, value: `${item.cTagValue || ''}` }))

  const handleUpdateState = async (id: string, state: string) => {
    const resp = await updateStateMutation.trigger({ id, state })
    if (resp?.data) {
      toast.success(resp.message ?? '操作成功', {})
      swr.mutate()
      return
    }
    toast.error(resp.message ?? '操作失败')
  }

  const handleUpdateTestState = async (id: string, state: string) => {
    const resp = await updateTestStateMutation.trigger({ id, state })
    if (resp?.data) {
      toast.success(resp.message ?? '操作成功')
      swr.mutate()
      return
    }
    toast.error(resp.message ?? '操作失败')
  }

  const columns: BaseTableColumn<Any>[] = [
    { id: 'cId', title: 'ID', size: 80, fixed: 'left' },
    { id: 'cAppId', title: 'AppId', size: 80, fixed: 'left' },
    { id: 'cAppName', title: '名称', fixed: 'left' },
    { id: 'cVersionName', title: '版本', cell: (props) => <Tag>{props.getValue<string>()}</Tag> },
    {
      id: 'cAppIcon',
      title: '图标',
      size: 50,
      cell: (props) => <Image referrerPolicy="no-referrer" src={props.getValue<string>() || void 0} width={36} height={36} />,
    },

    {
      id: 'customerValue',
      title: '客户',
      hiddenInColumn: true,
      filter: {
        type: 'select',
        props: { options: customerOptions },
        hidable: false,
      },
    },
    {
      id: 'sdkValue',
      title: 'SDK',
      hiddenInColumn: true,
      filter: {
        type: 'select',
        props: { options: sdkOptions },
        hidable: false,
      },
    },
    {
      id: 'chipValue',
      title: '方案',
      hiddenInColumn: true,
      filter: {
        type: 'select',
        props: { options: chipOptions },
        hidable: false,
      },
    },

    {
      id: 'cSupportDevice',
      title: '地区',
      filter: {
        type: 'select',
        sort: Infinity,
        id: 'supportDevice',
        props: { options: REGION_OPTIONS },
        hidable: false,
      },
      cell: (props) => {
        const val = props.getValue<string>()
        return (
          <Tag color={REGION_COLORS[val]} bordered={false}>
            {REGION_MAP[val] || val}
          </Tag>
        )
      },
    },
    { id: 'cUpdateDesc', title: '应用介绍', cell: (props) => <Ellipsis text={props.getValue<string>()} maxLine={2} /> },
    {
      id: 'cState',
      title: '状态',
      size: 80,
      cell: (props) => {
        const val = props.getValue<string>()
        return (
          <Tag color={APP_STATE_COLOR[val]} bordered={false}>
            {APP_STATE[val] || '--'}
          </Tag>
        )
      },
    },
    {
      id: 'cTestState',
      title: '测试状态',
      size: 80,
      cell: (props) => {
        const val = props.getValue<string>()
        return (
          <Tag color={APP_TEST_STATE_COLOR[val]} bordered={false}>
            {APP_TEST_STATE[val] || '--'}
          </Tag>
        )
      },
    },
    { id: 'cRecommendValue', title: '推荐值', size: 80 },
    {
      id: 'cIsMilestone',
      title: '里程碑',
      size: 50,
      cell: (props) => {
        const val = props.getValue<boolean>()
        return (
          <Tag color={val ? 'success' : 'warning'} bordered={false}>
            {val ? '是' : '否'}
          </Tag>
        )
      },
    },

    { id: 'cCrtName', title: '创建人', size: 80, cell: (props) => props.getValue() || '--' },
    {
      id: 'cCrtTime',
      title: '创建时间',
      cell: (props) => (props.getValue() ? dayjs(props.getValue() as never).format('YYYY-MM-DD HH:mm:ss') : '--'),
    },
    { id: 'cUpdName', title: '更新人', size: 80, cell: (props) => props.getValue() || '--' },
    {
      id: 'cUpdTime',
      title: '更新时间',
      cell: (props) => (props.getValue() ? dayjs(props.getValue() as never).format('YYYY-MM-DD HH:mm:ss') : '--'),
    },
    {
      ...COLUMN_OPTIONS_COLUMN,
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button size="icon" variant="ghost" className="hover:text-primary-2 size-4 cursor-pointer p-0" title="更新状态">
                  <ArrowUpFromLine />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel className="p-1">应用状态</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuRadioGroup
                  value={`${row.original.cState || '0'}`}
                  onValueChange={(state) => handleUpdateState(row.original.cId, state)}
                >
                  {APP_STATE_OPTIONS.map((opt) => (
                    <DropdownMenuRadioItem key={opt.value} disabled={opt.value == row.original.cState} value={opt.value}>
                      {opt.label}
                    </DropdownMenuRadioItem>
                  ))}
                </DropdownMenuRadioGroup>
              </DropdownMenuContent>
            </DropdownMenu>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  size="icon"
                  variant="ghost"
                  className="hover:text-primary-2 size-4 cursor-pointer p-0"
                  title="更新测试状态"
                >
                  <FlaskConical />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel className="p-1">测试状态</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuRadioGroup
                  value={`${row.original.cTestState || '0'}`}
                  onValueChange={(state) => handleUpdateTestState(row.original.cId, state)}
                >
                  {APP_TEST_STATE_OPTIONS.map((opt) => (
                    <DropdownMenuRadioItem key={opt.value} disabled={opt.value == row.original.cTestState} value={opt.value}>
                      {opt.label}
                    </DropdownMenuRadioItem>
                  ))}
                </DropdownMenuRadioGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )
      },
    },
  ]

  return (
    <div className="absolute top-0 left-0 h-full w-full p-2">
      <BaseTable
        rowKey="cId"
        columns={columns}
        loading={swr.isValidating}
        data={swr.data?.records || []}
        filtering={{ enabled: true, value: data, onChange: setData }}
        rowSelection={{}}
        onRefresh={() => swr.mutate()}
        renderToolBar={(table) => {
          const selectedIds: string[] = table
            .getFilteredSelectedRowModel()
            .rows.map((row: Any) => `${row.original.cId || ''}`)
            .filter(Boolean)
          return (
            <div className="flex items-center gap-1">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button size="sm" disabled={!table.getFilteredSelectedRowModel().rows.length}>
                    更新状态
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[200px]">
                  <DropdownMenuLabel className="flex items-center">
                    批量更新状态
                    <ArrowUpFromLine className="ml-auto size-4" />
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuGroup>
                    {APP_STATE_OPTIONS.map((opt) => (
                      <DropdownMenuItem
                        key={opt.value}
                        onClick={() => {
                          modal.confirm({
                            title: '更新状态',
                            content: (
                              <div>
                                确定将选中数据状态更新为
                                <Tag className="!mx-1" color={APP_STATE_COLOR[opt.value]}>
                                  {APP_STATE[opt.value]}
                                </Tag>
                                吗？
                              </div>
                            ),
                            onOk: async () => {
                              const [err, resp] = await tryit(batchUpdateStateMutation.trigger)({
                                ids: selectedIds,
                                state: opt.value,
                              })
                              if (err || !resp?.data) {
                                toast.error(resp?.message ?? '操作失败')
                                return
                              }
                              toast.success(resp.message ?? '操作成功')
                              table.setRowSelection({})
                              swr.mutate()
                            },
                          })
                        }}
                      >
                        <div
                          className={cn('size-1.5 rounded-full', {
                            'bg-black/30': opt.value == '0',
                            'bg-success': opt.value == '4',
                            'bg-danger': opt.value == '5',
                            'bg-warning': opt.value == '6',
                          })}
                        />
                        {opt.label}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuGroup>
                </DropdownMenuContent>
              </DropdownMenu>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button size="sm" disabled={!table.getFilteredSelectedRowModel().rows.length}>
                    更新测试状态
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[200px]">
                  <DropdownMenuLabel className="flex items-center">
                    批量更新测试状态
                    <FlaskConical className="ml-auto size-4" />
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuGroup>
                    {APP_TEST_STATE_OPTIONS.map((opt) => (
                      <DropdownMenuItem
                        key={opt.value}
                        onClick={() => {
                          modal.confirm({
                            title: '更新状态',
                            content: (
                              <div>
                                确定将选中数据的测试状态更新为
                                <Tag className="!mx-1" color={APP_TEST_STATE_COLOR[opt.value]}>
                                  {APP_TEST_STATE[opt.value]}
                                </Tag>
                                吗？
                              </div>
                            ),
                            onOk: async () => {
                              const [err, resp] = await tryit(batchUpdateTestStateMutation.trigger)({
                                ids: selectedIds,
                                state: opt.value,
                              })
                              if (err || !resp?.data) {
                                toast.error(resp?.message ?? '操作失败')
                                return
                              }
                              toast.success(resp.message ?? '操作成功')
                              table.setRowSelection({})
                              swr.mutate()
                            },
                          })
                        }}
                      >
                        <div
                          className={cn('size-1.5 rounded-full', {
                            'bg-black/30': opt.value == '0',
                            'bg-success': opt.value == '1',
                            'bg-danger': opt.value == '2',
                          })}
                        />
                        {opt.label}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )
        }}
        pagination={{
          total: swr.data?.total || 0,
          current: pagination.current,
          pageSize: pagination.size,
          onChange: (pg) => {
            pagination.setCurrent(pg.current)
            pagination.setSize(pg.pageSize)
          },
        }}
      />
    </div>
  )
}
