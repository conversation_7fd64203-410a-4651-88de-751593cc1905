'use client'

import { Affix } from 'antd'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { useMemo } from 'react'

import { SidebarInset, SidebarProvider } from '@repo/design-system/components/ui/sidebar'
import { AdminSidebar, NavHeader } from '@repo/layouts/shadcn-admin-layout'

import TitanBlack from '@/assets/titan-black.png'
import { useAuthUser, useModuleContext } from '@/providers'
export interface ModuleClientLayoutProps {
  children: React.ReactNode
}

export default function PrimitiveLayout(props: ModuleClientLayoutProps) {
  const router = useRouter()
  const user = useAuthUser()
  const { menus, currentMenu, currentModule, modules } = useModuleContext()
  const route = useMemo(
    () => ({
      push: (path: string, opts?: { scroll?: boolean }) => router.push(path, opts),
      replace: (path: string, opts?: { scroll?: boolean }) => router.replace(path, opts),
      back: () => router.back(),
      forward: () => router.forward(),
    }),
    [router]
  )

  return (
    <SidebarProvider style={{ '--sidebar-width': '14rem' } as never}>
      <AdminSidebar
        current={currentMenu}
        menus={menus}
        modules={modules}
        module={currentModule}
        router={route}
        logo={<Image alt="titan" src={TitanBlack} width={64} height={22} />}
      />
      <SidebarInset className="overflow-hidden">
        <Affix>
          <NavHeader menus={menus} current={currentMenu} module={currentModule} router={route} user={user} />
        </Affix>
        <div className="relative flex min-h-64 w-full flex-1 flex-col bg-[#F5F6F8] p-2">{props.children}</div>
      </SidebarInset>
    </SidebarProvider>
  )
}
