'use client'

import { ModuleProvider } from '@/providers'
import { MenuState } from '@/stores/menus'
import { ModuleState } from '@/stores/module'
import { useParams, usePathname } from 'next/navigation'

export interface ModuleClientLayoutProps {
  children: React.ReactNode
  modules: ModuleState['modules']
  menus: MenuState['menus']
}

export function ModuleClientLayout(props: ModuleClientLayoutProps) {
  const { modules, menus } = props
  const pathname = usePathname()
  const { module } = useParams<{ module: string }>()

  const currentMenu = menus.find((menu) => menu.path === pathname) || null

  const currentModule = modules.find((md) => md.code === module) || null

  return (
    <ModuleProvider menus={menus} modules={modules} currentMenu={currentMenu} currentModule={currentModule}>
      {props.children}
    </ModuleProvider>
  )
}
