import { PropsWithChildren } from 'react'

import { fetchApp } from '@/fetchs'
import { MicroAppClientLayoutWrapper } from './microapp-client-layout-wrapper'

async function ModuleLayout(props: PropsWithChildren<{ params: Promise<{ module: string; microapp: string }> }>) {
  const { microapp } = await props.params
  const app = await fetchApp(microapp)

  return <MicroAppClientLayoutWrapper app={app}>{props.children}</MicroAppClientLayoutWrapper>
}

export default ModuleLayout
