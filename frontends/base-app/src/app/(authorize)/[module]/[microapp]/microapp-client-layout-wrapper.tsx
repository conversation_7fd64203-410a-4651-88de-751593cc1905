'use client'

import { MicroAppInfo } from '@repo/types'
import dynamic from 'next/dynamic'

export interface ModuleClientLayoutProps {
  app?: MicroAppInfo
  children: React.ReactNode
}

const MicroAppClientLayout = dynamic(() => import('./microapp-client-layout').then((mod) => mod.default), { ssr: false })

export function MicroAppClientLayoutWrapper(props: ModuleClientLayoutProps) {
  return <MicroAppClientLayout {...props} />
}
