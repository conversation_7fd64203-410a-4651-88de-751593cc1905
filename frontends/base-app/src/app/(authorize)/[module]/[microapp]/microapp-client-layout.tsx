'use client'

import microApp from '@micro-zoe/micro-app'
import { Affix } from 'antd'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useMemo } from 'react'

import { SidebarInset, SidebarProvider } from '@repo/design-system/components/ui/sidebar'
import { AdminSidebar, NavHeader } from '@repo/layouts/shadcn-admin-layout'
import { MicroAppNext } from '@repo/micro-app/microapp/micro-app-next'
import { MicroAppInfo } from '@repo/types'

import { useAuthUser, useModuleContext } from '@/providers'

export interface ModuleClientLayoutProps {
  app?: MicroAppInfo
  children: React.ReactNode
}

export default function MicroAppClientLayout(props: ModuleClientLayoutProps) {
  const { app } = props

  const { module, microapp } = useParams<{ module: string; microapp: string }>()

  const router = useRouter()

  const user = useAuthUser()

  const { menus, currentMenu, currentModule, modules } = useModuleContext()

  const route = useMemo(
    () => ({
      push: (path: string, opts?: { scroll?: boolean }) => {
        if (microApp.hasInit && path.startsWith(`/${module}/${microapp}`)) {
          microApp.router.push({ name: microapp, path })
        }
        router.push(path, opts)
      },
      replace: (path: string, opts?: { scroll?: boolean }) => {
        if (microApp.hasInit && path.startsWith(`/${module}/${microapp}`)) {
          microApp.router.push({ name: microapp, path })
        }
        router.replace(path, opts)
      },
      back: () => router.back(),
      forward: () => router.forward(),
    }),
    [microapp, module, router]
  )

  return (
    <SidebarProvider style={{ '--sidebar-width': '14rem' } as never}>
      <AdminSidebar current={currentMenu} menus={menus} modules={modules} module={currentModule} router={route} />
      <SidebarInset className="overflow-hidden">
        <Affix>
          <NavHeader menus={menus} current={currentMenu} module={currentModule} router={route} user={user} />
        </Affix>
        <div className="relative flex min-h-64 w-full flex-1 flex-col bg-[#F5F6F8] p-2">
          {app ? (
            <MicroAppNext
              menu={currentMenu as never}
              app={{ ...app, config: { ...app.config, baseroute: `/${module}/${microapp}` } }}
            />
          ) : (
            props.children
          )}
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
