import { Watermark } from 'antd'
import { type JwtPayload, jwtDecode } from 'jwt-decode'
import { cookies, headers } from 'next/headers'
import { redirect } from 'next/navigation'
import qs from 'query-string'
import { PropsWithChildren } from 'react'

import { AuthProvider, AuthUser } from '@/providers'

async function AuthorizedLayout(props: PropsWithChildren) {
  const cookieList = await cookies()

  const jwt = cookieList.get('cvte.jwt')

  if (!jwt?.value) {
    const headerList = await headers()
    const search = headerList.get('x-client-search')
    const pathname = headerList.get('x-client-pathname')
    redirect(qs.stringifyUrl({ url: `/api/auth${search || ''}`, query: { redirectTo: pathname } }))
  }

  const user = getAuthUser(jwt.value)

  return (
    <AuthProvider user={user} jwt={jwt.value}>
      <Watermark content={user?.name || 'DevOps Next'} zIndex={11} gap={[256, 256]}>
        {props.children}
      </Watermark>
    </AuthProvider>
  )
}

export default AuthorizedLayout

function getAuthUser(jwt: string) {
  // TODO: 正常情况下应该去请求远端获取用户信息，临时先用 jwt 直接解析获取
  const jwtResp = jwtDecode<JwtPayload & { map?: AuthUser }>(jwt)

  if (!jwtResp.map) {
    throw new Error('jwt decode error')
  }

  return jwtResp.map
}
