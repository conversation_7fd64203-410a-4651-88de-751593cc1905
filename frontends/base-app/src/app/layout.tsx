import type { Metada<PERSON> } from 'next'
import { <PERSON>ei<PERSON>, <PERSON>eist_Mono } from 'next/font/google'

import { NextAntdProvider } from '@repo/fe-helper/providers/next-antd-provider'
import { NextDesignThemeProvider } from '@repo/fe-helper/providers/next-design-theme-provider'
import { ProgressBarProvider } from '@repo/fe-helper/providers/progress-bar-provider'
import { SWRProvider } from '@repo/fe-helper/providers/swr-provider'

import './globals.css'

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
})

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
})

export const metadata: Metadata = {
  title: 'Create Next App',
  description: 'Generated by create next app',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        <ProgressBarProvider>
          <SWRProvider>
            <NextDesignThemeProvider>
              <NextAntdProvider>{children}</NextAntdProvider>
            </NextDesignThemeProvider>
          </SWRProvider>
        </ProgressBarProvider>
      </body>
    </html>
  )
}
