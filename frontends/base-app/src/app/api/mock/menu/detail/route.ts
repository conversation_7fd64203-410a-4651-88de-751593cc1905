import { NextRequest, NextResponse } from 'next/server'

import { menus } from '@repo/mock/menus'

export function GET(request: NextRequest) {
  const { searchParams } = request.nextUrl

  const path = searchParams.get('path')
  const md = searchParams.get('module')

  if (!path || !md) {
    return NextResponse.json({
      code: 1,
      message: '菜单不存在',
      data: null,
    })
  }

  const menu = menus.find((it) => `/${it.module}${it.path.replace(/\//g, '/')}` === path && it.module === md)

  if (!menu) {
    return NextResponse.json({
      code: 1,
      message: '菜单不存在',
      data: null,
    })
  }

  return NextResponse.json({
    code: 0,
    message: 'success',
    data: menu,
  })
}
