import { NextRequest, NextResponse } from 'next/server'

import { menus } from '@repo/mock/menus'

export function GET(request: NextRequest) {
  const { searchParams } = request.nextUrl

  const md = searchParams.get('module')

  if (searchParams.has('module') && !md) {
    return NextResponse.json({
      code: 1,
      message: '模块不存在',
      data: [],
    })
  }

  if (!md) {
    return NextResponse.json({
      code: 0,
      message: 'success',
      data: menus,
    })
  }

  const ret = menus.filter((it) => it.module === md)

  return NextResponse.json({
    code: 0,
    message: 'success',
    data: ret,
  })
}
