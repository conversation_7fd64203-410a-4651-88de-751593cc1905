import { modules } from '@repo/mock/module'
import { NextRequest, NextResponse } from 'next/server'

export function GET(req: NextRequest) {
  const { searchParams } = req.nextUrl
  const code = searchParams.get('code')

  const md = modules.find((it) => it.code === code)

  if (!md) {
    return NextResponse.json({
      code: 1,
      message: '未找到模块',
      data: null,
    })
  }

  return NextResponse.json({
    code: 0,
    message: 'success',
    data: md,
  })
}
