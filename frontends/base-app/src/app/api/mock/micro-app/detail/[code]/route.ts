import { NextRequest, NextResponse } from 'next/server'

import { micro_apps } from '@repo/mock/micro-app'

export async function GET(req: NextRequest, { params }: { params: Promise<{ code: string }> }) {
  const { code } = await params

  const app = micro_apps.find((it) => it.code === code)

  if (!app) {
    return NextResponse.json({
      code: 1,
      message: '未找到应用',
      data: null,
    })
  }

  return NextResponse.json({
    code: 0,
    message: 'success',
    data: app,
  })
}
