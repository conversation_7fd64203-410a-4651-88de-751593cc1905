import { createNextApiServer } from '@repo/hono-api'

import env from '@/env'

function getApi(namespace: string, path: string): string {
  const API_NAME_SPACES: Record<string, string> = {
    'app-store': env.API_STARK_APP_STORE,
  }

  const domain = API_NAME_SPACES[namespace]?.replace(/\/$/, '')

  if (!domain) {
    return ''
  }

  return `${domain}/${path.replace(/^\//, '')}`
}

export const { GET, POST, PUT, DELETE, dynamic } = createNextApiServer({ getApi })
