'use client'

import { DevOpsNext, TravelConnectSignIn } from '@repo/fe-helper/components'

function Page() {
  return (
    <TravelConnectSignIn
      logo={<DevOpsNext width={128} height={44} className="text-blue-500" />}
      title="DevOps Next"
      description="Sign in to access your global system dashboard"
      providers={[
        {
          providerId: 'cvte-oauth2',
          name: 'CVTE',
          title: 'Login with CVTE',
          logo: <span className="h-5 w-5 rounded-4xl bg-amber-600 text-center leading-5 text-white">C</span>,
          onClick: () => console.log('CVTE sign-in'),
        },
      ]}
      onLogin={({ account, password }) => console.log('Sign in attempt with:', { account, password })}
    />
  )
}

export default Page
