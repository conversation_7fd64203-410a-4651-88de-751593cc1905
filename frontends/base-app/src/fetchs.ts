import { request } from '@repo/fe-helper/request'
import env from './env'

function isServer() {
  return typeof window === 'undefined'
}

function getHost() {
  return isServer() ? env.DEPLOY_URL || 'http://localhost:3000' : ''
}

export async function fetchApps() {
  const res = await request(`${getHost()}/api/mock/micro-app/list`)
  const ret = res.data
  return ret.data
}

export async function fetchApp(code: string) {
  const res = await request(`${getHost()}/api/mock/micro-app/detail/${code}`)
  const ret = res.data
  return ret.data
}

export async function fetchUser() {
  const res = await request(`${getHost()}/api/mock/user/profile`)
  const ret = res.data
  return ret.data
}

export async function fetchMenus(module?: string) {
  const res = await request(`${getHost()}/api/mock/menu/list?${module ? `module=${module}` : ''}`)
  const ret = res.data
  return ret.data
}

export async function fetchMenuDetail(path: string, module: string) {
  const res = await request(`${getHost()}/api/mock/menu/detail?path=${path}&module=${module}`)
  const ret = res.data
  return ret.data
}

export async function fetchModules() {
  const res = await request(`${getHost()}/api/mock/module/list`)
  const ret = res.data
  return ret.data
}

export async function fetchModule(code: string) {
  const res = await request(`${getHost()}/api/mock/module/detail?code=${code}`)
  const ret = res.data
  return ret.data
}

export async function fetchSystems() {
  const res = await request(`${getHost()}/api/mock/system/list`)
  const ret = res.data
  return ret.data
}
