{"name": "@fe/microapp-demo-vite", "version": "0.0.0", "private": true, "type": "module", "scripts": {"build": "tsc -b && vite build", "clean": "rimraf .turbo dist node_modules", "dev": "vite", "lint": "oxlint . && eslint .", "lint:fix": "prettier --check --write . && oxlint --fix --fix-suggestions . && eslint . --fix", "preview": "vite preview"}, "lint-staged": {"*": ["prettier --check --write", "oxlint --fix --fix-suggestions", "eslint --fix"]}, "dependencies": {"ogl": "^1.0.11", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.5.3"}, "devDependencies": {"@eslint/js": "^9.25.0", "@repo/design-system": "workspace:*", "@repo/micro-app": "workspace:*", "@tailwindcss/vite": "^4.1.5", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react-swc": "^3.9.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "tailwindcss": "^4.1.5", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}