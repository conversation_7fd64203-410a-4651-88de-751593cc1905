import { createBrowserRouter, Navigate } from 'react-router'

import About from './pages/about'
import Home from './pages/home'

export const routers = createBrowserRouter(
  [
    { path: '/', element: <Navigate replace to="/home" /> },
    {
      path: '/home',
      element: <Home />,
    },
    {
      path: '/about',
      element: <About />,
    },
  ],
  { basename: window.__MICRO_APP_BASE_ROUTE__ }
)
