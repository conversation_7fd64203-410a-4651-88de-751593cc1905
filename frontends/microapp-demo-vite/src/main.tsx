import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'

import { RouterProvider } from 'react-router'
import { routers } from './route'

import './global.css'

const root = createRoot(document.getElementById('root')!)

if (window.__MICRO_APP_ENVIRONMENT__) {
  window.onmount = () => {
    root.render(
      <StrictMode>
        <RouterProvider router={routers} />
      </StrictMode>
    )
  }
  window.onunmount = () => {
    root.unmount()
  }
} else {
  root.render(
    <StrictMode>
      <RouterProvider router={routers} />
    </StrictMode>
  )
}
