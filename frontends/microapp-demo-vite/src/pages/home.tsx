import { Button } from '@repo/design-system/components/ui/button'
import { Link } from 'react-router'

import Aurora from '../components/aurora'

import './home.css'

function Home() {
  console.log('entry home')
  return (
    <div className="home relative h-screen w-screen bg-black">
      <div className="absolute top-1/2 left-1/2 inline-flex -translate-x-1/2 -translate-y-1/2 flex-col text-center">
        <p className="text-7xl whitespace-nowrap text-white">welcome to home page</p>
        <div className="mt-4 flex items-center justify-center gap-2 text-center">
          <Link className="text-2xl !text-blue-700 hover:!text-blue-300" to="/about">
            go to about
          </Link>
          <span className="text-red-700!">tes</span>
          <Button
            className="cursor-pointer"
            onClick={() => {
              // 获取主应用路由
              //@ts-expect-error - type error
              const baseRouter = window.microApp?.router.getBaseAppRouter()
              if (baseRouter) {
                baseRouter.push('/micro/microapp-demo-umi')
              } else {
                window.location.href = '/micro/microapp-demo-umi'
              }
            }}
          >
            去 umi 应用
          </Button>
          <Button
            className="cursor-pointer"
            onClick={() => {
              // 获取主应用路由
              //@ts-expect-error - type error
              const baseRouter = window.microApp?.router.getBaseAppRouter()
              if (baseRouter) {
                baseRouter.push('/micro/microapp-demo-umi/table')
              } else {
                window.location.href = '/micro/microapp-demo-umi/table'
              }
            }}
          >
            去 umi 应用 table 页面
          </Button>
        </div>
      </div>
      <Aurora colorStops={['#3A29FF', '#FF94B4', '#FF3232']} blend={0.2} amplitude={1.0} speed={0.5} />
    </div>
  )
}

export default Home
