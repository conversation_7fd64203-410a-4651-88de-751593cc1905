import { Link } from 'react-router'
import Balatro from '../components/balatro'

function About() {
  console.log('entry about')
  return (
    <div className="relative h-screen w-screen bg-black">
      <div className="absolute top-1/2 left-1/2 inline-flex -translate-x-1/2 -translate-y-1/2 flex-col text-center">
        <p className="text-7xl whitespace-nowrap text-white">welcome to about page</p>
        <div className="text-center">
          <Link className="mt-4 text-2xl text-white hover:text-blue-300" to="/home">
            go to home
          </Link>
        </div>
      </div>
      <Balatro isRotate={false} mouseInteraction={true} pixelFilter={700} />

      <img
        src="https://oyster.ignimgs.com/mediawiki/apis.ign.com/balatro/e/ef/Joker.png"
        className="pointer-events-none absolute top-1/6 left-1/2 h-auto w-[200px] -translate-x-1/2 -translate-y-1/6 rounded-md"
      ></img>
    </div>
  )
}

export default About
