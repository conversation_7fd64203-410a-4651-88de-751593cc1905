# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
node_modules
.pnp
.pnp.js

# Local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
.nyc_output
blob-report/
coverage
test-results/
playwright/.cache/
playwright-report/

# Turbo
.turbo

# Vercel
.vercel

# Build Outputs
.next/
out/
build
dist
.deploy

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tailwind.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Debug
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Misc
.DS_Store
*.pem
