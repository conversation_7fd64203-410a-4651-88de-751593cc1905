{"name": "@repo/micro-app", "version": "0.0.0", "private": true, "license": "ISC", "author": "vic", "exports": {"./iframe/*": "./src/iframe/*.tsx", "./microapp/*": "./src/microapp/*.tsx", "./microapp/types": "./src/microapp/types.ts", "./micro-app.d.ts": "./src/microapp/micro-app.d.ts", "./tailwindcss.css": "./src/tailwindcss.css"}, "scripts": {"clean": "rimraf .turbo dist node_modules", "lint": "oxlint . && eslint .", "lint:fix": "prettier --check --write . && oxlint --fix --fix-suggestions . && eslint . --fix"}, "lint-staged": {"*": ["prettier --check --write", "oxlint --fix --fix-suggestions", "eslint --fix"]}, "devDependencies": {"@micro-zoe/micro-app": "1.0.0-rc.26", "@repo/design-system": "workspace:*", "@repo/eslint-config": "workspace:*", "@repo/types": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^20.11.24", "@types/react": "^19", "@types/react-dom": "^19", "antd": "^5.25.4", "next": "15.3.3", "react": "^19.1.0", "typescript": "5.5.4"}, "peerDependencies": {"@micro-zoe/micro-app": "1.0.0-rc.26", "@repo/design-system": "workspace:*", "antd": "^5.25.4", "next": "15.3.3", "react": "^19.1.0"}}