/// <reference types="@types/react" />

/* eslint-disable @typescript-eslint/ban-types */

declare namespace React {
  namespace JSX {
    interface IntrinsicElements {
      'micro-app': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>
    }
  }
}

declare interface Window {
  // ============================ micro-app 环境变量/钩子 ============================
  // 是否 micro-app 应用环境中
  __MICRO_APP_ENVIRONMENT__?: boolean
  // micro-app 应用环境中配置的应用名称
  __MICRO_APP_NAME__?: string
  // micro-app 应用环境中的子应用静态资源前缀
  __MICRO_APP_PUBLIC_PATH__?: string
  // micro-app 应用环境中的子应用基准路由
  __MICRO_APP_BASE_ROUTE__?: string
  // micro-app 应用环境中是否是主应用
  __MICRO_APP_BASE_APPLICATION__?: boolean

  onmount?: () => void
  onunmount?: () => void

  // https://micro-zoe.github.io/micro-app/docs.html#/zh-cn/api
  microApp?: {
    pureCreateElement: <K extends keyof HTMLElementTagNameMap>(tag: K) => HTMLElementTagNameMap[K]
    removeDomScope: (force?: boolean) => void
    rawWindow: typeof window
    rawDocument: typeof document
    getData: () => Record<string, unknown>
    addDataListener: (dataListener: Function, autoTrigger?: boolean) => void
    removeDataListener: (dataListener: Function) => void
    clearDataListener: () => void
    dispatch: (obj: Record<string, unknown>) => void
    getGlobalData: () => Record<string, unknown>
    addGlobalDataListener: (dataListener: Function, autoTrigger?: boolean) => void
    removeGlobalDataListener: (dataListener: Function) => void
    clearGlobalDataListener: () => void
    setGlobalData: (data: Record<string, unknown>) => void
  }

  // if use webpack builder
  publicPath?: string
}
