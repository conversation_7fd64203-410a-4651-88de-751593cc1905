/** @jsxRuntime classic */
/** @jsx jsxCustomEvent */
// organize-imports-ignore
import jsxCustomEvent from '@micro-zoe/micro-app/polyfill/jsx-custom-event'

import microApp from '@micro-zoe/micro-app'
import { Button, Result, Spin } from 'antd'
import { useEffect, useState } from 'react'

import { MicroAppProps } from './types'

import { ErrorMap } from './error'

export default function MicroApp(props: MicroAppProps) {
  const { menu, app, router } = props
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)

  const onCreated = () => {
    console.log('micro-app元素被创建')
    setLoading(true)
  }

  const onBeforemount = () => {
    console.log('即将被渲染，只在初始化时执行一次')
    setLoading(true)
  }

  const onMounted = () => {
    console.log('已经渲染完成，只在初始化时执行一次')
    setLoading(false)
  }

  const onAfterhidden = () => {
    console.log('已推入后台')
  }

  const onAftershow = () => {
    console.log('已经推入前台，初始化时不执行')
    setLoading(false)
  }

  const onBeforeshow = () => {
    console.log('即将推入前台，初始化时不执行')
    setLoading(true)
  }

  const onError = (e: CustomEvent) => {
    const error = e.detail.error
    setError(ErrorMap[error.message] || error.message)
    setLoading(false)
    console.error(e)
  }

  useEffect(() => {
    if (microApp.hasInit) return
    setLoading(true)
    microApp.start({
      fetch: async (url, options) => {
        const _url = new URL(url)
        // native 路由下，如果 url 和 window.location.pathname 相同，应当属于请求应用资源
        if (_url.pathname === window.location.pathname) {
          const res = await fetch(app.url, options)
          return res.text()
        }
        const res = await fetch(url, options)
        return res.text()
      },
    })
    microApp.router.setBaseAppRouter(router)

    return () => {
      microApp.unmountApp(app.code)
    }
  }, [])

  return (
    // @ts-expect-error - type error
    <div className="relative h-full min-h-96 w-full">
      {loading && (
        <Spin
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            zIndex: 1000,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            width: '100%',
            height: '100%',
          }}
        />
      )}

      {error ? (
        <Result
          status="error"
          title={error}
          subTitle="请联系管理员或点击重试"
          extra={<Button onClick={() => setError(null)}>重试</Button>}
        />
      ) : (
        // @ts-expect-error - type error
        <micro-app
          name={app.code}
          url={app.url}
          data={{ menu, app }}
          baseroute={`/${app.code.replace(/^\//, '')}`}
          // ssr={app.isSSR}
          {...app.config}
          router-mode="native-scope"
          onCreated={onCreated}
          onBeforemount={onBeforemount}
          onMounted={onMounted}
          onAfterhidden={onAfterhidden}
          onAftershow={onAftershow}
          onBeforeshow={onBeforeshow}
          onError={onError}
        />
      )}
      {/* @ts-expect-error - type error */}
    </div>
  )
}
