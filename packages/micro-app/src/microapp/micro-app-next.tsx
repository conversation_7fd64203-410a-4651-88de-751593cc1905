'use client'

import dynamic from 'next/dynamic'
import { useRouter } from 'next/navigation'

import { MicroAppProps } from './types'

const MicroApp = dynamic(() => import('./micro-app'), { ssr: false })

export function MicroAppNext(props: Omit<MicroAppProps, 'router'>) {
  const router = useRouter()

  const route = {
    push: (path: string, opts?: { scroll?: boolean }) => router.push(path, opts),
    replace: (path: string, opts?: { scroll?: boolean }) => router.replace(path, opts),
    back: () => router.back(),
    forward: () => router.forward(),
  }
  return <MicroApp {...props} router={route} />
}
