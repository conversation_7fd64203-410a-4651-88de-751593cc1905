import { MicroAppInfo } from '@repo/types'
import { ReactElement } from 'react'

export interface MicroAppProps {
  app: MicroAppInfo
  menu: MicroAppInMenu | null
  router: {
    push: (path: string, opts?: { scroll?: boolean }) => void
    replace: (path: string, opts?: { scroll?: boolean }) => void
    back: () => void
    forward: () => void
  }
}

export interface MicroAppInMenu {
  id: string | number
  parentId: string
  code: string
  // module code
  module: string
  path: string
  title: string
  name: string
  sort: number
  target: number

  // microapp code
  microapp?: string
  icon?: ReactElement | string
  group?: string
  defaultOpen?: boolean
  children?: Omit<MicroAppInMenu, 'group' | 'defaultOpen'>[]
  query?: string

  hadSide?: boolean
  hadHeader?: boolean
  hadNav?: boolean
  hiddenInMenu: number
  enabled?: boolean
}
