import { createEnv } from '@t3-oss/env-core'
import { z } from 'zod'
import { CvteEnv } from './preset.type'

export const cvte = (): Readonly<CvteEnv> => {
  return createEnv({
    server: {
      RUNNING_ENV: z.enum(['dev', 'fat', 'prod', 'builder']),
      NODE_ENV: z.enum(['development', 'production', 'test']),
      CI: z.string().optional(),
      PORTAL_APP_ID: z.string().optional(),
      PORTAL_AUTH_URL: z.string(),
      PORTAL_LOGOUT_URL: z.string(),
      VALIDATE_TICKET_URL: z.string(),
      DEPLOY_URL: z.string().optional(),
      ASSETS_BASE_PATH: z.string().optional(),
    },
    runtimeEnv: process.env,
  })
}
