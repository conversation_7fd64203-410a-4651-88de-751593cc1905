{"name": "@repo/env", "version": "0.0.0", "private": true, "license": "ISC", "author": "vic", "exports": {".": "./src/index.ts"}, "scripts": {"clean": "rimraf .turbo dist node_modules", "lint": "oxlint . && eslint .", "lint:fix": "prettier --check --write . && oxlint --fix --fix-suggestions . && eslint . --fix"}, "lint-staged": {"*": ["prettier --check --write", "oxlint --fix --fix-suggestions", "eslint --fix"]}, "dependencies": {"@t3-oss/env-core": "^0.13.6", "@t3-oss/env-nextjs": "^0.13.6", "zod": "^3.25.51"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^20.11.24", "typescript": "5.5.4"}}