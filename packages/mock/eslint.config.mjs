import { FlatCompat } from '@eslint/eslintrc'
import { defineConfig } from 'eslint/config'

import internalEslint from '@repo/eslint-config/react-internal.mjs'

const compat = new FlatCompat({
  baseDirectory: import.meta.dirname,
})

export default defineConfig([
  internalEslint,
  ...compat.config({
    root: true,
    parser: '@typescript-eslint/parser',
    parserOptions: { project: './tsconfig.lint.json', tsconfigRootDir: import.meta.dirname },
  }),
  { ignores: ['postcss.config.mjs'] },
])
