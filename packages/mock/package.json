{"name": "@repo/mock", "version": "0.0.0", "private": true, "license": "ISC", "author": "vic", "exports": {".": "./src/index.ts", "./*": "./src/*.ts"}, "scripts": {"clean": "rimraf .turbo dist node_modules", "lint": "oxlint . && eslint .", "lint:fix": "prettier --check --write . && oxlint --fix --fix-suggestions . && eslint . --fix"}, "lint-staged": {"*": ["prettier --check --write", "oxlint --fix --fix-suggestions", "eslint --fix"]}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^20.11.24", "typescript": "5.5.4"}}