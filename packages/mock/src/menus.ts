import { umiMicroApp, viteMicroApp } from './micro-app'

export const menus = [
  { id: 1, module: 'system', microapp: umiMicroApp.code, path: '/home', title: '首页', hadSide: true, hadHeader: true },
  { id: 2, module: 'system', microapp: umiMicroApp.code, path: '/access', title: '权限演示', hadSide: true, hadHeader: true },
  { id: 3, module: 'system', microapp: viteMicroApp.code, path: '/home', title: 'ViteHome', hadSide: true, hadHeader: true },
  { id: 4, module: 'system', microapp: viteMicroApp.code, path: '/about', title: 'ViteAbout', hadSide: true, hadHeader: true },

  { id: 5, module: 'app-store', path: '/app-manager', title: '应用管理', hadSide: true, hadHeader: true },
  { id: 6, module: 'app-store', path: '/app-up', title: '应用上架', hadSide: true, hadHeader: true },
]
