export const viteMicroApp = {
  name: 'microapp-demo-vite',
  code: 'microapp-demo-vite',
  url: 'http://localhost:3002/',
  description: 'microapp-demo-vite',
  config: {
    iframe: true,
  },
  isSSR: 0,
  authType: 'ticket',
  loader: 1,
  sort: 0,
  version: '1.0.0',
  enabled: true,
}

export const umiMicroApp = {
  name: 'microapp-demo-umi',
  code: 'microapp-demo-umi',
  url: 'http://localhost:8000/',
  description: 'microapp-demo-umi',
  config: {},
  isSSR: 0,
  authType: 'ticket',
  loader: 1,
  sort: 0,
  version: '1.0.0',
  enabled: true,
}

// TODO：对于 SSR 项目好像现有的微前端方案都存在问题，SSR 子应用强制使用 iframe ?
export const nextjsMicroApp = {
  name: 'microapp-demo-nextjs',
  code: 'microapp-demo-nextjs',
  url: 'http://localhost:3003/',
  description: 'microapp-demo-nextjs',
  config: {},
  isSSR: 1,
  authType: 'ticket',
  loader: 1,
  sort: 0,
  version: '1.0.0',
  enabled: true,
}

export const micro_apps = [viteMicroApp, umiMicroApp, nextjsMicroApp]
