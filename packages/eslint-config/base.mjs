import { FlatCompat } from '@eslint/eslintrc'
import js from '@eslint/js'
import oxlint from 'eslint-plugin-oxlint'
import { defineConfig } from 'eslint/config'
import ts from 'typescript-eslint'

const compat = new FlatCompat({
  baseDirectory: import.meta.dirname,
  recommendedConfig: ts.configs.recommended,
})

export default defineConfig([
  { extends: [js.configs.recommended] },
  ...compat.config({
    root: true,
    env: { node: true },
    extends: ['plugin:@typescript-eslint/recommended', 'plugin:prettier/recommended', 'prettier'],
    plugins: ['@typescript-eslint/eslint-plugin', 'prettier', 'turbo', 'oxlint'],
    parser: '@typescript-eslint/parser',
    rules: { 'prettier/prettier': 'warn' },
  }),
  {
    ignores: [
      '.*.js',
      '*.setup.js',
      '*.config.js',
      'eslint.config.mjs',
      '.turbo/',
      'dist/',
      'coverage/',
      'node_modules/',
      '.husky/',
    ],
  },
  ...oxlint.configs['flat/recommended'],
])
