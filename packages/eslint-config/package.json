{"name": "@repo/eslint-config", "version": "0.0.0", "private": true, "files": ["base.mjs", "library.mjs", "next.mjs", "react-internal.mjs", "package.json"], "scripts": {"clean": "rimraf node_modules .turbo"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.25.1", "@hono/eslint-config": "^2.0.0", "@next/eslint-plugin-next": "^15.3.1", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "eslint": "^9.25.1", "eslint-config-next": "15.3.1", "eslint-config-prettier": "^9.1.0", "eslint-config-turbo": "^2.0.0", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-oxlint": "0.16.9", "eslint-plugin-prettier": "^5.2.6", "globals": "^16.0.0", "oxlint": "0.16.9", "prettier": "^3.5.3", "typescript": "5.5.4", "typescript-eslint": "^8.31.0"}}