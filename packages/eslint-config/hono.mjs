import { FlatCompat } from '@eslint/eslintrc'
import oxlint from 'eslint-plugin-oxlint'
import { defineConfig } from 'eslint/config'
import path from 'node:path'

import baseEslint from './base.mjs'

const project = path.resolve(process.cwd(), 'tsconfig.json')

const compat = new FlatCompat({
  baseDirectory: import.meta.dirname,
})

export default defineConfig([
  ...baseEslint,
  ...compat.extends('prettier'),
  ...compat.config({
    env: { node: true },
    settings: { 'import/resolver': { typescript: { project } } },
    overrides: [{ files: ['*.js?(x)', '*.ts?(x)'] }],
  }),
  ...oxlint.configs['flat/recommended'],
])
