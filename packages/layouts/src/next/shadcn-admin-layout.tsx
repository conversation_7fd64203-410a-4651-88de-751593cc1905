'use client'

import { useRouter } from 'next/navigation'
import { useMemo } from 'react'

import { AdminLayoutContainer, AdminLayoutContainerProps } from '../shadcn-admin-layout'

export interface ShadcnAdminLayoutProps extends Omit<AdminLayoutContainerProps, 'router'> {
  routerPrefix?: string
}

function withPath(path: string, prefix: string) {
  path = path.replace(/^\//, '')
  prefix = prefix.replace(/^\//, '')
  if (!prefix) return `/${path}`
  return `/${prefix}/${path}`
}

export function ShadcnAdminLayout(props: ShadcnAdminLayoutProps) {
  const router = useRouter()

  const routerPrefix = props.routerPrefix || ''

  const route = useMemo(
    () => ({
      push: (path: string, opts?: { scroll?: boolean; prefix?: string }) => {
        const { prefix = routerPrefix, ...rest } = opts || {}

        console.log(withPath(path, prefix), 'withPath(path, prefix)')
        router.push(withPath(path, prefix), rest)
      },
      replace: (path: string, opts?: { scroll?: boolean; prefix?: string }) => {
        const { prefix = routerPrefix, ...rest } = opts || {}
        router.replace(withPath(path, prefix), rest)
      },
      back: () => router.back(),
      forward: () => router.forward(),
    }),
    [router, routerPrefix]
  )

  return <AdminLayoutContainer {...props} router={route} />
}
