'use client'

import { SidebarInset, SidebarProvider } from '@repo/design-system/components/ui/sidebar'
import { BaseAppRouter, BaseUserInfo, MenuInfo, ModuleInfo } from '@repo/types'
import { Affix, Watermark } from 'antd'
import type { ReactNode } from 'react'

import { NavHeader } from './nav-header'
import { AdminSidebar } from './sidebar'

export function AdminLayoutContainer(props: AdminLayoutContainerProps) {
  const { systems, currentMenu, menus, currentSystem, user, router } = props

  return (
    <Watermark className="ant-watermark-wrapper" content={user?.name} zIndex={11}>
      <SidebarProvider style={{ '--sidebar-width': '14rem' } as never}>
        <AdminSidebar
          current={currentMenu}
          menus={menus}
          modules={systems}
          module={currentSystem}
          router={router}
          logo={props.logo}
        />
        <SidebarInset className="overflow-hidden">
          <Affix>
            <NavHeader menus={menus} current={currentMenu} module={currentSystem} router={router} />
          </Affix>
          <div className="bg-primary-foreground relative flex min-h-64 w-full flex-1 flex-col p-2">{props.children}</div>
        </SidebarInset>
      </SidebarProvider>
    </Watermark>
  )
}

export interface AdminLayoutContainerProps {
  children: ReactNode
  systems: ModuleInfo[]
  currentSystem: ModuleInfo
  menus: MenuInfo[]
  currentMenu: MenuInfo
  user: BaseUserInfo
  router: BaseAppRouter
  logo?: ReactNode
}
