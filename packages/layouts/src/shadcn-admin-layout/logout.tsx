'use client'

import type { JSX } from 'react'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@repo/design-system/components/ui/alert-dialog'

import { BaseAppRouter, MenuInfo } from '@repo/types'

export function Logout(props: LogoutProps) {
  const { router, current } = props

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>{props.children}</AlertDialogTrigger>
      <AlertDialogContent className="top-1/4">
        <AlertDialogHeader>
          <AlertDialogTitle>确认退出登录?</AlertDialogTitle>
          <AlertDialogDescription>
            <span className="text-red-500">“继续”将会退出当前系统</span>
            <br />
            <span>“取消”将停留在该系统</span>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>取消</AlertDialogCancel>
          <AlertDialogAction
            onClick={() => {
              router.push(`/api/logout?redirectTo=${current?.path || '/'}`)
            }}
          >
            继续
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

export interface LogoutProps {
  children: JSX.Element
  router: BaseAppRouter
  current?: MenuInfo | null
}
