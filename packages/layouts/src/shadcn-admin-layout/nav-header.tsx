'use client'

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rumbEllipsis,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@repo/design-system/components/ui/breadcrumb'
import { Separator } from '@repo/design-system/components/ui/separator'
import { SidebarTrigger } from '@repo/design-system/components/ui/sidebar'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@repo/design-system/components/ui/dropdown-menu'
import { BaseAppRouter, BaseUserInfo, MenuInfo, ModuleInfo } from '@repo/types'
import { Fragment } from 'react'

import { NavActions } from './nav-actions'
import { findMenuPaths } from './utils'

export interface NavHeaderProps {
  menus: MenuInfo[]
  current?: MenuInfo | null
  module?: ModuleInfo | null
  router: BaseAppRouter
  user?: BaseUserInfo | null
}

export function NavHeader(props: NavHeaderProps) {
  const { menus, current, module, router, user } = props

  const hadHeader = current?.hadHeader ?? true

  const hadSide = current?.hadSide ?? true

  if (!hadHeader) {
    return null
  }

  const allBreadcrumbs = findMenuPaths(menus, current?.path || '')

  const ellipsisBreadcrumbs: MenuInfo[] = []

  const breadcrumbs: MenuInfo[] = []

  if (allBreadcrumbs.length > 2) {
    ellipsisBreadcrumbs.push(...allBreadcrumbs.slice(0, -1))
    breadcrumbs.push(allBreadcrumbs.at(-1) as MenuInfo)
  } else {
    breadcrumbs.push(...allBreadcrumbs)
  }

  return (
    <header className="bg-background sticky top-0 z-10 flex h-10 shrink-0 items-center gap-2 shadow-sm transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
      <div className="flex items-center gap-1 px-4">
        {hadSide && (
          <Fragment>
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="!mr-2 !h-4" />
          </Fragment>
        )}
        <Breadcrumb>
          <BreadcrumbList className="flex-nowrap">
            <BreadcrumbItem>
              <BreadcrumbLink onClick={() => router.push(module?.url || '/')}>{module ? module.name : '--'}</BreadcrumbLink>
            </BreadcrumbItem>
            {ellipsisBreadcrumbs.length > 0 && (
              <Fragment>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <DropdownMenu>
                    <BreadcrumbLink asChild>
                      <DropdownMenuTrigger className="flex items-center gap-1">
                        <BreadcrumbEllipsis className="h-4 w-4" />
                        <span className="sr-only">Toggle menu</span>
                      </DropdownMenuTrigger>
                    </BreadcrumbLink>

                    <DropdownMenuContent align="start">
                      {ellipsisBreadcrumbs.map((ebc) => (
                        <DropdownMenuItem
                          key={ebc.id}
                          className="cursor-pointer focus:text-blue-500"
                          onClick={() => router.push(ebc.path)}
                        >
                          {ebc.title}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </BreadcrumbItem>
              </Fragment>
            )}
            {breadcrumbs.map((bc, idx) => (
              <Fragment key={bc.id}>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  {breadcrumbs.length - 1 === idx ? (
                    <BreadcrumbPage>{bc.title}</BreadcrumbPage>
                  ) : (
                    <BreadcrumbLink onClick={() => router.push(bc.path)}>{bc.title}</BreadcrumbLink>
                  )}
                </BreadcrumbItem>
              </Fragment>
            ))}
          </BreadcrumbList>
        </Breadcrumb>
      </div>
      <div className="ml-auto px-3">
        <NavActions router={router} current={current} user={user} />
      </div>
    </header>
  )
}
