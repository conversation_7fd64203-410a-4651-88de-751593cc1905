'use client'

import { ReactNode } from 'react'

import { <PERSON>bar, SidebarFooter, SidebarHeader } from '@repo/design-system/components/ui/sidebar'
import { MenuInfo } from '@repo/types'

import { SidebarMenuContainer } from './sidebar-menu-container'
import { Switcher, SwitcherProps } from './switcher'

export interface AdminSidebarPrimitiveProps {
  current?: MenuInfo | null
  menus: MenuInfo[]
  logo?: ReactNode
}

export type AdminSidebarProps = AdminSidebarPrimitiveProps & SwitcherProps

export function AdminSidebar(props: AdminSidebarProps) {
  const { current, module, modules, router, menus } = props

  const hadSide = current?.hadSide ?? true

  if (!hadSide) {
    return null
  }

  return (
    <Sidebar collapsible="icon">
      <SidebarHeader>
        <Switcher modules={modules} module={module} router={router} />
      </SidebarHeader>
      <SidebarMenuContainer current={current} menus={menus} router={router} />
      <SidebarFooter className="text-center whitespace-pre-wrap">
        <div className="flex w-full items-center justify-center">
          {props.logo || <div className="h-6 w-16 font-medium">--</div>}
        </div>
      </SidebarFooter>
    </Sidebar>
  )
}
