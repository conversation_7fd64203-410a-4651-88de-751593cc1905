'use client'

import { group } from 'radash'
import { Fragment } from 'react'

import { ScrollArea } from '@repo/design-system/components/ui/scroll-area'
import { Separator } from '@repo/design-system/components/ui/separator'
import { SidebarContent, SidebarGroup, SidebarGroupLabel, SidebarMenu } from '@repo/design-system/components/ui/sidebar'
import { BaseAppRouter, MenuInfo } from '@repo/types'

import { SidebarMenuItem } from './sidebar-menu-item'

export interface SidebarMenuContainerProps {
  router: BaseAppRouter
  current?: MenuInfo | null
  menus: MenuInfo[]
}

export function SidebarMenuContainer(props: SidebarMenuContainerProps) {
  const { menus, router, current } = props

  const groupData = group(menus, (item) => item.group || 'default')

  const groupKeys = Object.keys(groupData)

  return (
    <SidebarContent className="gap-0">
      <ScrollArea>
        {groupKeys.map((groupKey) => {
          const groupItems = groupData[groupKey] || []
          return (
            <Fragment key={groupKey}>
              <Separator />
              <SidebarGroup>
                <SidebarGroupLabel className="h-auto py-1 group-data-[collapsible=icon]:hidden">{groupKey}</SidebarGroupLabel>
                <SidebarMenu>
                  {groupItems.map((item) => (
                    <SidebarMenuItem key={item.id} current={current} menu={item} router={router} />
                  ))}
                </SidebarMenu>
              </SidebarGroup>
            </Fragment>
          )
        })}
      </ScrollArea>
    </SidebarContent>
  )
}
