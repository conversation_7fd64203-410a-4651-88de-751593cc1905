'use client'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@repo/design-system/components/ui/dropdown-menu'
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem, useSidebar } from '@repo/design-system/components/ui/sidebar'
import { AwesomeIcon } from '@repo/fe-helper/components'
import { BaseAppRouter, ModuleInfo } from '@repo/types'
import { ChevronsUpDown, type LucideProps } from 'lucide-react'
import { type ReactElement } from 'react'

export interface SwitcherProps {
  module?: ModuleInfo | null
  modules: ModuleInfo[]
  router: BaseAppRouter
}

export function Switcher(props: SwitcherProps) {
  const { module: md, modules, router } = props
  const { isMobile } = useSidebar()
  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                <AwesomeIcon icon={md?.icon} className="size-4" />
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">{md?.name || '--'}</span>
                <span className="truncate text-xs">{md?.description || '--'}</span>
              </div>
              <ChevronsUpDown className="ml-auto" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            align="start"
            side={isMobile ? 'bottom' : 'right'}
            sideOffset={4}
          >
            <DropdownMenuLabel className="text-muted-foreground text-xs">Systems</DropdownMenuLabel>
            {modules.map((md, idx) => (
              <DropdownMenuItem
                key={md.id}
                className="gap-2 p-2 hover:cursor-pointer hover:text-blue-500 focus:text-blue-500"
                onClick={() => router.push(md.url)}
              >
                <div className="flex size-6 items-center justify-center rounded-sm border">
                  <AwesomeIcon icon={md?.icon} className="size-4 shrink-0" />
                </div>
                {md.name}
                <DropdownMenuShortcut>{`⌘${idx + 1}`}</DropdownMenuShortcut>
              </DropdownMenuItem>
            ))}
            <DropdownMenuSeparator />
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}

export interface SystemSwitcherProps {
  systems: { name: string; logo: ReactElement<LucideProps>; plan: string }[]
}
