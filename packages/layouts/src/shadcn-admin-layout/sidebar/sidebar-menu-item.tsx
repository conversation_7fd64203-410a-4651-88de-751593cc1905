'use client'

import { Airplay, ChevronRight, ListCollapse } from 'lucide-react'

import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@repo/design-system/components/ui/collapsible'
import {
  SidebarMenuItem as ShadcnSidebarMenuItem,
  SidebarMenuButton,
  SidebarMenuSub,
  useSidebar,
} from '@repo/design-system/components/ui/sidebar'
import { cn } from '@repo/design-system/lib/utils'
import { AwesomeIcon } from '@repo/fe-helper/components'
import { BaseAppRouter, MenuInfo } from '@repo/types'

export interface SidebarMenuItemProps {
  router: BaseAppRouter
  current?: MenuInfo | null
  menu: MenuInfo
}

export function SidebarMenuItem({ router, menu, current }: SidebarMenuItemProps) {
  const { setOpen, open } = useSidebar()

  if (!menu.children?.length) {
    return (
      <ShadcnSidebarMenuItem>
        <SidebarMenuButton
          className={cn(
            'text-inherit',
            'data-[active=true]:bg-[#e6f4ff] data-[active=true]:text-[#1677ff]',
            'data-[state=open]:bg-[#e6f4ff] data-[state=open]:text-[#1677ff]',
            'hover:bg-[#e6f4ff] hover:text-[#1677ff]'
          )}
          tooltip={menu.title}
          isActive={current?.id === menu.id}
          onClick={() => router.push(menu.path)}
        >
          <AwesomeIcon icon={menu.icon} fallback={<Airplay />} />
          <span title={menu.title}>{menu.title}</span>
        </SidebarMenuButton>
      </ShadcnSidebarMenuItem>
    )
  }

  return (
    <Collapsible asChild defaultOpen className="group/collapsible">
      <ShadcnSidebarMenuItem>
        <CollapsibleTrigger asChild>
          <SidebarMenuButton
            className="flex items-center"
            tooltip={menu.title}
            onClick={(e) => {
              if (!open) {
                e.preventDefault()
                setOpen(true)
              }
            }}
          >
            <AwesomeIcon icon={menu.icon} fallback={<ListCollapse />} />
            <span title={menu.title} className="flex-1 overflow-hidden text-ellipsis whitespace-nowrap">
              {menu.title}
            </span>
            <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
          </SidebarMenuButton>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <SidebarMenuSub className="mr-0 pr-0">
            {menu.children?.map((subItem) => (
              <SidebarMenuItem key={subItem.id} current={current} menu={subItem} router={router} />
            ))}
          </SidebarMenuSub>
        </CollapsibleContent>
      </ShadcnSidebarMenuItem>
    </Collapsible>
  )
}
