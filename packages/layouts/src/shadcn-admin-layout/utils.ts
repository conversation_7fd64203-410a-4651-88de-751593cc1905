import { MenuInfo } from '@repo/types'

export function findMenuPaths(menus: MenuInfo[], currentPathname: string) {
  const dfs = (node: MenuInfo, targetPathname: string, paths: MenuInfo[] = []): MenuInfo[] => {
    if (!node) {
      return []
    }

    paths.push(node)

    if (node.path === targetPathname) {
      return [...paths]
    }

    for (const child of node.children || []) {
      const result = dfs(child, targetPathname, paths)
      if (result.length) {
        return result
      }
    }
    paths.pop()
    return []
  }

  const pseudotree: MenuInfo = {
    id: 'root',
    path: '/page',
    title: 'Root',
    children: menus,
    parentId: null,
    code: 'root',
    module: 'root',
    sort: 0,
    target: 0,
  }

  const pathMenus = dfs(pseudotree, currentPathname)
  // 移除伪造的根节点
  pathMenus?.shift()

  return pathMenus
}
