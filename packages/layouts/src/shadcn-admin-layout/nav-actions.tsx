'use client'

import { Avatar, AvatarFallback, AvatarImage } from '@repo/design-system/components/ui/avatar'
import { Button } from '@repo/design-system/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@repo/design-system/components/ui/popover'
import { Separator } from '@repo/design-system/components/ui/separator'
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@repo/design-system/components/ui/sidebar'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import { BadgeCheck, Bell, CreditCard, LogOut, Sparkles } from 'lucide-react'
import { useEffect, useState } from 'react'

import { BaseAppRouter, BaseUserInfo, MenuInfo } from '@repo/types'
import { Logout } from './logout'

export interface NavActionsProps {
  user?: BaseUserInfo | null
  router: BaseAppRouter
  current?: MenuInfo | null
}

export function NavActions(props: NavActionsProps) {
  const [currentTime, setCurrentTime] = useState(() => dayjs().locale('zh-cn').format('YYYY-MM-DD dddd'))
  const { user, router, current } = props
  const { name = 'Unknown', email = '<EMAIL>', avatar = '' } = user || {}

  useEffect(() => {
    setCurrentTime(dayjs().locale('zh-cn').format('YYYY-MM-DD dddd'))
  }, [])

  return (
    <div className="flex items-center gap-1.5 text-sm">
      <div className="text-muted-foreground hidden font-medium md:inline-block">{currentTime}</div>

      <Separator orientation="vertical" className="h-4 w-px" />

      <Popover>
        <PopoverTrigger asChild>
          <Button variant="ghost" className="data-[state=open]:bg-accent/70 h-10 w-10 p-0">
            <Bell />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-56 overflow-hidden rounded-lg" align="center">
          <div>暂无消息</div>
        </PopoverContent>
      </Popover>

      <Separator orientation="vertical" className="h-4 w-[1px]" />

      <Popover>
        <PopoverTrigger asChild>
          <Button variant="ghost" size="icon" className="data-[state=open]:bg-accent/70 h-10 w-fit px-1">
            <Avatar className="h-7 w-7">
              <AvatarImage src={avatar} alt={name} />
              <AvatarFallback className="text-white" style={{ background: 'linear-gradient(330deg,#8e4ec6 0,#3e63dd 100%)' }}>
                {name.slice(0, 1)}
              </AvatarFallback>
            </Avatar>
            <span className="truncate text-sm font-semibold">{name}</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-56 overflow-hidden rounded-lg p-0" align="end">
          <Sidebar collapsible="none" className="bg-transparent">
            <SidebarContent className="gap-0">
              <SidebarHeader className="border-b">
                <div className="flex items-center gap-2 text-left text-sm">
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarImage src={avatar} alt={name} />
                    <AvatarFallback className="rounded-lg">{name.slice(0, 1)}</AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">{name}</span>
                    <span className="truncate text-xs">{email}</span>
                  </div>
                </div>
              </SidebarHeader>
              <SidebarGroup className="border-b last:border-none">
                <SidebarGroupContent className="gap-0">
                  <SidebarMenu>
                    <SidebarMenuItem>
                      <SidebarMenuButton>
                        <Sparkles />
                        <span className="ml-2">Upgrade to Pro</span>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  </SidebarMenu>
                </SidebarGroupContent>
              </SidebarGroup>

              <SidebarGroup className="border-b last:border-none">
                <SidebarGroupContent className="gap-0">
                  <SidebarMenu>
                    <SidebarMenuItem>
                      <SidebarMenuButton>
                        <BadgeCheck />
                        <span className="ml-2">Account</span>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                    <SidebarMenuItem>
                      <SidebarMenuButton>
                        <CreditCard />
                        <span className="ml-2">Billing</span>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                    <SidebarMenuItem>
                      <SidebarMenuButton>
                        <Bell />
                        <span className="ml-2">Notifications</span>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  </SidebarMenu>
                </SidebarGroupContent>
              </SidebarGroup>

              <SidebarGroup className="border-b last:border-none">
                <SidebarGroupContent className="gap-0">
                  <SidebarMenu>
                    <SidebarMenuItem>
                      <Logout current={current} router={router}>
                        <SidebarMenuButton>
                          <LogOut />
                          <span className="ml-2">Log out</span>
                        </SidebarMenuButton>
                      </Logout>
                    </SidebarMenuItem>
                  </SidebarMenu>
                </SidebarGroupContent>
              </SidebarGroup>
            </SidebarContent>
          </Sidebar>
        </PopoverContent>
      </Popover>
    </div>
  )
}
