{"name": "@repo/layouts", "version": "0.0.0", "private": true, "license": "ISC", "author": "vic", "exports": {"./shadcn-admin-layout": "./src/shadcn-admin-layout/index.ts", "./next": "./src/next/index.ts"}, "scripts": {"clean": "rimraf .turbo dist node_modules", "lint": "oxlint . && eslint .", "lint:fix": "prettier --check --write . && oxlint --fix --fix-suggestions . && eslint . --fix"}, "lint-staged": {"*": ["prettier --check --write", "oxlint --fix --fix-suggestions", "eslint --fix"]}, "dependencies": {"@micro-zoe/micro-app": "1.0.0-rc.26", "dayjs": "^1.11.13", "lucide-react": "^0.507.0", "next": "15.3.3"}, "devDependencies": {"@repo/design-system": "workspace:*", "@repo/eslint-config": "workspace:*", "@repo/fe-helper": "workspace:*", "@repo/types": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^20.11.24", "@types/react": "^19", "@types/react-dom": "^19", "antd": "^5.25.4", "antd-style": "^3.7.1", "next-themes": "^0.4.6", "react": "^19.1.0", "swr": "^2.3.3", "typescript": "5.5.4"}, "peerDependencies": {"@repo/design-system": "workspace:*", "antd": "^5.25.4", "antd-style": "^3.7.1", "next-themes": "^0.4.6", "react": "^19.1.0", "swr": "^2.3.3"}}