@theme static {
  /* 主色 */
  --color-primary: #0064ff;
  /* 主色前景色 */
  --color-primary-foreground: #ffffff;
  /* 悬浮 */
  --color-primary-1: #0e42d2;
  /* 点击 */
  --color-primary-2: #4080ff;
  /* 特殊场景 */
  --color-primary-3: #6aa1ff;
  /* 一般禁用 */
  --color-primary-4: #94bfff;
  /* 文字禁用 */
  --color-primary-5: #bedaff;
  /* 浅色/白底悬浮 */
  --color-primary-6: #e8f3ff;

  /* 按钮描边 */
  --color-neutral-border: #86909c;
  /* 悬浮 */
  --color-neutral-border-1: #c9cdd4;
  /* 一般 */
  --color-neutral-border-2: #e5e6eb;
  /* 浅色 */
  --color-neutral-border-3: #f2f3f5;

  /* 强调/图标/特殊 */
  --color-neutral-fill: #4e5969;
  /* 重/特殊场景 */
  --color-neutral-fill-1: #c9cdd4;
  /* 灰底悬浮 */
  --color-neutral-fill-2: #e5e6eb;
  /* 一般/常规/白底悬浮 */
  --color-neutral-fill-3: #f2f3f5;
  /* 禁用 */
  --color-neutral-fill-4: #f7f8fa;

  /* 强调/正文标题 */
  --color-neutral-text: #1d2129;
  /* 次强调/正文标题 */
  --color-neutral-text-1: #4e5969;
  /* 次要信息 */
  --color-neutral-text-2: #86909c;
  /* 置灰信息 */
  --color-neutral-text-3: #c9cdd4;
  /* 纯白文字 */
  --color-neutral-text-4: #ffffff;

  /* Functional color - Success */

  /* Success 常规 (Regular) */
  --color-success: #00b42a;

  /* Success-1 点击 (Click) */
  --color-success-1: #009a29;

  /* Success-2 悬浮 (Hover) */
  --color-success-2: #23c343;

  /* Success-3 禁用 (Disabled) */
  --color-success-3: #7be188;

  /* Success-4 特殊场景 (Special Scene) */
  --color-success-4: #aff0b5;

  /* Success-5 浅色背景 (Light Background) */
  --color-success-5: #e8ffea;

  /* Functional color - Warning */

  /* Warning 常规 (Regular) */
  --color-warning: #ff7d00;

  /* Warning-1 点击 (Click) */
  --color-warning-1: #d25f00;

  /* Warning-2 悬浮 (Hover) */
  --color-warning-2: #ff9a2e;

  /* Warning-3 禁用 (Disabled) */
  --color-warning-3: #ffcf8b;

  /* Warning-4 特殊场景 (Special Scene) */
  --color-warning-4: #ffe4ba;

  /* Warning-5 浅色背景 (Light Background) */
  --color-warning-5: #fff7e8;

  /* Functional color - Danger */

  /* Danger 常规 (Regular) */
  --color-danger: #f53f3f;

  /* Danger-1 点击 (Click) */
  --color-danger-1: #cb2634;

  /* Danger-2 悬浮 (Hover) */
  --color-danger-2: #f76560;

  /* Danger-3 禁用 (Disabled) */
  --color-danger-3: #fbaca3;

  /* Danger-4 特殊场景 (Special Scene) */
  --color-danger-4: #fdcdc5;

  /* Danger-5 浅色背景 (Light Background) */
  --color-danger-5: #ffece8;

  /* 其他系统色 - 碧清青 Cyan */

  /* Cyan 点击 (Click) */
  --color-cyan: #00b42a;

  /* Cyan-1 常规 (Regular) */
  --color-cyan-1: #009a29;

  /* Cyan-2 悬浮 (Hover) */
  --color-cyan-2: #23c343;

  /* Cyan-3 禁用 (Disabled) */
  --color-cyan-3: #7be188;

  /* Cyan-4 特殊场景 (Special Scene) */
  --color-cyan-4: #aff0b5;

  /* Cyan-5 浅色背景 (Light Background) */
  --color-cyan-5: #e8fffb;

  /* 其他系统色 - 晚秋红 Orange Red */

  /* Orange Red 常规 (Regular) */
  --color-orange-red: #f77234;

  /* Orange Red-1 点击 (Click) */
  --color-orange-red-1: #cc5120;

  /* Orange Red-2 悬浮 (Hover) */
  --color-orange-red-2: #f99d57;

  /* Orange Red-3 禁用 (Disabled) */
  --color-orange-red-3: #fcc59f;

  /* Orange Red-4 特殊场景 (Special Scene) */
  --color-orange-red-4: #fdddc3;

  /* Orange Red-5 浅色背景 (Light Background) */
  --color-orange-red-5: #fff3e8;

  /* 其他系统色 - 黄昏 Gold */

  /* Gold 常规 (Regular) */
  --color-gold: #f7ba1e;

  /* Gold-1 点击 (Click) */
  --color-gold-1: #cc9213;

  /* Gold-2 悬浮 (Hover) */
  --color-gold-2: #f9cc45;

  /* Gold-3 禁用 (Disabled) */
  --color-gold-3: #fce996;

  /* Gold-4 特殊场景 (Special Scene) */
  --color-gold-4: #fdf4bf;

  /* Gold-5 浅色背景 (Light Background) */
  --color-gold-5: #fffce8;

  /* 其他系统色 - 海蔚蓝 Blue */

  /* Blue 常规 (Regular) */
  --color-blue: #3491fa;

  /* Blue-1 点击 (Click) */
  --color-blue-1: #206ccf;

  /* Blue-2 悬浮 (Hover) */
  --color-blue-2: #57a9fb;

  /* Blue-3 禁用 (Disabled) */
  --color-blue-3: #9fd4fd;

  /* Blue-4 特殊场景 (Special Scene) */
  --color-blue-4: #c3e7fe;

  /* Blue-5 浅色背景 (Light Background) */
  --color-blue-5: #e8f7ff;

  /* 其他系统色 - 暗夜紫 Purple */

  /* Purple 常规 (Regular) */
  --color-purple: #722ed1;

  /* Purple-1 点击 (Click) */
  --color-purple-1: #551db0;

  /* Purple-2 悬浮 (Hover) */
  --color-purple-2: #8d4eda;

  /* Purple-3 禁用 (Disabled) */
  --color-purple-3: #c396ed;

  /* Purple-4 特殊场景 (Special Scene) */
  --color-purple-4: #ddbef6;

  /* Purple-5 浅色背景 (Light Background) */
  --color-purple-5: #f5e8ff;

  /* 其他系统色 - 柠檬黄 Yellow */

  /* Yellow 常规 (Regular) */
  --color-yellow: #fadc19;

  /* Yellow-1 点击 (Click) */
  --color-yellow-1: #cfaf0f;

  /* Yellow-2 悬浮 (Hover) */
  --color-yellow-2: #fbe842;

  /* Yellow-3 禁用 (Disabled) */
  --color-yellow-3: #fdfa94;

  /* Yellow-4 特殊场景 (Special Scene) */
  --color-yellow-4: #fefebe;

  /* Yellow-5 浅色背景 (Light Background) */
  --color-yellow-5: #feffe8;

  /* 新生绿 Lime */

  /* Lime 常规 (Regular) */
  --color-lime: #9fdb1d;

  /* Lime-1 点击 (Click) */
  --color-lime-1: #7eb712;

  /* Lime-2 悬浮 (Hover) */
  --color-lime-2: #b5e241;

  /* Lime-3 禁用 (Disabled) */
  --color-lime-3: #dcf190;

  /* Lime-4 特殊场景 (Special Scene) */
  --color-lime-4: #edf8bb;

  /* Lime-5 浅色背景 (Light Background) */
  --color-lime-5: #fcffe8;

  /* 青春紫 Pink Purple */

  /* Pink Purple 常规 (Regular) */
  --color-pink-purple: #d91ad9;

  /* Pink Purple-1 点击 (Click) */
  --color-pink-purple-1: #b010b6;

  /* Pink Purple-2 悬浮 (Hover) */
  --color-pink-purple-2: #e13edb;

  /* Pink Purple-3 禁用 (Disabled) */
  --color-pink-purple-3: #f08ee6;

  /* Pink Purple-4 特殊场景 (Special Scene) */
  --color-pink-purple-4: #f7baef;

  /* Pink Purple-5 浅色背景 (Light Background) */
  --color-pink-purple-5: #ffe8fb;

  /* 品红 Magenta */

  /* Magenta 常规 (Regular) */
  --color-magenta: #f5319d;

  /* Magenta-1 点击 (Click) */
  --color-magenta-1: #cb1e83;

  /* Magenta-2 悬浮 (Hover) */
  --color-magenta-2: #f754a8;

  /* Magenta-3 禁用 (Disabled) */
  --color-magenta-3: #fb9dc7;

  /* Magenta-4 特殊场景 (Special Scene) */
  --color-magenta-4: #fdc2db;

  /* Magenta-5 浅色背景 (Light Background) */
  --color-magenta-5: #ffe8f1;
}
