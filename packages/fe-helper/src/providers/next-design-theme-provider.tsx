import { Toaster } from '@repo/design-system/components/ui/sonner'
import { TooltipProvider } from '@repo/design-system/components/ui/tooltip'
import type { ThemeProviderProps } from 'next-themes'
import { ThemeProvider as NextThemeProvider } from 'next-themes'

export const NextDesignThemeProvider = ({ children, ...properties }: ThemeProviderProps) => (
  <NextThemeProvider attribute="class" defaultTheme="light" enableSystem disableTransitionOnChange {...properties}>
    <Toaster richColors position="top-center" />
    <TooltipProvider>{children}</TooltipProvider>
  </NextThemeProvider>
)
