'use client'

import type { Any } from '@repo/types'
import { isArray, isString } from 'radash'
import { SWRConfig, SWRConfiguration } from 'swr'
import { request } from '../request'
import { responseUnpackMiddleware as respUnpack } from '../swr'

function fetcher(arg: Any) {
  if (isString(arg)) {
    return request(arg).then((res) => res.data)
  }
  if (isArray(arg)) {
    const [url, init] = arg
    return request(url, init).then((res) => res.data)
  }
}

export function SWRProvider({ children, options }: { children: React.ReactNode; options?: SWRConfiguration }) {
  return (
    <SWRConfig value={{ revalidateOnFocus: false, shouldRetryOnError: false, fetcher, use: [respUnpack], ...options }}>
      {children}
    </SWRConfig>
  )
}
