'use client'

import { App, type AppProps, ConfigProvider, type ConfigProviderProps } from 'antd'
import { type StyleProvider } from 'antd-style'
import zhCN from 'antd/locale/zh_CN'

import type React from 'react'

import 'dayjs/locale/zh-cn'

import { NextAntdRegistry } from './next-antd-registry'

export interface AntdProviderProps {
  config?: ConfigProviderProps
  style?: Parameters<typeof StyleProvider>[0]
  app?: AppProps
}

export const NextAntdProvider: React.FC<React.PropsWithChildren<AntdProviderProps>> = (props) => {
  return (
    <NextAntdRegistry {...props.style}>
      <ConfigProvider {...props.config} locale={zhCN} theme={{ cssVar: true, hashed: false, ...props.config?.theme }}>
        <App {...props.app}>{props.children}</App>
      </ConfigProvider>
    </NextAntdRegistry>
  )
}
