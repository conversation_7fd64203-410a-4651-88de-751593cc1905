'use client'

import { StyleProvider, extractStaticStyle } from 'antd-style'
import { useServerInsertedHTML } from 'next/navigation'
import { useRef } from 'react'

import '../patch/v5'

type AntdRegistryProps = Omit<Parameters<typeof StyleProvider>[0], 'cache'>

export function NextAntdRegistry(props: AntdRegistryProps) {
  const inserted = useRef(false)

  useServerInsertedHTML(() => {
    if (inserted.current) {
      return
    }
    inserted.current = true
    const styles = extractStaticStyle().map((item) => item.style)
    return styles
  })

  return (
    <StyleProvider
      hashPriority="high"
      // @ts-expect-error - type error
      layer
      {...props}
      speedy
      autoClear
      cache={extractStaticStyle.cache}
    >
      {props.children}
    </StyleProvider>
  )
}
