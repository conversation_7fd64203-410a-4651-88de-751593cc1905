'use client'

import { AppProgressProvider, AppProgressProviderProps } from '@bprogress/next'

import type { PropsWithChildren } from 'react'

export function ProgressBarProvider({ children, ...props }: PropsWithChildren<AppProgressProviderProps>) {
  return (
    <AppProgressProvider height="4px" color="#1677ffbb" options={{ showSpinner: false }} shallowRouting {...props}>
      {children}
    </AppProgressProvider>
  )
}
