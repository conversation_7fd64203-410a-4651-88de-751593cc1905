import { Any } from '@repo/types'
import { Middleware, SWRHook } from 'swr'

interface BaseResponse<T = Any> {
  data: T
  code: number
  message: string
}

export const responseUnpackMiddleware: Middleware = (useSWRNext: SWRHook) => {
  return (key, fetcher, config) => {
    const ret = useSWRNext(key, fetcher, config)

    const resp = ret.data as BaseResponse

    let error = ret.error

    if (!resp) {
      return ret
    }

    if (resp.code !== 200) {
      error = resp
    }

    return { ...ret, error, data: resp.data }
  }
}
