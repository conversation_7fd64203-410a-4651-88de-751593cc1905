import { Any } from '@repo/types'
import { AxiosRequestConfig } from 'axios'
import { isString } from 'radash'
import { useState } from 'react'
import useSWR, { SWRConfiguration } from 'swr'

import { SWRKey } from '../types'

export interface UsePaginationSWROptions<Result = Any> extends SWRConfiguration<Result> {
  paginationIn?: 'data' | 'params'
}

export function usePaginationSWR<Result = Any, Data = Any, Params = Any>(
  key: SWRKey,
  config?: UsePaginationSWROptions<Result>
) {
  const axiosRequestConfig = isString(key) ? void 0 : key[1]
  const defaultData = axiosRequestConfig?.data
  const defaultParams = axiosRequestConfig?.params

  const defaultCurrent = defaultParams?.current ?? defaultData?.current ?? 1
  const defaultSize = defaultParams?.size ?? defaultData?.size ?? 10

  const [data, setData] = useState<Data>(defaultData)
  const [params, setParams] = useState<Params>(defaultParams)
  const [current, setCurrent] = useState(defaultCurrent)
  const [size, setSize] = useState(defaultSize)

  const api = isString(key) ? key : key[0]

  const paginationIn = config?.paginationIn ?? (axiosRequestConfig?.method?.toLowerCase() === 'post' ? 'data' : 'params')

  const axiosOpts: AxiosRequestConfig = {
    ...axiosRequestConfig,
    data: paginationIn === 'data' ? { ...data, current, size } : data,
    params: paginationIn === 'params' ? { ...params, current, size } : params,
  }

  const ret = useSWR<Result>([api, axiosOpts], config)

  const pagination = { current, setCurrent, size, setSize }

  return {
    swr: ret,
    data,
    setData,
    params,
    setParams,
    pagination,
  }
}
