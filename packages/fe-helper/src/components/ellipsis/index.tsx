'use client'

import ReactEllipsis from 'react-ellipsis-component'

import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@repo/design-system/components/ui/tooltip'
import { cn } from '@repo/design-system/lib/utils'

import { CopyableToClipboard } from '../copyable-to-clipboard'
import { EllipsisProps } from './types'

export function Ellipsis(props: EllipsisProps) {
  const { className, ...rcEllipsisProps } = props

  return (
    <TooltipProvider>
      <Tooltip delayDuration={1000}>
        <TooltipTrigger asChild>
          <div className={cn('inline-flex items-center gap-1 break-all', className)}>
            <ReactEllipsis {...rcEllipsisProps} ellipsis />
            <CopyableToClipboard text={rcEllipsisProps.text} className="text-gray-400" />
          </div>
        </TooltipTrigger>
        <TooltipContent className="max-w-3xs">
          <p className="whitespace-pre-wrap">{rcEllipsisProps.text}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
