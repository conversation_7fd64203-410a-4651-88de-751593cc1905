'use client'

import { Loader } from 'lucide-react'

import { cn } from '@repo/design-system/lib/utils'

export interface BaseLoadingProps {
  classNames?: {
    root?: string
    mask?: string
    loader?: string
  }
  className?: string
  style?: React.CSSProperties
  styles?: Partial<Record<'root' | 'mask' | 'loader', React.CSSProperties>>
}

export function BaseLoading(props: BaseLoadingProps) {
  const { className, classNames, style, styles } = props
  return (
    <div
      className={cn('absolute top-0 left-0 z-10 flex h-full w-full items-end justify-center', className, classNames?.root)}
      style={styles?.root ?? style}
    >
      <div className={cn('flex h-full w-full items-center justify-center bg-black/20', classNames?.mask)} style={styles?.mask}>
        <Loader className={cn('text-primary animate-spin', classNames?.loader)} style={styles?.loader} />
      </div>
    </div>
  )
}
