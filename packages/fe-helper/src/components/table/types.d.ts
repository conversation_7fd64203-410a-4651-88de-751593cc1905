import '@tanstack/react-table'

import { BaseTableFilterProps } from './base-table/base-table-filter/types'

declare module '@tanstack/react-table' {
  interface ColumnDefBase<TData extends RowData, TValue> {
    id: string
    /** 列名称 */
    title: string
    /** 固定列 */
    fixed?: 'left' | 'right' | false
    /** 对齐方式 */
    align?: 'left' | 'right' | 'center'
    filter?: Partial<Omit<BaseTableFilterProps<TData>, 'column'>>
    className?: string | ((column: CellContext<TData, TValue>) => string)
    visible?: boolean | ((column: CellContext<TData, TValue>) => boolean)
    hiddenInColumn?: boolean
  }
}
