'use client'

import { Head<PERSON>, RowD<PERSON> } from '@tanstack/react-table'
import { ArrowDownIcon, ArrowUpIcon, ChevronDown, ChevronsUpDown, ChevronUp, EyeOff } from 'lucide-react'

import { But<PERSON> } from '@repo/design-system/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@repo/design-system/components/ui/dropdown-menu'
import { cn } from '@repo/design-system/lib/utils'

export interface TableSorterProps<TData extends RowData = unknown> {
  header: Header<TData, unknown>
}

export function TableSorter<TData extends RowData = unknown>(props: TableSorterProps<TData>) {
  const { header } = props
  const column = header.column

  const canSort = column.getCanSort()
  const sort = column.getIsSorted()

  const isAsc = sort === 'asc'
  const isDesc = sort === 'desc'

  if (!canSort) return null

  console.log(sort, 'sort')

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className={cn('p-0', sort && 'text-primary')}>
          {!sort && <ChevronsUpDown />}
          {isAsc && <ChevronUp />}
          {isDesc && <ChevronDown />}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start">
        <DropdownMenuGroup>
          <DropdownMenuItem
            className={cn(isAsc ? 'text-primary bg-primary-foreground' : 'text-muted-foreground/70')}
            onClick={() => (isAsc ? column.clearSorting() : column.toggleSorting(false, true))}
          >
            <ArrowUpIcon className="mr-2 h-3.5 w-3.5" />
            Asc
          </DropdownMenuItem>
          <DropdownMenuItem
            className={cn(isDesc ? 'text-primary bg-primary-foreground' : 'text-muted-foreground/70')}
            onClick={() => (isDesc ? column.clearSorting() : column.toggleSorting(true, true))}
          >
            <ArrowDownIcon className="mr-2 h-3.5 w-3.5" />
            Desc
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem className="text-muted-foreground/70" onClick={() => column.toggleVisibility(false)}>
            <EyeOff className="mr-2 h-3.5 w-3.5" />
            Hide
          </DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
