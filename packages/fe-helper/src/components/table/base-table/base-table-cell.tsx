'use client'

import { Cell, flexRender, RowData } from '@tanstack/react-table'

import { TableCell } from '@repo/design-system/components/ui/table'
import { cn } from '@repo/design-system/lib/utils'

import { BaseTableColumn } from './types'

export interface BaseTableCellProps<TData extends RowData = unknown, TValue = unknown> {
  cell: Cell<TData, TValue>
  className?: string
  classNames?: { root?: string; pinning?: string }
  style?: React.CSSProperties
}

export function BaseTableCell<TData extends RowData = unknown, TValue = unknown>(props: BaseTableCellProps<TData, TValue>) {
  const { cell, className, classNames, style } = props

  const cellCol = cell.column

  const isPinned = cellCol.getIsPinned()
  const isResizing = cellCol.getIsResizing()

  const columnDef: BaseTableColumn<TData, TValue> = cellCol.columnDef as BaseTableColumn<TData, TValue>

  return (
    <TableCell
      style={style}
      className={cn(
        'bg-primary-foreground relative border-b px-3 text-left transition-colors',
        columnDef.align === 'center' && 'text-center',
        columnDef.align === 'right' && 'text-right',
        className,
        classNames?.root
      )}
    >
      <div className="h-full whitespace-normal" style={{ width: cellCol.getSize() }}>
        {flexRender(columnDef.cell, cell.getContext())}
      </div>
      {isPinned && <div className="bg-border absolute top-0 right-0 h-full w-px" />}
      {isResizing && (
        <div className="user-select-none before:bg-primary/80 absolute top-0 -right-2 z-10 flex h-full w-4 cursor-col-resize touch-none justify-center before:absolute before:inset-y-0 before:w-px before:-translate-x-px" />
      )}
    </TableCell>
  )
}
