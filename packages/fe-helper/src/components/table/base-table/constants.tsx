import { Checkbox } from 'antd'

import { Any } from '@repo/types'

import { BaseTableColumn } from './types'

export const DEFAULT_PAGE_SIZE_OPTIONS = [10, 20, 50, 100]

export const ROW_SELECTION_COLUMN_ID = 'ROW_SELECTION_COLUMN_ID'
export const COLUMN_OPTIONS_COLUMN_ID = 'COLUMN_OPTIONS_COLUMN_ID'

export const ROW_SELECTION_COLUMN: BaseTableColumn<Any> = {
  id: ROW_SELECTION_COLUMN_ID,
  title: ' ',
  size: 36,
  header: ({ table }) => (
    <Checkbox
      checked={table.getIsAllPageRowsSelected()}
      indeterminate={table.getIsSomePageRowsSelected()}
      onChange={(e) => table.toggleAllPageRowsSelected(e.target.checked)}
      aria-label="Select all"
      className="translate-y-[2px]"
    />
  ),
  cell: ({ row }) => (
    <Checkbox
      disabled={!row.getCanSelect()}
      checked={row.getIsSelected()}
      onChange={(e) => row.toggleSelected(e.target.checked)}
      aria-label="Select row"
      className="translate-y-[2px]"
    />
  ),
  fixed: 'left',
  enableSorting: false,
  enableHiding: false,
  enablePinning: false,
  enableResizing: false,
  enableColumnFilter: false,
}

export const COLUMN_OPTIONS_COLUMN: BaseTableColumn<Any> = {
  id: COLUMN_OPTIONS_COLUMN_ID,
  title: '操作',
  size: 120,
  fixed: 'right',
  align: 'left',
  enableSorting: false,
  enableHiding: false,
}

export const DEFAULT_COLUMN_PROPS: Partial<BaseTableColumn<Any>> = {
  enableSorting: false,
  size: 200,
}
