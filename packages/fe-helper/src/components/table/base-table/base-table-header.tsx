'use client'

import { flexRender, RowData, Table } from '@tanstack/react-table'
import { forwardRef } from 'react'

import { TableHead, TableHeader, TableRow } from '@repo/design-system/components/ui/table'
import { cn } from '@repo/design-system/lib/utils'
import { Any } from '@repo/types'

import { ROW_SELECTION_COLUMN_ID } from './constants'
import { TableSorter } from './table-sorter'
import { BaseTableColumn } from './types'
import { getCommonPinningStyles } from './utils'

export interface BaseTableHeaderProps<TData extends RowData = unknown> {
  table: Table<TData>
  className?: string
  classNames?: {
    root?: string
    row?: string
    cell?: string
  }
}

export const BaseTableHeader = forwardRef(function _BaseTableHeader(
  props: BaseTableHeaderProps<Any>,
  ref: React.ForwardedRef<HTMLTableSectionElement>
) {
  const { table, className, classNames } = props
  return (
    <TableHeader ref={ref} className={cn('sticky top-0 z-10 min-h-9 shadow-sm', className, classNames?.root)}>
      {table.getHeaderGroups().map((headerGroup) => (
        <TableRow key={headerGroup.id} className={classNames?.row}>
          {headerGroup.headers.map((header) => {
            const isRowSelection = header.id === ROW_SELECTION_COLUMN_ID

            const headerCol = header.column
            const columnDef = headerCol.columnDef as BaseTableColumn<Any, Any>
            const ctx = header.getContext()

            return (
              <TableHead
                key={header.id}
                colSpan={header.colSpan}
                style={getCommonPinningStyles(headerCol)}
                className={cn(
                  'bg-primary-foreground relative h-8 px-3 py-2 whitespace-nowrap',
                  'after:bg-border after:absolute after:top-0 after:right-0 after:h-full after:w-px',
                  isRowSelection && 'px-2',
                  classNames?.cell,
                  headerCol.getIsResizing() && 'bg-accent'
                )}
              >
                <div
                  className={cn(
                    'flex items-center justify-start gap-1',
                    columnDef.align === 'left' && 'justify-start',
                    columnDef.align === 'center' && 'justify-center',
                    columnDef.align === 'right' && 'justify-end',
                    isRowSelection && 'justify-center'
                  )}
                  title={columnDef.title}
                >
                  <div className="flex flex-1 items-center overflow-hidden">
                    {!header.isPlaceholder && flexRender(columnDef.header, ctx)}
                  </div>
                  <div className="flex items-center gap-1">
                    <TableSorter header={header} />
                  </div>
                </div>

                {headerCol.getCanResize() && (
                  <div
                    onDoubleClick={() => headerCol.resetSize()}
                    onMouseDown={header.getResizeHandler()}
                    onTouchStart={header.getResizeHandler()}
                    className={cn(
                      'user-select-none cursor-col-resize touch-none',
                      'absolute top-0 -right-2 z-10 flex h-full w-4 justify-center',
                      'before:absolute before:inset-y-0 before:w-px before:-translate-x-px before:bg-transparent',
                      headerCol.getIsResizing() && 'before:bg-primary/80 before:w-2'
                    )}
                  />
                )}
              </TableHead>
            )
          })}
        </TableRow>
      ))}
    </TableHeader>
  )
})
