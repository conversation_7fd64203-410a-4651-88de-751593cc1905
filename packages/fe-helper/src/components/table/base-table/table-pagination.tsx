'use client'

import { Table } from '@tanstack/react-table'
import { Pagination } from 'antd'

export interface TablePaginationProps<TData> {
  table: Table<TData>
  pageSizeOptions: number[]
}

export function TablePagination<TData>({ table, pageSizeOptions }: TablePaginationProps<TData>) {
  const selectedCount = table.getFilteredSelectedRowModel().rows.length
  const pagination = table.getState().pagination
  return (
    <div className="flex items-center justify-between px-2">
      <div className="text-muted-foreground flex-1 text-sm">{selectedCount > 0 && `已选 ${selectedCount} 项`}</div>
      <div className="flex items-center">
        <Pagination
          size="small"
          showSizeChanger
          showQuickJumper
          showTotal={(total) => `共 ${total} 条`}
          pageSize={pagination.pageSize}
          pageSizeOptions={pageSizeOptions}
          current={pagination.pageIndex + 1}
          total={table.getTotalSize()}
          onChange={(current, size) => {
            table.setRowSelection({})
            table.setPagination({ pageIndex: current - 1, pageSize: size })
          }}
        />
      </div>
    </div>
  )
}
