'use client'

import { RestrictToVerticalAxis } from '@dnd-kit/abstract/modifiers'
import { useSortable } from '@dnd-kit/react/sortable'
import { Column, Table } from '@tanstack/react-table'
import { ArrowLeftToLine, ArrowRightToLine, GripHorizontal, PinOff } from 'lucide-react'

import { Button } from '@repo/design-system/components/ui/button'
import { DropdownMenuItem } from '@repo/design-system/components/ui/dropdown-menu'
import { Label } from '@repo/design-system/components/ui/label'
import { cn } from '@repo/design-system/lib/utils'
import { Any } from '@repo/types'

import { Checkbox } from 'antd'
import { BaseTableColumn } from '../types'

export interface DropdownMenuDndItemProps<TData> {
  table: Table<TData>
  column: Column<TData>
  index: number
}

export function DropdownMenuDndItem<TData>({ column, index, table }: DropdownMenuDndItemProps<TData>) {
  const colVisible = column.getIsVisible()
  const pinned = column.getIsPinned()
  const canHide = column.getCanHide()
  const canPin = column.getCanPin()

  const { ref, handleRef, isDragging } = useSortable({
    id: column.id,
    index,
    modifiers: [RestrictToVerticalAxis],
  })

  const columnDef = column.columnDef as BaseTableColumn<TData, Any>

  return (
    <DropdownMenuItem
      ref={ref}
      onClick={(e) => {
        e.preventDefault()
        e.stopPropagation()
      }}
      asChild
    >
      <div className={cn('flex w-full items-center justify-between gap-2', isDragging && 'bg-accent opacity-50')}>
        <div className="flex w-36 items-center gap-2 overflow-hidden">
          <Checkbox
            className="[&[data-state=checked]_svg]:text-primary-foreground"
            disabled={!canHide}
            checked={colVisible}
            onChange={(e) => column.toggleVisibility(e.target.value)}
          />
          <Button ref={handleRef} size="icon" variant="ghost" className="hover:text-primary h-4 w-4 hover:cursor-move">
            <GripHorizontal />
          </Button>
          <Label className={cn('overflow-hidden text-ellipsis hover:cursor-pointer', !colVisible && 'text-muted-foreground')}>
            {columnDef.title}
          </Label>
        </div>

        <div className="flex items-center gap-2">
          <Button
            size="icon"
            variant="ghost"
            className={cn('hover:text-primary h-3 w-3', pinned === 'left' && 'text-primary')}
            onClick={() => column.pin(pinned === 'left' ? false : 'left')}
            disabled={!canPin}
          >
            {pinned === 'left' ? <PinOff /> : <ArrowLeftToLine />}
          </Button>

          <Button
            size="icon"
            variant="ghost"
            className={cn('hover:text-primary h-3 w-3', pinned === 'right' && 'text-primary')}
            disabled={!canPin}
            onClick={() => {
              table.setColumnPinning((prev) => {
                const { right = [], left = [] } = prev
                if (pinned === 'right') {
                  return { left, right: right.filter((id) => id !== column.id) }
                }
                if (pinned === 'left') {
                  return { left: left.filter((id) => id !== column.id), right: [column.id, ...right] }
                }
                return { left, right: [column.id, ...right] }
              })
            }}
          >
            {pinned === 'right' ? <PinOff /> : <ArrowRightToLine />}
          </Button>
        </div>
      </div>
    </DropdownMenuItem>
  )
}
