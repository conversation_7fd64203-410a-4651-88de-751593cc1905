'use client'

import { move } from '@dnd-kit/helpers'
import { DragDropProvider } from '@dnd-kit/react'
import { Table } from '@tanstack/react-table'
import { Checkbox } from 'antd'
import { Settings } from 'lucide-react'

import { Button } from '@repo/design-system/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@repo/design-system/components/ui/dropdown-menu'
import { Label } from '@repo/design-system/components/ui/label'

import { objectify } from 'radash'
import { ROW_SELECTION_COLUMN_ID } from '../constants'
import { DropdownMenuDndItem } from './dropdown-menu-dnd-item'

interface TableViewerOptionsProps<TData> {
  table: Table<TData>
}

export function TableViewerOptions<TData>({ table }: TableViewerOptionsProps<TData>) {
  const leftColumns = table.getLeftLeafColumns().filter((col) => col.id !== ROW_SELECTION_COLUMN_ID)
  const rightColumns = table.getRightLeafColumns().filter((col) => col.id !== ROW_SELECTION_COLUMN_ID)
  const centerColumns = table.getCenterLeafColumns().filter((col) => col.id !== ROW_SELECTION_COLUMN_ID)

  const visibleCols = table.getVisibleFlatColumns().filter((col) => col.id !== ROW_SELECTION_COLUMN_ID && col.getCanHide())
  const allColumns = table.getAllFlatColumns().filter((col) => col.id !== ROW_SELECTION_COLUMN_ID && col.getCanHide())

  const isAllColumnsVisible = visibleCols.length === allColumns.length

  const isSomeColumnsVisible = visibleCols.length > 0 && visibleCols.length < allColumns.length

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          data-visibility="visible"
          className="hover:text-primary-2 ml-auto flex size-6 cursor-pointer"
        >
          <Settings />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[200px]">
        <DropdownMenuLabel
          className="flex items-center gap-2 hover:cursor-pointer"
          onClick={(e) => {
            e.preventDefault()
            e.stopPropagation()
            const nextVisibleCols = objectify(
              allColumns,
              (col) => col.id,
              () => (isAllColumnsVisible ? false : true)
            )
            table.setColumnVisibility(nextVisibleCols)
          }}
        >
          <Checkbox
            checked={isAllColumnsVisible}
            indeterminate={isSomeColumnsVisible}
            onChange={(e) => {
              const checked = e.target.value
              const nextVisibleCols = objectify(
                allColumns,
                (col) => col.id,
                () => checked
              )

              table.setColumnVisibility(nextVisibleCols)
            }}
          />
          <Label className="hover:cursor-pointer">全部</Label>
        </DropdownMenuLabel>

        <DropdownMenuSeparator />
        {!!leftColumns.length && (
          <>
            <DropdownMenuLabel className="text-muted-foreground text-xs font-normal">固定在左侧</DropdownMenuLabel>
            <DragDropProvider
              onDragEnd={(ev) => {
                const nextLeftCols = move(table.getLeftLeafColumns(), ev)
                const nextCenterCols = table.getCenterLeafColumns()
                const nextRightCols = table.getRightLeafColumns()

                table.setColumnPinning((prev) => ({ ...prev, left: nextLeftCols.map((col) => col.id) }))
                table.setColumnOrder([...nextLeftCols, ...nextCenterCols, ...nextRightCols].map((col) => col.id))
              }}
            >
              {leftColumns.map((col, index) => (
                <DropdownMenuDndItem key={col.id} table={table} column={col} index={index} />
              ))}
            </DragDropProvider>

            <DropdownMenuSeparator />
          </>
        )}

        {!!centerColumns.length && (
          <>
            <DropdownMenuLabel className="text-muted-foreground text-xs font-normal">不固定</DropdownMenuLabel>
            <DragDropProvider
              onDragEnd={(ev) => {
                const nextLeftCols = table.getLeftLeafColumns()
                const nextCenterCols = move(table.getCenterLeafColumns(), ev)
                const nextRightCols = table.getRightLeafColumns()
                table.setColumnOrder([...nextLeftCols, ...nextCenterCols, ...nextRightCols].map((col) => col.id))
              }}
            >
              {centerColumns.map((col, index) => (
                <DropdownMenuDndItem key={col.id} table={table} column={col} index={index} />
              ))}
            </DragDropProvider>

            <DropdownMenuSeparator />
          </>
        )}

        {!!rightColumns.length && (
          <>
            <DropdownMenuLabel className="text-muted-foreground text-xs font-normal">固定在侧</DropdownMenuLabel>
            <DragDropProvider
              onDragEnd={(ev) => {
                const nextLeftCols = table.getLeftLeafColumns()
                const nextCenterCols = table.getCenterLeafColumns()
                const nextRightCols = move(table.getRightLeafColumns(), ev)
                table.setColumnPinning((prev) => ({ ...prev, right: nextRightCols.map((col) => col.id) }))
                table.setColumnOrder([...nextLeftCols, ...nextCenterCols, ...nextRightCols].map((col) => col.id))
              }}
            >
              {rightColumns.map((col, index) => (
                <DropdownMenuDndItem key={col.id} table={table} column={col} index={index} />
              ))}
            </DragDropProvider>
            <DropdownMenuSeparator />
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
