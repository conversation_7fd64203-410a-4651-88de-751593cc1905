'use client'

import { RowData } from '@tanstack/react-table'
import { useSize } from 'ahooks'
import { Empty } from 'antd'
import { RefreshCw } from 'lucide-react'
import { forwardRef, useEffect, useImperativeHandle, useRef } from 'react'

import { <PERSON><PERSON> } from '@repo/design-system/components/ui/button'
import { ScrollArea, ScrollBar } from '@repo/design-system/components/ui/scroll-area'
import { cn } from '@repo/design-system/lib/utils'

import { BaseLoading } from '../../loading'
import { BaseTableBody } from './base-table-body'
import { BaseTableFilter } from './base-table-filter'
import { BaseTableHeader } from './base-table-header'
import { useTable } from './hooks'
import { TableViewerOptions } from './table-column-view-options'
import { TablePagination } from './table-pagination'
import { BaseTableProps, BaseTableRef } from './types'

export const BaseTable = forwardRef(function BaseTable<TData extends RowData = unknown, TValue = unknown>(
  props: BaseTableProps<TData, TValue>,
  ref: React.ForwardedRef<BaseTableRef<TData>>
) {
  const tableScrollRef = useRef<HTMLDivElement>(null)
  const tableHeaderRef = useRef<HTMLTableSectionElement>(null)

  const tableHeaderSize = useSize(tableHeaderRef)

  const { table, pagination, columnFilters, filterColumns } = useTable(props)

  const rows = table.getRowModel().rows

  const isEmpty = rows.length === 0

  useImperativeHandle(ref, () => {
    const scroll = {
      container: tableScrollRef,
      getScrollViewport: () => tableScrollRef.current?.querySelector<HTMLDivElement>('div[data-radix-scroll-area-viewport]'),
    }
    return { table, scroll }
  }, [table])

  useEffect(() => {
    const scrollContainer = tableScrollRef.current?.querySelector('div[data-radix-scroll-area-viewport]')
    if (!scrollContainer) return
    scrollContainer.scrollTo({ left: 0, top: 0 })
  }, [pagination.current, pagination.pageSize, columnFilters.state])

  return (
    <div className={cn('flex h-full flex-col space-y-2 rounded-md bg-white p-2', props.classNames?.root)}>
      <div className="flex items-center justify-between gap-2">
        <BaseTableFilter table={table} filterColumns={filterColumns} />
        <div className="flex items-center gap-1">
          {props.renderToolBar && props.renderToolBar(table)}
          {props.onRefresh && (
            <Button
              type="button"
              size="icon"
              variant="ghost"
              className={cn('hover:text-primary-2 size-6 cursor-pointer', props.loading && 'text-primary-2 animate-spin')}
              title="刷新"
              onClick={props.onRefresh}
            >
              <RefreshCw />
            </Button>
          )}

          <TableViewerOptions table={table} />
        </div>
      </div>
      <ScrollArea type="auto" className="flex-1 overflow-auto rounded-md border" ref={tableScrollRef}>
        <table
          className={cn('w-full table-fixed caption-bottom text-sm', props.classNames?.table)}
          style={{ minWidth: table.getTotalSize() }}
        >
          <BaseTableHeader className={props.classNames?.header} table={table} ref={tableHeaderRef} />
          <BaseTableBody className={props.classNames?.body} table={table} />
        </table>
        {isEmpty && (
          <div className="pointer-events-none absolute top-0 left-0 z-10 flex h-full w-full items-end justify-center">
            <div
              className="bg-primary-foreground flex h-full w-full items-center justify-center"
              style={{ height: `calc(100% - ${tableHeaderSize?.height || 0}px)` }}
            >
              <Empty />
            </div>
          </div>
        )}

        {props.loading && (
          <BaseLoading
            className={props.classNames?.loading}
            styles={{ mask: { height: tableHeaderSize?.height ? `calc(100% - ${tableHeaderSize.height}px)` : 0 } }}
          />
        )}

        <ScrollBar orientation="horizontal" className="z-10 cursor-pointer" />
        <ScrollBar orientation="vertical" className="z-10 cursor-pointer" />
      </ScrollArea>
      <TablePagination table={table} pageSizeOptions={pagination.pageSizeOptions} />
    </div>
  )
})
