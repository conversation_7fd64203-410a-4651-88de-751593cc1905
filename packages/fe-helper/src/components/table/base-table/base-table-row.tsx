'use client'

import { Row, RowData } from '@tanstack/react-table'
import { isFunction } from 'radash'

import { TableRow } from '@repo/design-system/components/ui/table'
import { cn } from '@repo/design-system/lib/utils'
import { Any } from '@repo/types'

import { BaseTableCell } from './base-table-cell'
import { ROW_SELECTION_COLUMN_ID } from './constants'
import { BaseTableColumn } from './types'
import { getCommonPinningStyles } from './utils'

export interface BaseTableRowProps<TData extends RowData = unknown> {
  row: Row<TData>
  className?: string
  classNames?: { root?: string; cell?: string }
  style?: React.CSSProperties
}

export function BaseTableRow<TData extends RowData = unknown>(props: BaseTableRowProps<TData>) {
  const { row, className, classNames, style } = props

  const visibleCells = row.getVisibleCells()

  return (
    <TableRow
      data-state={row.getIsSelected() && 'selected'}
      style={style}
      className={cn(
        '[&[data-state=selected]_td]:bg-primary-5 [&:hover_td]:bg-primary-6 border-b-0',
        className,
        classNames?.root
      )}
    >
      {visibleCells.map((cell, idx) => {
        const cellStyles = getCommonPinningStyles(cell.column)
        const isLast = visibleCells.length === idx + 1
        const context = cell.getContext()
        const columnDef = cell.column.columnDef as BaseTableColumn<TData, Any>

        return (
          <BaseTableCell
            key={cell.id}
            cell={cell}
            className={cn(
              isLast && 'border-r',
              cell.column.id === ROW_SELECTION_COLUMN_ID && '!p-2',
              classNames?.cell,
              isFunction(columnDef.className) ? columnDef.className(context) : columnDef.className
            )}
            style={cellStyles}
          />
        )
      })}
    </TableRow>
  )
}
