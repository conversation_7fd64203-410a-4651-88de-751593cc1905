import {
  CellContext,
  ColumnDef,
  ColumnOrderState,
  ColumnPinningState,
  ColumnResizeMode,
  ColumnSizingTableState,
  Row,
  RowData,
  SortingState,
  Table,
} from '@tanstack/react-table'
import { RefObject } from 'react'

import { Any } from '@repo/types'

import { BaseFilterProps } from './base-table-filter/types'

export interface BaseTableRef<TData extends RowData = unknown> {
  table: Table<TData>
  scroll: {
    container: RefObject<HTMLDivElement | null>
    getScrollViewport: () => HTMLDivElement | null | undefined
  }
}

export type BaseTableColumn<TData extends RowData = Any, TValue = Any> = ColumnDef<TData, TValue> & {
  /** rowKey, 若 accessorKey 不存在，则使用 id 取值 */
  id: string
  /** 列名称 */
  title: string
  /** 固定列 */
  fixed?: 'left' | 'right' | false
  /** 对齐方式 */
  align?: 'left' | 'right' | 'center'
  filter?: Partial<Omit<BaseFilterProps<TData>, 'column'>>
  className?: string | ((column: CellContext<TData, TValue>) => string)
  visible?: boolean
  hiddenInColumn?: boolean
}

export interface BaseTableRowSelectionProps<TData extends RowData = unknown> {
  type?: 'checkbox' | 'radio'
  enabled?: boolean | ((row: Row<TData>) => boolean)
  defaultSelectedRowKeys?: string[]
  selectedRowKeys?: string[]
  onChange?: (selectedRowKeys: string[]) => void
}

export interface BaseTablePaginationProps {
  total?: number
  defaultPageSize?: number
  pageSize?: number
  defaultCurrent?: number
  current?: number
  pageSizeOptions?: number[]
  onChange?: (pagination: { current: number; pageSize: number }) => void
}

export interface BaseTableColumnPinningProps {
  enabled?: boolean
  defaultValue?: ColumnPinningState
  value?: ColumnPinningState
  onChange?: (value: ColumnPinningState) => void
}

export interface BaseTableSortingProps {
  enabled?: boolean
  defaultValue?: SortingState
  value?: SortingState
  onChange?: (sorting: SortingState) => void
}
export interface BaseTableColumnResizingProps {
  enabled?: boolean
  mode?: ColumnResizeMode
  onChange?: (state: ColumnSizingTableState) => void
}

export interface BaseTableColumnFiltersProps {
  enabled?: boolean
  value?: Record<string, Any>
  defaultValue?: Record<string, Any>
  onChange?: (value: Record<string, Any>) => void
}

export interface ColumnVisibilityProps {
  enabled?: boolean
  value?: Record<string, boolean>
  defaultValue?: Record<string, boolean>
  onChange?: (value: Record<string, boolean>) => void
}

export interface ColumnOrderProps {
  enabled?: boolean
  value?: ColumnOrderState
  defaultValue?: ColumnOrderState
  onChange?: (value: ColumnOrderState) => void
}

export interface BaseTableProps<TData extends RowData = Any, TValue = Any> {
  data: TData[]
  columns: BaseTableColumn<TData, TValue>[]
  loading?: boolean
  rowSelection?: BaseTableRowSelectionProps<TData>
  pagination?: BaseTablePaginationProps
  columnPinning?: BaseTableColumnPinningProps
  sorting?: BaseTableSortingProps
  resizing?: BaseTableColumnResizingProps
  filtering?: BaseTableColumnFiltersProps
  columnVisibility?: ColumnVisibilityProps
  columnOrder?: ColumnOrderProps
  rowKey?: string | ((originalRow: TData, index: number, parent?: Row<TData>) => string)
  className?: string
  classNames?: {
    root?: string
    table?: string
    header?: string
    body?: string
    footer?: string
    cell?: string
    row?: string
    loading?: string
  }
  onRefresh?: () => void
  renderToolBar?: (table: Table<TData>) => React.ReactNode
}
