import { Column, RowData } from '@tanstack/react-table'
import { CSSProperties } from 'react'

export function getCommonPinningStyles<TData extends RowData>(column: Column<TData>): CSSProperties {
  const index = column.getIndex()
  const isPinned = column.getIsPinned()
  const isLastLeftPinnedColumn = isPinned === 'left' && column.getIsLastColumn('left')
  const isFirstRightPinnedColumn = isPinned === 'right' && column.getIsFirstColumn('right')

  let boxShadow: string | undefined = void 0
  if (isLastLeftPinnedColumn) {
    boxShadow = '-1px 0px 1px -1px gray inset'
  } else if (isFirstRightPinnedColumn) {
    boxShadow = '1px 0 1px -1px gray inset'
  }

  return {
    boxShadow,
    left: isPinned === 'left' ? `${column.getStart('left')}px` : void 0,
    right: isPinned === 'right' ? `${column.getAfter('right')}px` : void 0,
    // backdropFilter: isPinned ? 'blur(50px)' : void 0,
    // opacity: isPinned ? 0.95 : 1,
    position: isPinned ? 'sticky' : 'relative',
    width: column.getSize(),
    zIndex: isPinned ? (index ? 1 : 2) : 0,
  }
}
