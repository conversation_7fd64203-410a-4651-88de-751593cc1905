'use client'

import { RowData, Table } from '@tanstack/react-table'

import { TableBody } from '@repo/design-system/components/ui/table'

import { BaseTableRow } from './base-table-row'

export interface BaseTableBodyProps<TData extends RowData = unknown> {
  table: Table<TData>
  className?: string
  classNames?: {
    row?: string
    cell?: string
  }
}

export function BaseTableBody<TData extends RowData = unknown>(props: BaseTableBodyProps<TData>) {
  const { table, className, classNames } = props

  const rows = table.getRowModel().rows

  return (
    <TableBody className={className}>
      {rows.map((row) => (
        <BaseTableRow key={row.id} row={row} classNames={{ root: classNames?.row, cell: classNames?.cell }} />
      ))}
    </TableBody>
  )
}
