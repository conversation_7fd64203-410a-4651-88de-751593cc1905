import { RowData, Table } from '@tanstack/react-table'
import React, { Dispatch, SetStateAction } from 'react'

import { Any } from '@repo/types'

import { BaseFilterProps } from './types'

export interface BaseFilterContextValue<TData extends RowData = unknown, TValue = unknown> {
  table: Table<TData>
  visibleFiltersState: Record<string, boolean>
  filters: BaseFilterProps<TData, TValue>[]
  setVisibleFilter: (id: string, visible: boolean) => void
  setVisibleFiltersState: Dispatch<SetStateAction<Record<string, boolean>>>
}

export const BaseTableFilterContext = React.createContext<BaseFilterContextValue<Any, Any> | null>(null)
