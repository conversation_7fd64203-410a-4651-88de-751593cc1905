import { Column, RowData } from '@tanstack/react-table'

import { Any } from '@repo/types'

import { BaseTableColumn } from '../types'

export type BaseFilterType = 'text' | 'number' | 'select' | 'multiple-select' | 'date' | 'datetime' | 'date-range'

export interface BaseFilterProps<TData extends RowData = Any, TValue = Any> {
  /** 当前列 */
  column: BaseTableColumn<TData, TValue>
  /** 过滤器 Id, 默认使用 column Id */
  id: string
  /** 过滤器标题, 默认使用 column Title */
  title: string
  /** 过滤器类型 */
  type: BaseFilterType
  /** 排序号 */
  sort: number
  /** 可隐藏的过滤器 */
  hidable: boolean
  /** 自定义过滤器 */
  render?: (filters: TValue, column: BaseTableColumn<TData, TValue>) => React.ReactNode
  /** 过滤器属性 */
  props?: Any | ((filters: TValue, column: Column<TData, TValue>) => Any)
}

export interface CommonFilterProps {
  id: string
  title: string
  /** 可隐藏的过滤器 */
  hidable: boolean
  className?: string
}
