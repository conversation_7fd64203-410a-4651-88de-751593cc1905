'use client'

import { useControllableValue } from 'ahooks'
import { CheckIcon, CircleX } from 'lucide-react'
import { objectify } from 'radash'
import { useMemo } from 'react'

import { Button } from '@repo/design-system/components/ui/button'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@repo/design-system/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@repo/design-system/components/ui/popover'
import { commandScore } from '@repo/design-system/lib/command-score'
import { cn } from '@repo/design-system/lib/utils'

import { CaretDown } from '../icons'
import { FilterContainer } from './filter-container'
import { CommonFilterProps } from './types'

export interface SelectOption {
  value: string
  label?: string
}
export interface MultipleSelectFilterProps extends CommonFilterProps {
  value?: string[]
  onChange?: (value: string[]) => void
  open?: boolean
  onOpenChange?: (open: boolean) => void
  options?: SelectOption[]
  manualSearch?: boolean
  onSearch?: (value: string) => void
}

export function MultipleSelectFilter(props: MultipleSelectFilterProps) {
  const { options = [], manualSearch, onSearch } = props
  const [value, setValue] = useControllableValue<string[] | undefined>(props)
  const [open, setOpen] = useControllableValue<boolean>(props, { valuePropName: 'open', trigger: 'onOpenChange' })

  const optsMap = useMemo(() => {
    return objectify(
      options,
      (it) => it.value,
      (it) => it.label
    )
  }, [options])

  const labels = value?.map((val) => optsMap[val] ?? val)

  const onFilter = (val: string, search: string, keywords?: string[]) => {
    const label = optsMap[val]
    if (!label) return commandScore(val, search, keywords)
    return commandScore(label, search, keywords)
  }

  return (
    <FilterContainer className="px-0" id={props.id} hidable={props.hidable} isActive={open}>
      <div className="group relative w-full [&:hover>span]:opacity-100">
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="ghost"
              role="combobox"
              aria-expanded={open}
              className={cn(
                'h-7 w-full max-w-36 border-none bg-transparent pr-0 pl-2 text-xs whitespace-nowrap text-black/30 shadow-none ring-0',
                'hover:text-primary focus:ring-0',
                '[&>span]:block [&>span]:flex-1 [&>span]:overflow-hidden [&>span]:text-ellipsis',
                value && 'text-black/80',
                open && 'text-primary'
              )}
            >
              <span className={cn('truncate', !value?.length && 'text-muted-foreground')}>{labels?.join('/')}</span>
              <CaretDown className={cn('ml-3 h-3 w-3 opacity-50 transition-all', open && 'rotate-180 opacity-100')} />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="border-input w-full min-w-[var(--radix-popper-anchor-width)] p-0" align="start">
            <Command shouldFilter={!manualSearch} filter={onFilter}>
              <CommandInput className="h-8 p-0" placeholder="Search..." onValueChange={onSearch} />
              <CommandList style={{ scrollbarWidth: 'thin', scrollbarColor: '#eaeaea transparent', scrollbarGutter: 'stable' }}>
                <CommandEmpty>Not Result</CommandEmpty>
                <CommandGroup>
                  {options.map((opt) => (
                    <CommandItem
                      key={opt.value}
                      value={opt.value}
                      onSelect={(currentValue) => {
                        setValue((prev = []) => {
                          if (prev.includes(currentValue)) {
                            return prev.filter((it) => it !== currentValue)
                          }
                          return [...prev, currentValue]
                        })
                        setOpen(false)
                      }}
                    >
                      {opt.label}
                      {value?.includes(opt.value) && <CheckIcon size={16} className="ml-auto" />}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
        {value && (
          <span
            role="button"
            aria-label="clear"
            className="hover:text-primary absolute top-1/2 right-5 inline-flex h-auto -translate-y-1/2 cursor-pointer p-0 text-black/30 opacity-0 transition-opacity [&_svg]:size-3"
            onClick={() => setValue([])}
          >
            <CircleX />
          </span>
        )}
      </div>
    </FilterContainer>
  )
}
