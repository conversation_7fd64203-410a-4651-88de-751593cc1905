'use client'

import { X } from 'lucide-react'

import { Button } from '@repo/design-system/components/ui/button'
import { Separator } from '@repo/design-system/components/ui/separator'
import { cn } from '@repo/design-system/lib/utils'

import { useBaseTableFilter } from './base-filter-provider'

export interface FilterContainerProps {
  /** 过滤器 Id, 默认使用 column Id */
  id: string
  /** 可隐藏的过滤器 */
  hidable: boolean
  /** 是否激活 */
  isActive?: boolean
  className?: string
  children: React.ReactNode
}

export function FilterContainer(props: FilterContainerProps) {
  const { setVisibleFilter } = useBaseTableFilter()

  return (
    <div
      className={cn(
        'border-input bg-accent hover:border-primary flex h-8 min-w-16 items-center gap-1 rounded border px-2 py-1',
        props.isActive && 'bg-primary/10 border-primary',
        props.className
      )}
    >
      {props.children}
      {props.hidable && (
        <>
          <Separator orientation="vertical" className="h-4" />
          <Button
            variant="ghost"
            className="hover:text-primary h-auto cursor-pointer p-0 text-black/30 hover:bg-transparent has-[>svg]:px-0 [&_svg]:size-3"
            onClick={() => setVisibleFilter(props.id, false)}
          >
            <X size={12} />
          </Button>
        </>
      )}
    </div>
  )
}
