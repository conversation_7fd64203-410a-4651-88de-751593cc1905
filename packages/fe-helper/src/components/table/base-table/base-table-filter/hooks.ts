import { RowData } from '@tanstack/react-table'
import { useMemo } from 'react'

import { Any } from '@repo/types'
import { BaseFilterProps } from './types'

export function useVisibleFilters<TData extends RowData = Any>(props: {
  filters: BaseFilterProps<TData, Any>[]
  visibleFiltersState: Record<string, boolean>
}) {
  const { filters, visibleFiltersState } = props

  return useMemo(() => {
    const unHidableFilters = filters.filter((it) => !it.hidable)
    const visibleFilters = filters.filter((it) => visibleFiltersState[it.id] && it.hidable)
    return [...unHidableFilters, ...visibleFilters]
  }, [filters, visibleFiltersState])
}
