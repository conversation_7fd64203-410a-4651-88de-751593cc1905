import { RowData } from '@tanstack/react-table'

import { Any } from '@repo/types'

import { BaseTableColumn } from '../types'
import { BaseFilterProps } from './types'

export function getFiltersInColumns<TData extends RowData = Any, TValue = Any>(columns: BaseTableColumn<TData, TValue>[]) {
  const filters: BaseFilterProps<TData, TValue>[] = []

  for (let i = 0; i < columns.length; i++) {
    const column = columns[i]!
    const { filter } = column
    const hidable = filter?.hidable ?? true
    if (filter) {
      filters.push({
        title: column.title,
        type: 'text',
        sort: 0,
        hidable: hidable,
        ...filter,
        id: filter.id ?? column.id,
        column,
      })
    }
  }

  return filters.sort((a, b) => b.sort - a.sort)
}
