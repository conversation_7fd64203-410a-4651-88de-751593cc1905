'use client'

import { RowData } from '@tanstack/react-table'
import React from 'react'
import { BaseFilterContextValue, BaseTableFilterContext } from './base-filter-context'

export interface BaseFilterProviderProps<TData extends RowData = unknown, TValue = unknown>
  extends BaseFilterContextValue<TData, TValue> {
  children: React.ReactNode
}

export function BaseTableFilterProvider<TData extends RowData = unknown, TValue = unknown>(
  props: BaseFilterProviderProps<TData, TValue>
) {
  const { children, ...rest } = props
  return <BaseTableFilterContext.Provider value={rest}>{children}</BaseTableFilterContext.Provider>
}

export const useBaseTableFilter = () => {
  const context = React.useContext(BaseTableFilterContext)
  if (!context) {
    throw new Error('useBaseTableFilter must be used within a BaseTableFilterProvider')
  }
  return context
}
