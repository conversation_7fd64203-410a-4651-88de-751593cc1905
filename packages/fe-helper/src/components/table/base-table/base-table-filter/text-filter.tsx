'use client'

import { useDebounceFn } from 'ahooks'
import { CircleX, Search } from 'lucide-react'
import { useEffect, useState } from 'react'

import { Input } from '@repo/design-system/components/ui/input'

import { FilterContainer } from './filter-container'
import { CommonFilterProps } from './types'

export interface TextFilterProps extends CommonFilterProps {
  value?: string
  onChange?: (value: string) => void
}

export function TextFilter(props: TextFilterProps) {
  const [value, setValue] = useState<string>(() => props.value || '')

  const onPropsChange = useDebounceFn((value: string) => {
    props.onChange?.(value)
  })

  const onChange = (val: string) => {
    setValue(val)
    onPropsChange.run(val)
  }

  const onEnter = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      e.stopPropagation()
      onPropsChange.flush()
    }
  }

  useEffect(() => {
    setValue(props.value || '')
  }, [props.value])

  return (
    <FilterContainer className="bg-transparent" id={props.id} hidable={props.hidable}>
      <div className="group relative [&:hover>span:last-child]:opacity-100">
        <span className="absolute top-1/2 left-0 inline-flex -translate-y-1/2 p-0 text-black/30 transition-opacity">
          <Search size={12} />
        </span>
        <Input
          type="text"
          placeholder={props.title}
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          onKeyDown={onEnter}
          className="h-7 border-none pr-4 pl-5 shadow-none ring-0 focus-visible:ring-0"
          autoComplete="off"
          aria-autocomplete="none"
        />
        {value && (
          <span
            role="button"
            className="hover:text-primary absolute top-1/2 right-1 inline-flex -translate-y-1/2 cursor-pointer p-0 text-black/30 opacity-0 transition-opacity"
            onClick={() => {
              onPropsChange.cancel()
              onChange('')
              props.onChange?.('')
            }}
          >
            <CircleX size={12} />
          </span>
        )}
      </div>
    </FilterContainer>
  )
}
