'use client'

import { RowData, Table } from '@tanstack/react-table'
import { isFunction, objectify } from 'radash'
import { useCallback, useMemo } from 'react'

import { Any } from '@repo/types'

import { DateFilter } from './date-filter'
import { DateRangeFilter } from './date-range-filter'
import { DateTimeFilter } from './datetime-filter'
import { MultipleSelectFilter } from './multiple-select-filter'
import { NumberFilter } from './number-filter'
import { SelectFilter } from './select-filter'
import { TextFilter } from './text-filter'
import { BaseFilterProps } from './types'

export interface FiltersComponentRenderProps<TData extends RowData = Any> {
  table: Table<TData>
  filter: BaseFilterProps<TData>
}

const filterComponents = {
  text: TextFilter,
  number: NumberFilter,
  select: SelectFilter,
  'multiple-select': MultipleSelectFilter,
  date: DateFilter,
  datetime: DateTimeFilter,
  'date-range': DateRangeFilter,
}

export function FiltersComponentRender<TData extends RowData = unknown>(props: FiltersComponentRenderProps<TData>) {
  const { table, filter } = props

  const columnFilters = table.getState().columnFilters

  const filterValues = useMemo(() => {
    return objectify(
      columnFilters,
      (it) => it.id,
      (it) => it.value
    )
  }, [columnFilters])

  const value = filterValues[filter.id]

  const onChange = useCallback(
    (v: unknown) => {
      const originalFilter = table.getState().columnFilters.find((it) => it.id === filter.id)

      if (!originalFilter) {
        table.setColumnFilters((st) => [...st, { id: filter.id, value: v }])
        return
      }
      table.setColumnFilters((st) => {
        return st.map((it) => {
          if (it.id === originalFilter.id) return { ...it, value: v }
          return it
        })
      })
    },
    [filter.id, table]
  )

  const filterProps = isFunction(filter.props) ? filter.props(filterValues, filter.column) : filter.props

  if (filter.render) {
    return <div key={filter.id}>{filter.render(filterValues, filter.column)}</div>
  }

  const Comp = filterComponents[filter.type]

  if (Comp) {
    return (
      <Comp
        key={filter.id}
        id={filter.id}
        title={filter.title}
        hidable={filter.hidable}
        value={value as never}
        onChange={onChange}
        {...filterProps}
      />
    )
  }

  return (
    <TextFilter
      key={filter.id}
      id={filter.id}
      title={filter.title}
      hidable={filter.hidable}
      value={value as never}
      onChange={onChange}
      {...filterProps}
    />
  )
}
