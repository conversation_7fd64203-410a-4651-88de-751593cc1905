import { RowData, Table } from '@tanstack/react-table'
import { Pagination } from 'antd'
import { useCallback, useMemo, useState } from 'react'

import { Separator } from '@repo/design-system/components/ui/separator'
import { Any } from '@repo/types'

import { BaseTableColumn } from '../types'
import { AddFilterTrigger } from './add-filter-trigger'
import { BaseTableFilterProvider } from './base-filter-provider'
import { FiltersComponentRender } from './filter-component-render'
import { useVisibleFilters } from './hooks'
import { getFiltersInColumns } from './utils'

export interface BaseTableFilterProps<TData extends RowData = Any> {
  table: Table<TData>
  filterColumns: BaseTableColumn<TData, Any>[]
}

export function BaseTableFilter<TData extends RowData = Any>(props: BaseTableFilterProps<TData>) {
  const { table, filterColumns } = props
  const pagination = table.getState().pagination

  const allFilters = useMemo(() => getFiltersInColumns(filterColumns), [filterColumns])
  const [visibleFiltersState, setVisibleFiltersState] = useState<Record<string, boolean>>(() => {
    return allFilters.reduce<Record<string, boolean>>((pre, cur) => {
      if (!cur.hidable) {
        pre[cur.id] = true
      }
      return pre
    }, {})
  })

  const visibleFilters = useVisibleFilters({ filters: allFilters, visibleFiltersState })

  const setVisibleFilter = useCallback(
    (id: string, visible: boolean) => {
      if (!visible) {
        table.setColumnFilters((st) => st.filter((it) => it.id !== id))
      }
      setVisibleFiltersState((prev) => ({ ...prev, [id]: visible }))
    },
    [table]
  )

  return (
    <BaseTableFilterProvider
      table={table}
      setVisibleFilter={setVisibleFilter}
      visibleFiltersState={visibleFiltersState}
      setVisibleFiltersState={setVisibleFiltersState}
      filters={allFilters}
    >
      <div className="relative flex flex-wrap items-center gap-2">
        {visibleFilters.map((vf) => (
          <FiltersComponentRender key={vf.id} table={table} filter={vf} />
        ))}
        <AddFilterTrigger table={table} />
        <Separator orientation="vertical" className="data-[orientation=vertical]:h-3.5 max-xl:hidden" />
        <Pagination
          simple
          className="max-xl:hidden"
          size="small"
          showSizeChanger={false}
          showTotal={(total) => `共 ${total} 条`}
          pageSize={pagination.pageSize}
          current={pagination.pageIndex + 1}
          total={table.getTotalSize()}
          onChange={(current, size) => {
            table.setRowSelection({})
            table.setPagination({ pageIndex: current - 1, pageSize: size })
          }}
        />
      </div>
    </BaseTableFilterProvider>
  )
}
