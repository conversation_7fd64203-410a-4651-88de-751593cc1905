'use client'

import { RowData, Table } from '@tanstack/react-table'
import { Plus, Save, Trash2 } from 'lucide-react'
import { objectify } from 'radash'
import { useState } from 'react'

import { Button } from '@repo/design-system/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@repo/design-system/components/ui/dropdown-menu'
import { cn } from '@repo/design-system/lib/utils'

import { useBaseTableFilter } from './base-filter-provider'

export interface AddFilterTriggerProps<TData extends RowData = unknown> {
  table: Table<TData>
}

export function AddFilterTrigger<TData extends RowData = unknown>(props: AddFilterTriggerProps<TData>) {
  const { table } = props

  const [open, setOpen] = useState(false)
  const { filters, visibleFiltersState, setVisibleFilter, setVisibleFiltersState } = useBaseTableFilter()

  const selectedFilters = filters.filter((it) => visibleFiltersState[it.id])

  const selectableFilters = filters.filter((it) => !visibleFiltersState[it.id])

  const unHidableFilters = filters.filter((it) => !it.hidable)

  const columnFilters = table.getState().columnFilters

  const explicitFilters = columnFilters.filter((it) => filters.find((f) => f.id === it.id))

  const shouldShowAdd = !!selectableFilters.length
  const shouldShowClear = !!explicitFilters.length || selectedFilters.length > unHidableFilters.length

  const onClear = () => {
    const nextState = objectify(
      unHidableFilters,
      (it) => it.id,
      () => true
    )
    setVisibleFiltersState(nextState)

    table.setColumnFilters((st) => st.filter((it) => !filters.find((f) => f.id === it.id)))
  }

  if (!shouldShowAdd && !shouldShowClear) {
    return null
  }

  return (
    <div className="flex items-center gap-2">
      {shouldShowAdd && (
        <DropdownMenu open={open} onOpenChange={setOpen}>
          <DropdownMenuTrigger asChild>
            <Button
              type="button"
              variant="outline"
              size="sm"
              className={cn(
                'bg-accent h-8 w-8 p-0 text-xs font-normal text-black/30 shadow-none',
                'hover:border-primary hover:bg-primary-foreground hover:text-primary',
                open && 'border-primary bg-primary-foreground text-primary'
              )}
              aria-label="添加筛选"
              title="添加筛选"
            >
              <Plus />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {selectableFilters.map((it) => (
              <DropdownMenuItem className="hover:!text-primary" key={it.id} onClick={() => setVisibleFilter(it.id, true)}>
                {it.title}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      )}

      <Button
        type="button"
        variant="outline"
        size="sm"
        className={cn(
          'hidden',
          'bg-accent h-8 w-8 p-0 text-xs font-normal text-black/30 shadow-none',
          'hover:border-primary hover:bg-primary-foreground hover:text-primary',
          'focus:border-primary focus:bg-primary-foreground focus:text-primary'
        )}
        aria-label="保存筛选"
        title="保存筛选"
      >
        <Save />
      </Button>

      {shouldShowClear && (
        <Button
          type="button"
          variant="outline"
          size="sm"
          className={cn(
            'bg-accent h-8 w-8 p-0 text-xs font-normal text-black/30 shadow-none',
            'hover:border-primary hover:bg-primary-foreground hover:text-primary',
            'focus:border-primary focus:bg-primary-foreground focus:text-primary'
          )}
          aria-label="清除筛选"
          title="清除筛选"
          onClick={onClear}
        >
          <Trash2 />
        </Button>
      )}
    </div>
  )
}
