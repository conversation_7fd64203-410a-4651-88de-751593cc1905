import {
  ColumnFiltersState,
  ColumnOrderState,
  ColumnPinningState,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getSortedRowModel,
  PaginationState,
  RowData,
  RowSelectionState,
  SortingState,
  TableOptions,
  TableState,
  useReactTable,
  VisibilityState,
} from '@tanstack/react-table'
import { useControllableValue } from 'ahooks'
import { listify, objectify } from 'radash'
import { useCallback, useMemo } from 'react'

import { DEFAULT_COLUMN_PROPS, DEFAULT_PAGE_SIZE_OPTIONS, ROW_SELECTION_COLUMN, ROW_SELECTION_COLUMN_ID } from './constants'
import { BaseTableColumn, BaseTableProps } from './types'

export function usePreprocessColumns<TData extends RowData = unknown, TValue = unknown>(
  props: BaseTableProps<TData, TValue>
): BaseTableColumn<TData, TValue>[] {
  const { columns, rowSelection } = props
  const rowSelectionEnabled = rowSelection ? (rowSelection.enabled ?? true) : false

  // 自动列宽？
  return useMemo(() => {
    const finalColumns = [...columns]

    if (rowSelectionEnabled) {
      finalColumns.unshift(ROW_SELECTION_COLUMN)
    }

    return finalColumns
      .map((column) => {
        return {
          ...DEFAULT_COLUMN_PROPS,
          accessorKey: column.id,
          header: column.title,
          ...column,
        }
      })
      .filter((col) => !col.hiddenInColumn)
  }, [columns, rowSelectionEnabled])
}

export function useFilterColumns<TData extends RowData = unknown, TValue = unknown>(
  props: BaseTableProps<TData, TValue>
): BaseTableColumn<TData, TValue>[] {
  const { columns } = props

  return useMemo(() => {
    const finalColumns = [...columns].map((column) => {
      return {
        ...DEFAULT_COLUMN_PROPS,
        accessorKey: column.id,
        header: column.title,
        ...column,
      }
    })
    return finalColumns.filter((col) => col.filter)
  }, [columns])
}

export function useRowSelection<TData extends RowData = unknown, TValue = unknown>(
  rowSelection: BaseTableProps<TData, TValue>['rowSelection']
) {
  const { type = 'checkbox', selectedRowKeys, defaultSelectedRowKeys, onChange } = rowSelection || {}

  const enabled = rowSelection ? (rowSelection.enabled ?? true) : false

  const selectedRowKeyMap = useMemo(() => {
    if (!selectedRowKeys) return
    return objectify(
      selectedRowKeys,
      (key) => key,
      () => true
    )
  }, [selectedRowKeys])

  const defaultSelectedRowKeyMap = useMemo(() => {
    if (!defaultSelectedRowKeys) return
    return objectify(
      defaultSelectedRowKeys,
      (key) => key,
      () => true
    )
  }, [defaultSelectedRowKeys])

  const handleChange = useCallback(
    (rowKeyMap: Record<string, boolean>) => {
      const nextSelectedRowKeys: string[] = listify(rowKeyMap, (key, value) => (value ? key : '')).filter(Boolean)
      onChange?.(nextSelectedRowKeys)
    },
    [onChange]
  )

  const controllableProps: {
    value?: RowSelectionState
    defaultValue?: RowSelectionState
    onChange?: (value: RowSelectionState) => void
  } = { onChange: handleChange }

  if (defaultSelectedRowKeyMap) {
    controllableProps.defaultValue = defaultSelectedRowKeyMap
  }

  if (selectedRowKeyMap) {
    controllableProps.value = selectedRowKeyMap
  }

  const [state, setState] = useControllableValue<RowSelectionState>(controllableProps, { defaultValue: [] as never })

  return { state, setState, enabled, type }
}

export function usePagination<TData extends RowData = unknown, TValue = unknown>(
  pagination: BaseTableProps<TData, TValue>['pagination'] = {}
) {
  const {
    total,
    defaultCurrent = 1,
    defaultPageSize = 10,
    pageSize = 10,
    current = 1,
    pageSizeOptions = DEFAULT_PAGE_SIZE_OPTIONS,
    onChange,
  } = pagination

  const pageIndex = current >= 1 ? current - 1 : current
  const defaultPageIndex = defaultCurrent >= 1 ? defaultCurrent - 1 : defaultCurrent

  const handleChange = useCallback(
    (pagination: { pageIndex: number; pageSize: number }) => {
      onChange?.({ current: pagination.pageIndex + 1, pageSize: pagination.pageSize })
    },
    [onChange]
  )

  const controllableProps = {
    value: { pageIndex, pageSize },
    onChange: handleChange,
    defaultValue: { pageIndex: defaultPageIndex, pageSize: defaultPageSize },
  }

  const [state, setState] = useControllableValue<PaginationState>(controllableProps, {
    defaultValue: { pageIndex: defaultPageIndex, pageSize: defaultPageSize },
  })
  return { manualPagination: true, total, pageSize, current, pageSizeOptions, state, setState }
}

export function useColumnPinning<TData extends RowData = unknown, TValue = unknown>(
  props: Pick<BaseTableProps<TData, TValue>, 'columnPinning' | 'columns'>
) {
  const { columns, columnPinning: columnPinningProps } = props

  const defaultPinning = useMemo(() => {
    if (columnPinningProps?.defaultValue) {
      return columnPinningProps.defaultValue
    }
    const left: string[] = []
    const right: string[] = []

    columns.map((column) => {
      if (column.fixed === 'left' && column.id) {
        if (column.id === ROW_SELECTION_COLUMN_ID) {
          left.unshift(column.id)
        } else {
          left.push(column.id)
        }
      } else if (column.fixed === 'right' && column.id) {
        right.push(column.id)
      }
    })
    return { left, right }
  }, [columns, columnPinningProps])

  const [state = { left: [], right: [] }, setState] = useControllableValue<ColumnPinningState>(columnPinningProps, {
    defaultValue: defaultPinning,
  })

  return { state, setState, enabled: columnPinningProps?.enabled ?? true }
}

export function useSorting<TData extends RowData = unknown, TValue = unknown>(
  props: Pick<BaseTableProps<TData, TValue>, 'sorting' | 'columns'>
) {
  const { sorting: sortingProps } = props

  const [state, setState] = useControllableValue<SortingState>(sortingProps)

  return {
    state,
    setState,
    enabled: sortingProps?.enabled ?? true,
  }
}

export function useColumnOrder<TData extends RowData = unknown, TValue = unknown>(
  props: BaseTableProps<TData, TValue>['columnOrder'] = {}
) {
  const [state, setState] = useControllableValue<ColumnOrderState>(props)
  return { state, setState, enabled: props.enabled ?? true }
}

export function useColumnResizing<TData extends RowData = unknown, TValue = unknown>(
  resizing: BaseTableProps<TData, TValue>['resizing'] = {}
) {
  const { mode = 'onChange', enabled = true, onChange } = resizing
  return { enabled, mode, onChange }
}

export function useColumnVisible<TData extends RowData = unknown, TValue = unknown>(
  props: Pick<BaseTableProps<TData, TValue>, 'columns' | 'columnVisibility'>
) {
  const { columns, columnVisibility: columnVisibilityProps = {} } = props

  const defaultColumnVisibility = useMemo(() => {
    if (columnVisibilityProps?.defaultValue) {
      return columnVisibilityProps.defaultValue
    }
    const nextColumnVisibility = columns.reduce(
      (acc, col: BaseTableColumn<TData, TValue>) => {
        if (typeof col.visible === 'boolean') {
          acc[col.id] = col.visible
        } else if (!col.visible) {
          acc[col.id] = true
        }
        return acc
      },
      {} as Record<string, boolean>
    )
    return nextColumnVisibility
  }, [columns, columnVisibilityProps])

  const [state, setState] = useControllableValue<VisibilityState>(columnVisibilityProps, {
    defaultValue: defaultColumnVisibility,
  })

  return {
    enabled: columnVisibilityProps?.enabled ?? true,
    state,
    setState,
  }
}

export function useColumnFilters<TData extends RowData = unknown, TValue = unknown>(
  props: Pick<BaseTableProps<TData, TValue>, 'columns' | 'filtering'>
) {
  const { filtering: filteringProps = {} } = props

  const { enabled = true, value: filteringValue, defaultValue, onChange: onFilteringChange } = filteringProps

  const defaultColumnFilters = useMemo(() => {
    return listify(defaultValue || {}, (id, value) => ({ id, value }))
  }, [defaultValue])

  const value = useMemo(() => {
    return listify(filteringValue || {}, (id, value) => ({ id, value }))
  }, [filteringValue])

  const onChange = useCallback(
    (value: ColumnFiltersState) => {
      const nextValue = objectify(
        value,
        (it) => it.id,
        (it) => it.value
      )
      onFilteringChange?.(nextValue)
    },
    [onFilteringChange]
  )

  const controllableProps = { value, defaultValue: defaultColumnFilters, onChange }

  const [state, setState] = useControllableValue<ColumnFiltersState>(controllableProps, { defaultValue: defaultColumnFilters })

  return { enabled, state, setState }
}

export function useTable<TData extends RowData = unknown, TValue = unknown>(props: BaseTableProps<TData, TValue>) {
  const columns = usePreprocessColumns(props)
  const filterColumns = useFilterColumns(props)

  const pagination = usePagination(props.pagination)
  const rowSelection = useRowSelection(props.rowSelection)
  const columnOrder = useColumnOrder(props.columnOrder)
  const resizing = useColumnResizing(props.resizing)
  const columnVisibility = useColumnVisible({ columns, columnVisibility: props.columnVisibility })
  const sorting = useSorting({ columns, sorting: props.sorting })
  const columnPinning = useColumnPinning({ columns, columnPinning: props.columnPinning })
  const columnFilters = useColumnFilters({ columns, filtering: props.filtering })

  const onPaginationChange = pagination.setState
  const onRowSelectionChange = rowSelection.setState
  const onColumnVisibilityChange = columnVisibility.setState
  const onSortingChange = sorting.setState
  const onColumnOrderChange = columnOrder.setState
  const onColumnPinningChange = columnPinning.setState
  const onColumnFiltersChange = columnFilters.setState

  const total = props.pagination?.total ?? props.data.length

  const tableState: Partial<TableState> = {
    pagination: pagination.state,

    rowSelection: rowSelection.state,
    columnVisibility: columnVisibility.state,
    sorting: sorting.state,
    columnPinning: columnPinning.state,
    columnFilters: columnFilters.state,
    columnOrder: columnOrder.state,
  }

  const tableOptions: TableOptions<TData> = {
    data: props.data,
    columns,
    rowCount: total,

    state: tableState,

    columnResizeMode: resizing.mode,

    manualPagination: true,
    manualFiltering: true,
    manualSorting: true,

    enableColumnResizing: resizing.enabled,
    enableColumnFilters: columnFilters.enabled,
    enableColumnPinning: columnPinning.enabled,
    enableSorting: sorting.enabled,
    enableRowSelection: rowSelection.enabled,
    enableHiding: columnVisibility.enabled,

    onColumnVisibilityChange,
    onSortingChange,
    onColumnOrderChange,
    onColumnPinningChange,
    onColumnFiltersChange,
    onPaginationChange,
    onRowSelectionChange,

    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),

    getRowId: (row, index, parent) => {
      const rowKey = props.rowKey || 'id'
      if (typeof rowKey === 'function') {
        return rowKey(row, index, parent)
      }
      return row[rowKey as never] ?? index
    },
  }

  const table = useReactTable(tableOptions)

  return {
    table,
    pagination,
    rowSelection,
    columnOrder,
    resizing,
    columnVisibility,
    sorting,
    columnPinning,
    columnFilters,
    filterColumns,
  }
}
