'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import clsx from 'clsx'
import { motion } from 'framer-motion'
import { ArrowRight } from 'lucide-react'
import React, { Fragment, useState } from 'react'
import { useForm } from 'react-hook-form'
import z from 'zod'

import { Button } from '@repo/design-system/components/ui/button'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@repo/design-system/components/ui/form'
import { Input } from '@repo/design-system/components/ui/input'
import { cn } from '@repo/design-system/lib/utils'

import { DotMap } from './dot-map'

export interface LoginProvider {
  providerId: string
  name: string
  title?: string
  logo?: React.ReactNode
  onClick: () => void
}

export interface SignInCardProps {
  logo?: React.ReactNode
  title?: string
  description?: string
  onLogin: (data: { account: string; password: string }) => void
  providers?: LoginProvider[]
}

const LoginFormSchema = z.object({
  account: z.string().trim().min(1, { message: 'Account is required' }),
  password: z.string().min(8, { message: 'Password length must be at least 8 characters' }),
})

export function SignInCard(props: SignInCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  const form = useForm<z.infer<typeof LoginFormSchema>>({ resolver: zodResolver(LoginFormSchema) })

  return (
    <div className="flex h-full w-full items-center justify-center">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="flex w-full max-w-4xl overflow-hidden rounded-2xl bg-white shadow-xl"
      >
        {/* Left side - Map */}
        <div className="relative hidden h-[600px] w-1/2 overflow-hidden border-r border-gray-100 md:block">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-100">
            <DotMap />

            {/* Logo and text overlay */}
            <div className="absolute inset-0 z-10 flex flex-col items-center justify-center p-8">
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6, duration: 0.5 }}
                className="mb-6"
              >
                {props.logo || (
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 shadow-lg shadow-blue-200">
                    <span className="h-6 w-6 text-center text-2xl leading-6 font-bold text-white">C</span>{' '}
                  </div>
                )}
              </motion.div>
              <motion.h2
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7, duration: 0.5 }}
                className="mb-2 bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-center text-3xl font-bold text-transparent"
              >
                {props.title || 'System Powered By CVTE'}
              </motion.h2>
              <motion.p
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8, duration: 0.5 }}
                className="max-w-xs text-center text-sm text-gray-600"
              >
                {props.description || 'Sign in to access your global system dashboard'}
              </motion.p>
            </div>
          </div>
        </div>

        {/* Right side - Sign In Form */}
        <div className="flex w-full flex-col justify-center bg-white p-8 md:w-1/2 md:p-10">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
            <h1 className="mb-1 text-2xl font-bold text-gray-800 md:text-3xl">Welcome back</h1>
            <p className="mb-8 text-gray-500">Sign in to your account</p>

            {!!props.providers?.length && (
              <Fragment>
                <div className="mb-6 flex flex-col gap-2">
                  {props.providers.slice(0, 3).map((provider) => (
                    <button
                      key={provider.providerId}
                      className="flex w-full cursor-pointer items-center justify-center gap-2 rounded-lg border border-gray-200 bg-gray-50 p-3 text-gray-700 shadow-sm transition-all duration-300 hover:bg-gray-100"
                      onClick={provider.onClick}
                    >
                      {provider.logo ?? (
                        <span className="h-5 w-5 rounded-4xl bg-amber-600 text-center leading-5 text-white">
                          {provider.name.at(0)}
                        </span>
                      )}
                      <span>{provider.title ?? `Login with ${provider.name}`}</span>
                    </button>
                  ))}
                </div>

                <div className="relative my-6">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-200"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="bg-white px-2 text-gray-500">or</span>
                  </div>
                </div>
              </Fragment>
            )}

            <Form {...form}>
              <form className="space-y-5" onSubmit={form.handleSubmit(props.onLogin)}>
                <FormField
                  control={form.control}
                  name="account"
                  render={({ field }) => (
                    <FormItem>
                      <div className="group relative">
                        <FormLabel
                          className={clsx(
                            'origin-start text-muted-foreground/70 absolute top-1/2 block -translate-y-1/2 cursor-text px-1 text-sm transition-all',
                            'group-focus-within:text-foreground group-focus-within:pointer-events-none group-focus-within:top-0 group-focus-within:cursor-default group-focus-within:text-xs group-focus-within:font-medium',
                            'has-[+input:not(:placeholder-shown)]:text-foreground has-[+input:not(:placeholder-shown)]:pointer-events-none has-[+input:not(:placeholder-shown)]:top-0 has-[+input:not(:placeholder-shown)]:cursor-default has-[+input:not(:placeholder-shown)]:text-xs has-[+input:not(:placeholder-shown)]:font-medium'
                          )}
                        >
                          <span className="bg-background inline-flex px-2">Account</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            defaultValue=""
                            className="h-12"
                            placeholder=" "
                            {...field}
                            autoComplete="off"
                            aria-autocomplete="none"
                          />
                        </FormControl>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <div className="group relative">
                        <FormLabel
                          className={clsx(
                            'origin-start text-muted-foreground/70 absolute top-1/2 block -translate-y-1/2 cursor-text px-1 text-sm transition-all',
                            'group-focus-within:text-foreground group-focus-within:pointer-events-none group-focus-within:top-0 group-focus-within:cursor-default group-focus-within:text-xs group-focus-within:font-medium',
                            'has-[+input:not(:placeholder-shown)]:text-foreground has-[+input:not(:placeholder-shown)]:pointer-events-none has-[+input:not(:placeholder-shown)]:top-0 has-[+input:not(:placeholder-shown)]:cursor-default has-[+input:not(:placeholder-shown)]:text-xs has-[+input:not(:placeholder-shown)]:font-medium'
                          )}
                        >
                          <span className="bg-background inline-flex px-2">Password</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            className="h-12"
                            placeholder=" "
                            defaultValue=""
                            {...field}
                            type="password"
                            autoComplete="off"
                            aria-autocomplete="none"
                          />
                        </FormControl>
                      </div>

                      <FormMessage />
                    </FormItem>
                  )}
                />
                <motion.div
                  whileHover={{ scale: 1.01 }}
                  whileTap={{ scale: 0.98 }}
                  onHoverStart={() => setIsHovered(true)}
                  onHoverEnd={() => setIsHovered(false)}
                  className="pt-2"
                >
                  <Button
                    type="submit"
                    className={cn(
                      'relative w-full overflow-hidden rounded-lg bg-gradient-to-r from-blue-500 to-indigo-600 py-2 text-white transition-all duration-300 hover:from-blue-600 hover:to-indigo-700',
                      isHovered ? 'shadow-lg shadow-blue-200' : ''
                    )}
                  >
                    <span className="flex items-center justify-center">
                      Sign in
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </span>
                    {isHovered && (
                      <motion.span
                        initial={{ left: '-100%' }}
                        animate={{ left: '100%' }}
                        transition={{ duration: 1, ease: 'easeInOut' }}
                        className="absolute top-0 bottom-0 left-0 w-20 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                        style={{ filter: 'blur(8px)' }}
                      />
                    )}
                  </Button>
                </motion.div>

                {/* <div className="mt-6 text-center">
                <a href="#" className="text-sm text-blue-600 transition-colors hover:text-blue-700">
                  Forgot password?
                </a>
              </div> */}
              </form>
            </Form>
          </motion.div>
        </div>
      </motion.div>
    </div>
  )
}
