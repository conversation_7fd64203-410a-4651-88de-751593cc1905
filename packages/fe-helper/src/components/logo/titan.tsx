import type { SVGProps } from 'react'

import { cn } from '@repo/design-system/lib/utils'

export function Titan(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 64 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
      className={cn('text-[#545C73]', props.className)}
    >
      <title>DevOps Next</title>
      <g opacity="0.9">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10.4265 9.30247C10.4265 7.51173 11.8981 6.06006 13.7134 6.06006H15.5879C17.4032 6.06006 18.8748 7.51173 18.8748 9.30247V10.0318C18.8748 11.4668 17.6955 12.6301 16.2408 12.6301H13.0831V13.959H18.3583C18.6049 13.959 18.8047 14.1589 18.8047 14.4055V16.1332C18.8047 16.3798 18.6049 16.5796 18.3583 16.5796H13.0605C11.6058 16.5796 10.4265 15.4163 10.4265 13.9813V9.30247ZM13.0831 10.0095H16.2183V9.30247C16.2183 8.95905 15.9361 8.68065 15.5879 8.68065H13.7134C13.3653 8.68065 13.0831 8.95905 13.0831 9.30247V10.0095Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M47.9534 5.61694H45.2969V8.29549V13.6898V16.3683V21.5536C45.2969 21.8001 45.4967 22 45.7433 22H47.507C47.7536 22 47.9534 21.8001 47.9534 21.5536V16.3683H47.9754H50.7674C52.5852 16.3683 54.0588 14.8947 54.0588 13.0769V8.90834C54.0588 7.09055 52.5852 5.61694 50.7674 5.61694H47.9754H47.9534ZM47.9754 8.29549H50.7674C51.1059 8.29549 51.3802 8.56987 51.3802 8.90834V13.0769C51.3802 13.4154 51.1059 13.6898 50.7674 13.6898H47.9754V8.29549Z"
          fill="currentColor"
        />
        <g>
          <g>
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M58.2087 9.49952L59.2565 9.49952L59.2565 12.1201L58.1862 12.1201C56.7314 12.1201 55.5521 10.9568 55.5521 9.52174L55.5521 8.14841C55.5521 6.71337 56.7314 5.55004 58.1862 5.55004L63.1168 5.55004C63.3634 5.55004 63.5632 5.74991 63.5632 5.99646L63.5632 7.72421C63.5632 7.97076 63.3634 8.17063 63.1168 8.17063L58.2087 8.17063L58.2087 9.49952Z"
              fill="currentColor"
            />
          </g>
          <g>
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M61.3439 12.1201L58.9595 12.1201L58.9595 9.49951L61.3665 9.49951C62.8212 9.49951 64.0005 10.6628 64.0005 12.0979L64.0005 13.4712C64.0005 14.9063 62.8212 16.0696 61.3665 16.0696L56.1388 16.0696C55.8923 16.0696 55.6924 15.8697 55.6924 15.6232L55.6924 13.8954C55.6924 13.6489 55.8923 13.449 56.1388 13.449L61.3439 13.449L61.3439 12.1201Z"
              fill="currentColor"
            />
          </g>
        </g>
        <g>
          <path
            d="M26.1264 16.9729C25.9355 16.9275 25.7373 17.0121 25.6381 17.1814L24.7619 18.676C24.6118 18.932 24.7486 19.2604 25.0359 19.3342L35.0888 21.9166C35.2806 21.9659 35.4819 21.883 35.5833 21.7129L36.6205 19.9736C36.7248 19.7987 36.6975 19.5753 36.554 19.4306L29.2985 12.1162C29.092 11.908 28.7443 11.952 28.5962 12.2051L27.7532 13.6456C27.6521 13.8183 27.6783 14.0372 27.8173 14.1812L31.8205 18.3264L26.1264 16.9729Z"
            fill="currentColor"
          />
          <path
            d="M28.2664 5.38502C28.3398 5.26342 28.4714 5.18909 28.6134 5.18909L30.7185 5.18909C31.038 5.18909 31.2319 5.54168 31.0607 5.81151L24.2019 16.6208C24.1275 16.738 23.9984 16.809 23.8597 16.809H22.0924C21.777 16.809 21.5824 16.4645 21.7454 16.1944L28.2664 5.38502Z"
            fill="currentColor"
            stroke="currentColor"
            strokeWidth="0.0822849"
          />
          <path
            d="M26.1407 5.0269C26.3316 5.07229 26.5298 4.98766 26.629 4.81836L27.5052 3.32374C27.6553 3.0678 27.5185 2.7394 27.2312 2.66558L17.1783 0.0831061C16.9865 0.0338402 16.7852 0.116785 16.6838 0.286848L15.6466 2.02611C15.5423 2.20108 15.5696 2.4245 15.7131 2.56914L22.9686 9.88357C23.1751 10.0917 23.5228 10.0477 23.6709 9.79467L24.5139 8.35417C24.615 8.18143 24.5888 7.96252 24.4498 7.81855L20.4466 3.67331L26.1407 5.0269Z"
            fill="currentColor"
          />
        </g>
        <rect
          x="36.2763"
          y="2.49699"
          width="6.08749"
          height="13.0666"
          rx="2.08926"
          stroke="currentColor"
          strokeWidth="2.67854"
        />
        <path
          d="M1.33927 2.49699H5.3375C6.49137 2.49699 7.42676 3.43238 7.42676 4.58625V13.4743C7.42676 14.6282 6.49137 15.5636 5.3375 15.5636H1.33927V2.49699Z"
          stroke="currentColor"
          strokeWidth="2.67854"
        />
      </g>
    </svg>
  )
}
