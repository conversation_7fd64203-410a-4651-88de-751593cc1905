import { useId } from 'react'

import { Input } from '@repo/design-system/components/ui/input'
import { cn } from '@repo/design-system/lib/utils'

export interface LightLabelInputProps extends React.ComponentProps<'input'> {
  id?: string
  label: React.ReactNode
  placeholder?: string
  classNames?: {
    root?: string
    input?: string
    label?: string
  }
  extra?: React.ReactNode
}

export function LightLabelInput(props: LightLabelInputProps) {
  const { id, label, placeholder, type = 'text', classNames, className, extra, ...rest } = props

  const selfId = useId()

  const compId = id ?? selfId

  return (
    <div className={cn('group relative', classNames?.root)}>
      <label
        htmlFor={compId}
        className={cn(
          'origin-start text-muted-foreground/70 absolute top-1/2 block -translate-y-1/2 cursor-text px-1 text-sm transition-all',
          'group-focus-within:text-foreground group-focus-within:pointer-events-none group-focus-within:top-0 group-focus-within:cursor-default group-focus-within:text-xs group-focus-within:font-medium',
          'has-[+input:not(:placeholder-shown)]:text-foreground has-[+input:not(:placeholder-shown)]:pointer-events-none has-[+input:not(:placeholder-shown)]:top-0 has-[+input:not(:placeholder-shown)]:cursor-default has-[+input:not(:placeholder-shown)]:text-xs has-[+input:not(:placeholder-shown)]:font-medium',
          classNames?.label
        )}
      >
        <span className="bg-background inline-flex px-2">{label}</span>
      </label>
      <Input id={compId} type={type} placeholder={placeholder} className={cn(className, classNames?.input)} {...rest} />
      {extra}
    </div>
  )
}
