import { CircleHelp, LucideProps } from 'lucide-react'
import { cloneElement, ReactElement, ReactNode } from 'react'

import { Any } from '@repo/types'

export function AwesomeIcon(props: AwesomeIconProps) {
  const { icon, fallback, ...rest } = props

  if (!icon) {
    return fallback ?? <CircleHelp />
  }

  if (typeof icon === 'string') {
    // oxlint-disable-next-line no-img-element
    return <img {...rest} src={icon} />
  }

  return cloneElement(icon, rest)
}

export interface AwesomeIconProps {
  icon?: ReactElement<Any> | ReactElement<LucideProps> | string
  fallback?: ReactNode | null
  className?: string
}
