'use client'

import copy from 'copy-to-clipboard'
import { Check, Copy, SquareX } from 'lucide-react'
import { useRef, useState } from 'react'

import { Button } from '@repo/design-system/components/ui/button'
import { cn } from '@repo/design-system/lib/utils'

export interface CopyableToClipboardProps {
  text: string
  debug?: boolean
  message?: string
  format?: string
  className?: string
  onCopy?: (clipboardData: object) => void
  recoveryDelay?: number
}

export function CopyableToClipboard(props: CopyableToClipboardProps) {
  const [success, setSuccess] = useState<'idle' | 'success' | 'fail'>('idle')

  const timer = useRef<NodeJS.Timeout | null>(null)

  const onCopy = () => {
    const { format, message, debug, onCopy, recoveryDelay = 3000 } = props
    const options = { format, message, debug, onCopy }
    const ret = copy(props.text, options)
    setSuccess(ret ? 'success' : 'fail')
    if (timer.current) {
      clearTimeout(timer.current)
      timer.current = null
    }
    timer.current = setTimeout(() => {
      setSuccess('idle')
      timer.current = null
    }, recoveryDelay)
  }

  let icon = <Copy width="1em" height="1em" />

  if (success === 'success') {
    icon = <Check width="1em" height="1em" />
  } else if (success === 'fail') {
    icon = <SquareX width="1em" height="1em" />
  }

  return (
    <Button
      type="button"
      variant="ghost"
      data-state={success}
      size="icon"
      className={cn(
        'inline-flex size-4 h-auto cursor-pointer',
        'data-[state=success]:text-success data-[state=fail]:text-destructive',
        props.className
      )}
      onClick={onCopy}
    >
      {icon}
    </Button>
  )
}
