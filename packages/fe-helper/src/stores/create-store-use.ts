'use client'

import { Context, useContext } from 'react'
import { StoreApi, useStore as useZustandStore } from 'zustand'

export function createStoreUse<T = unknown>(ctx: Context<StoreApi<T> | null>) {
  return function useStore<R = T>(selector?: (state: T) => R) {
    const storeRef = useContext(ctx)

    if (!storeRef) {
      throw new Error(`${ctx.displayName || 'use'}Store must be used within a StoreProvider`)
    }

    return useZustandStore(storeRef, selector!)
  }
}
