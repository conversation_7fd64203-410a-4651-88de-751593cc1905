'use client'

import { Context, useRef } from 'react'
import { StoreApi } from 'zustand'

export interface StoreProviderProps<T = unknown> {
  children: React.ReactNode
  store?: StoreApi<T> | null
  init?: Partial<T>
}

export interface CreateStoreProviderOptions<T = unknown> {
  store?: StoreApi<T> | null
  init?: Partial<T>
  name?: string
}

export function createStoreProvider<T = unknown>(
  StoreContext: Context<StoreApi<T> | null>,
  createStore: <P extends Partial<T>>(init?: P) => StoreApi<T>,
  options?: CreateStoreProviderOptions<T>
) {
  const { store: optStore = null, init: optInit, name } = options || {}

  const StoreProvider: React.FC<StoreProviderProps<T>> = ({ children, store = optStore, init = optInit }) => {
    const storeRef = useRef<StoreApi<T>>(store)

    if (!storeRef.current) {
      storeRef.current = createStore(init)
    }

    return <StoreContext.Provider value={storeRef.current}>{children}</StoreContext.Provider>
  }

  StoreProvider.displayName = name || 'StoreProvider'

  return StoreProvider
}
