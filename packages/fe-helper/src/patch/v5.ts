'use client'

import { unstableSetRender } from 'antd'
import { createRoot } from 'react-dom/client'

type RenderType = Parameters<typeof unstableSetRender>[0]
//@ts-expect-error - no type
type ContainerType = Parameters<RenderType>[1] & {
  _reactRoot?: ReturnType<typeof createRoot>
}

// 参考：https://ant-design.antgroup.com/docs/react/v5-for-19-cn
unstableSetRender((node, container: ContainerType) => {
  container._reactRoot ||= createRoot(container)
  const root: ReturnType<typeof createRoot> = container._reactRoot
  root.render(node)

  return () =>
    new Promise<void>((resolve) => {
      setTimeout(() => {
        root.unmount()
        resolve()
      }, 0)
    })
})
