{"name": "@repo/fe-helper", "version": "0.0.0", "private": true, "license": "ISC", "author": "vic", "exports": {"./providers/*": "./src/providers/*.tsx", "./patch/v5": "./src/patch/v5.ts", "./stores": "./src/stores/index.ts", "./components": "./src/components/index.ts"}, "scripts": {"clean": "rimraf .turbo dist node_modules", "lint": "oxlint . && eslint .", "lint:fix": "prettier --check --write . && oxlint --fix --fix-suggestions . && eslint . --fix"}, "lint-staged": {"*": ["prettier --check --write", "oxlint --fix --fix-suggestions", "eslint --fix"]}, "devDependencies": {"@bprogress/next": "^3.2.12", "@dnd-kit/abstract": "^0.1.18", "@dnd-kit/helpers": "^0.1.18", "@dnd-kit/react": "^0.1.18", "@hookform/resolvers": "^5.0.1", "@repo/design-system": "workspace:*", "@repo/eslint-config": "workspace:*", "@repo/types": "workspace:*", "@repo/typescript-config": "workspace:*", "@tanstack/react-table": "^8.21.3", "@types/node": "^20.11.24", "@types/react": "^19", "@types/react-dom": "^19", "ahooks": "^3.8.5", "antd": "^5.25.4", "antd-style": "^3.7.1", "axios": "^1.9.0", "copy-to-clipboard": "^3.3.3", "lucide-react": "^0.507.0", "next-themes": "^0.4.6", "radash": "^12.1.0", "react": "^19.1.0", "react-ellipsis-component": "^1.1.11", "swr": "^2.3.3", "typescript": "5.5.4", "zod": "^3.24.4", "zustand": "^5.0.5"}, "peerDependencies": {"@bprogress/next": "^3.2.12", "@dnd-kit/abstract": "^0.1.18", "@dnd-kit/helpers": "^0.1.18", "@dnd-kit/react": "^0.1.18", "@hookform/resolvers": "^5.0.1", "@repo/design-system": "workspace:*", "@tanstack/react-table": "^8.21.3", "ahooks": "^3.8.5", "antd": "^5.25.4", "antd-style": "^3.7.1", "axios": "^1.9.0", "copy-to-clipboard": "^3.3.3", "lucide-react": "^0.507.0", "next-themes": "^0.4.6", "radash": "^12.1.0", "react": "^19.1.0", "react-ellipsis-component": "^1.1.11", "swr": "^2.3.3", "zod": "^3.24.4"}}