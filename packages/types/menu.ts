import { ReactElement } from 'react'

export interface MenuInfo {
  /**
   * 菜单ID
   */
  id: string | number
  /**
   * 父菜单ID
   */
  parentId: string | null
  /**
   * 菜单编码
   */
  code: string
  /**
   * 模块编码
   */
  module: string
  /**
   * 菜单路径
   */
  path: string
  /**
   * 菜单标题
   */
  title: string
  /**
   * 排序
   */
  sort: number
  /**
   * 访问形式
   */
  target: number
  /**
   * 图标
   */
  icon?: ReactElement | string
  /**
   * 微应用编码
   */
  microapp?: string
  /**
   * 归组
   */
  group?: string
  /**
   * 默认展开
   */
  defaultOpen?: boolean
  /**
   * 子菜单
   */
  children?: Omit<MenuInfo, 'group' | 'defaultOpen'>[]
  /**
   * 查询参数
   */
  query?: string
  /**
   * 是否有侧边栏
   */
  hadSide?: boolean
  /**
   * 是否有顶部导航
   */
  hadHeader?: boolean
  /**
   * 在菜单中隐藏
   */
  hiddenInMenu?: number
  /**
   * 是否启用
   */
  enabled?: boolean
}
