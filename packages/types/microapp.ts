export interface MicroAppInfo {
  /**
   * 微应用ID
   */
  id: string | number
  /**
   * 微应用名称
   */
  name: string
  /**
   * 微应用编码
   */
  code: string
  /**
   * 微应用资源路径
   */
  url: string
  /**
   * 查询参数
   */
  query?: string
  /**
   * 微应用图标
   */
  icon?: string
  /**
   * 微应用描述
   */
  description?: string
  /**
   * 微应用加载器
   */
  loader: number
  /**
   * 微应用配置
   */
  config?: Record<string, unknown>
  /**
   * 是否服务端渲染
   */
  isSSR?: number
  /**
   * 授权类型
   */
  authType?: 'ticket' | 'jwt' | 'parent'
  /**
   * 排序
   */
  sort?: number
  /**
   * 版本
   */
  version: string
  /**
   * 是否启用
   */
  enabled: boolean
}
