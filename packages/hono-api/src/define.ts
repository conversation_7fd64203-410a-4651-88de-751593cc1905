import type { Env, Hono, Schema } from 'hono'
import type { BlankEnv, BlankSchema } from 'hono/types'
import type { CreateNextApiServerOptions } from './server'

export type DefineRouteFn = (app: Hono, opts: CreateNextApiServerOptions) => void

export function createDefineRoute<E extends Env = BlankEnv, S extends Schema = BlankSchema, BasePath extends string = '/'>(
  app: Hono<E, S, BasePath>,
  opts?: CreateNextApiServerOptions<BasePath>
) {
  return function defineRoute(callback: DefineRouteFn) {
    return callback(app as never, opts as never)
  }
}
