import type { Hono } from 'hono'
import qs from 'query-string'

import env from '../env'
import { deployUrlMiddleware } from '../middlewares'
import { removeJwtCookie } from '../utils'

export default function logout(app: Hono) {
  app.get('/logout', deployUrlMiddleware(env.PORTAL_AUTH_URL.startsWith('https') ? 'https' : void 0), (ctx) => {
    const redicectTo = qs.stringifyUrl({ url: `${ctx.var.DEPLOY_URL}/api/auth/callback`, query: ctx.req.query() })

    const redirectUrl = qs.stringifyUrl({
      url: env.PORTAL_LOGOUT_URL,
      query: { redirect: redicectTo, appId: env.PORTAL_APP_ID, type: 'op' },
    })
    removeJwtCookie(ctx)
    return ctx.redirect(redirectUrl)
  })
}
