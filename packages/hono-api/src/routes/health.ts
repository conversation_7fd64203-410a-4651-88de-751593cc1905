import { zValidator } from '@hono/zod-validator'
import type { Hono } from 'hono'
import { z } from 'zod'

export default function health(app: Hono) {
  app.get('/health', zValidator('query', z.object({ _d: z.string().optional() })), (c) => {
    const _d = c.req.valid('query')._d

    if (_d === 'true') {
      return c.json({
        success: true,
        status: 'ok',
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        nodeVersion: process.version,
        platform: process.platform,
      })
    }

    return c.json({ success: true, status: 'ok' })
  })
}
