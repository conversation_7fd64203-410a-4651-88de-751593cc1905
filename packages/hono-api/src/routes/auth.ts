import type { Hon<PERSON> } from 'hono'
import { HTTPException } from 'hono/http-exception'
import { validator } from 'hono/validator'
import qs from 'query-string'
import { tryit } from 'radash'

import env from '../env'
import { deployUrlMiddleware } from '../middlewares'
import type { CreateNextApiServerOptions } from '../server'
import { setJwtCookie } from '../utils'

export default function auth(app: Hono, opts: CreateNextApiServerOptions) {
  const basePath = opts.basePath

  const basePrefix = basePath === '/' ? '' : basePath

  app.get('/auth', deployUrlMiddleware(env.PORTAL_AUTH_URL.startsWith('https') ? 'https' : void 0), (ctx) => {
    const { PORTAL_AUTH_URL, PORTAL_APP_ID: appId } = env

    if (!PORTAL_AUTH_URL) {
      throw new HTTPException(500, { message: 'PORTAL_AUTH_URL is not set' })
    }

    const query = ctx.req.query()

    const redirect = qs.stringifyUrl({ url: `${ctx.var.DEPLOY_URL}${basePrefix}/auth/callback`, query })

    const redirectUrl = qs.stringifyUrl({ url: PORTAL_AUTH_URL, query: { redirect, appId, type: 'op' } })

    return ctx.redirect(redirectUrl)
  })

  app.get(
    '/auth/callback',
    validator('query', (value) => {
      const { ticket, _t, redirectTo, ...rest } = value

      const t = ticket || _t

      if (!t) {
        throw new HTTPException(400, { message: 'ticket not found' })
      }

      return {
        ticket: t,
        redirectTo: redirectTo as string,
        ...rest,
      }
    }),
    deployUrlMiddleware(),
    async (ctx) => {
      const { VALIDATE_TICKET_URL, PORTAL_APP_ID: appId } = env

      const { ticket, redirectTo, ...query } = ctx.req.valid('query')

      const [err, resp] = await tryit(fetch)(
        qs.stringifyUrl({ url: VALIDATE_TICKET_URL, query: appId ? { ticket, appId } : { _t: ticket } })
      )

      if (err) {
        throw new HTTPException(500, err)
      }

      const respData = await resp.json()

      const data = respData.data ?? respData

      if (resp.status !== 200) {
        throw new HTTPException(resp.status as never, respData)
      }

      if (!data.jwt) {
        throw new HTTPException(500, { message: 'jwt not found' })
      }

      setJwtCookie(ctx, data.jwt)

      let _redirectTo = redirectTo || '/'

      if (_redirectTo.startsWith('http')) {
        _redirectTo = '/'
      } else if (!_redirectTo.startsWith('/')) {
        _redirectTo = `/${_redirectTo}`
      }

      const redirectUrl = qs.stringifyUrl({ url: `${ctx.var.DEPLOY_URL}${_redirectTo}`, query })

      return ctx.redirect(redirectUrl)
    }
  )
}
