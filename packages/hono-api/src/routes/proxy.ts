import type { <PERSON><PERSON> } from 'hono'
import { HTTPException } from 'hono/http-exception'
import { proxy } from 'hono/proxy'
import qs from 'query-string'

import type { CreateNextApiServerOptions } from '../server'
import { getJwtCookie } from '../utils'

export default function iproxy(app: Hono, opts: CreateNextApiServerOptions) {
  const getApi = opts.getApi || (() => void 0)
  const basePath = opts.basePath

  const basePrefix = basePath === '/' ? '' : basePath || ''

  app.all('/proxy/:namespace/*', (ctx) => {
    const namespace = ctx.req.param('namespace')
    const prefix = `${basePrefix}/proxy/${namespace}`
    const realPath = ctx.req.path.replace(prefix, '')

    const api = getApi(namespace, realPath)

    if (!api) {
      throw new HTTPException(404, { message: `${realPath} is not found in ${namespace}` })
    }

    // TODO 待优化，应当可配置
    const headers: Record<string, string> = { ...ctx.req.header() }

    const jwt = getJwtCookie(ctx)
    if (namespace === 'zen' && jwt) {
      headers.Authorization = `Bearer ${jwt}`
    } else if (jwt) {
      headers['x-auth-token'] = jwt
    }

    return proxy(qs.stringifyUrl({ url: api, query: ctx.req.query() }), { ...ctx.req, headers })
  })
}
