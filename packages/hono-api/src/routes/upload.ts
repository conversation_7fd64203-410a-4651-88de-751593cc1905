import type { Hono } from 'hono'
import { HTTPException } from 'hono/http-exception'
import { proxy } from 'hono/proxy'
import qs from 'query-string'

import env from '../env'
import { getJwtCookie } from '../utils'

export default function upload(app: Hono) {
  app.post('/upload', (ctx) => {
    const apiUrl = qs.stringifyUrl({ url: env.ATTACHMENT_UPLOAD_URL, query: ctx.req.query() })

    if (!apiUrl) {
      throw new HTTPException(404, { message: 'ATTACHMENT_UPLOAD_URL is not found' })
    }

    const headers: Record<string, string> = { ...ctx.req.header() }

    const jwt = getJwtCookie(ctx)
    // TODO 待优化，应当可配置
    if (jwt) {
      headers['x-auth-token'] = jwt
    }

    return proxy(apiUrl, { ...ctx.req, headers })
  })
}
