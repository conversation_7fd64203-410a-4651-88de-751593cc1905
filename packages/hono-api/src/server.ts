import { type Env, Hono, type Schema } from 'hono'
import { logger } from 'hono/logger'
import { type RequestIdVariables, requestId } from 'hono/request-id'
import type { BlankEnv, BlankSchema } from 'hono/types'
import { handle } from 'hono/vercel'

import { type DefineRouteFn, createDefineRoute } from './define'
import { DEFAULT_ROUTES } from './routes'
import proxy from './routes/proxy'

export type NextApiRouteOptions<
  SubPath extends string = string,
  SubEnv extends Env = BlankEnv,
  SubSchema extends Schema = BlankSchema,
  SubBasePath extends string = '*',
> = [SubPath, Hono<SubEnv, SubSchema, SubBasePath>]

export interface BaseEnv extends Env {
  Variables?: RequestIdVariables
}

export interface CreateNextApiServerOptions<BasePath extends string = '/'> {
  basePath?: BasePath
  dynamic?: 'auto' | 'force-dynamic' | 'error' | 'force-static'
  getApi?: (namespace: string, path: string) => string | undefined
  routes?: DefineRouteFn[]
}

export function createNextApiServer<
  E extends BaseEnv = BaseEnv,
  S extends Schema = BlankSchema,
  BasePath extends string = '/api',
>(options: CreateNextApiServerOptions<BasePath> = {}) {
  const { basePath = '/api', dynamic = 'force-dynamic', getApi, routes = DEFAULT_ROUTES, ...restOpts } = options

  const app = new Hono<E, S, BasePath>().basePath(basePath)

  app.use(logger())
  app.use('*', requestId())

  const defineRoute = createDefineRoute(app, { basePath, dynamic, getApi, ...restOpts } as never)

  routes.forEach((route) => defineRoute(route))

  if (getApi) {
    defineRoute(proxy)
  }

  const requestHandler = handle(app)

  return {
    GET: requestHandler,
    POST: requestHandler,
    PUT: requestHandler,
    DELETE: requestHandler,
    defineRoute,
    dynamic,
    app,
  }
}
