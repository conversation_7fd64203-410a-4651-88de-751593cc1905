import { createMiddleware } from 'hono/factory'
import { HTTPException } from 'hono/http-exception'

import { getDeployUrl } from '../utils'

export function deployUrlMiddleware(proto?: 'http' | 'https') {
  return createMiddleware<{ Variables: { DEPLOY_URL: string } }>(async (ctx, next) => {
    const deployUrl = getDeployUrl(ctx, proto)
    if (!deployUrl) {
      throw new HTTPException(500, { message: 'DEPLOY_PATH is not required' })
    }
    ctx.set('DEPLOY_URL', deployUrl)
    await next()
  })
}
