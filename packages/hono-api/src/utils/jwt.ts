import type { Context } from 'hono'
import { deleteCookie, getCookie, setCookie } from 'hono/cookie'
import { jwtDecode } from 'jwt-decode'

export function setJwtCookie(ctx: Context, jwt: string) {
  const jwtResp = jwtDecode(jwt)
  let jwtExp = jwtResp.exp

  if (jwtExp && `${jwtExp}`.length < 13) {
    jwtExp *= 10 ** (13 - `${jwtExp}`.length)
  }

  const exp = jwtExp ? (jwtExp - Date.now()) / 1000 : void 0

  setCookie(ctx, 'cvte.jwt', jwt, { path: '/', httpOnly: true, sameSite: 'lax', maxAge: exp })
}

export function getJwtCookie(ctx: Context) {
  return getCookie(ctx, 'cvte.jwt')
}

export function removeJwtCookie(ctx: Context) {
  return deleteCookie(ctx, 'cvte.jwt')
}
