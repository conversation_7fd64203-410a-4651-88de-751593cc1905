import type { Context } from 'hono'

import env from '../env'

export function getDeployUrl(ctx: Context, proto?: 'http' | 'https') {
  const { DEPLOY_URL, ASSETS_BASE_PATH } = env

  const assetsBasePath = ASSETS_BASE_PATH ? `/${ASSETS_BASE_PATH.replace(/^\//g, '')}` : ''

  if (DEPLOY_URL) {
    return `${DEPLOY_URL}${assetsBasePath}`
  }

  const host = ctx.req.header('Host')
  const xForwardProto = ctx.req.header('X-Forwarded-Proto')
  let _proto = (proto || xForwardProto)?.startsWith('https') ? 'https' : 'http'

  if (env.NODE_ENV === 'development') {
    _proto = xForwardProto?.startsWith('https') ? 'https' : 'http'
  }

  return host ? `${_proto}://${host}${assetsBasePath}` : ''
}
