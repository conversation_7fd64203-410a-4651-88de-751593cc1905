{"name": "@repo/hono-api", "version": "0.0.0", "private": true, "license": "ISC", "author": "vic", "exports": {"./*": "./src/*.ts", ".": "./src/index.ts"}, "scripts": {"clean": "rimraf .turbo dist node_modules", "lint": "oxlint . && eslint .", "lint:fix": "prettier --check --write . && oxlint --fix --fix-suggestions . && eslint . --fix"}, "lint-staged": {"*": ["prettier --check --write", "oxlint --fix --fix-suggestions", "eslint --fix"]}, "dependencies": {"@repo/env": "workspace:*", "hono": "^4.7.11", "jwt-decode": "^4.0.0"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^20.11.24", "typescript": "5.5.4"}}