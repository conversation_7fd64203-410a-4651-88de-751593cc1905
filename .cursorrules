# DevOps Next - Cursor Rules

This project follows specific development guidelines and architectural patterns. Please refer to the following rule files:

## Core Architecture
- [Project Structure](.cursor/rules/01-project-structure.mdc)
- [Backend Architecture](.cursor/rules/backend-architecture.mdc)
- [Frontend Architecture](.cursor/rules/frontend-architecture.mdc)
- [Micro Frontend Architecture](.cursor/rules/micro-frontend-architecture.mdc)

## Development Standards
- [Coding Standards](.cursor/rules/coding-standards.mdc)
- [Development Guidelines](.cursor/rules/02-development-guidelines.mdc)
- [Development Toolchain](.cursor/rules/development-toolchain.mdc)

## API & Response Standards
- [API Response Structure](.cursor/rules/api-response-structure.mdc) - **统一API响应结构规范**
- [RBAC API Routes](.cursor/rules/rbac-api-routes.mdc)

## Database & Models
- [Database Models](.cursor/rules/database-models.mdc)
- [RBAC Database Schema](.cursor/rules/rbac-database-schema.mdc)
- [Prisma Soft Delete](.cursor/rules/prisma-soft-delete.mdc)

## RBAC System
- [RBAC Implementation Guide](.cursor/rules/rbac-implementation-guide.mdc)
- [RBAC Permission System](.cursor/rules/rbac-permission-system.mdc)
- [RBAC Service Architecture](.cursor/rules/rbac-service-architecture.mdc)
- [RBAC Middleware Usage](.cursor/rules/rbac-middleware-usage.mdc)

## UI & Design
- [Design System](.cursor/rules/design-system.mdc)

## Key Requirements

### API Response Structure (重要)
**所有API接口必须使用统一响应结构：**
```typescript
interface ApiResponse<T> {
  code: number    // 状态码
  message: string // 响应消息
  data: T         // 响应数据
}
```

使用响应工具函数：
- `success(c, data, message)` - 成功响应
- `fail(c, message, code, data)` - 失败响应
- 禁止直接使用 `c.json()` 返回响应

### Development Guidelines
- 严格遵循SOLID原则和行业最佳实践
- 使用第一性原理思考解决复杂问题
- 确保代码可读性和可维护性
- 为关键逻辑添加有意义的注释
- 合理组织代码结构并优化性能
- 确保安全性和适当的错误处理 
