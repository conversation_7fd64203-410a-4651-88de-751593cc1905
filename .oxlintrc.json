{"plugins": ["import", "nextjs", "react", "unicorn", "typescript", "oxc"], "categories": {}, "rules": {"for-direction": "warn", "no-async-promise-executor": "warn", "no-caller": "warn", "no-class-assign": "warn", "no-compare-neg-zero": "warn", "no-cond-assign": "warn", "no-const-assign": "warn", "no-constant-binary-expression": "warn", "no-constant-condition": "warn", "no-control-regex": "warn", "no-debugger": "warn", "no-delete-var": "warn", "no-dupe-class-members": "warn", "no-dupe-else-if": "warn", "no-dupe-keys": "warn", "no-duplicate-case": "warn", "no-empty-character-class": "warn", "no-empty-pattern": "warn", "no-empty-static-block": "warn", "no-eval": "warn", "no-ex-assign": "warn", "no-extra-boolean-cast": "warn", "no-func-assign": "warn", "no-global-assign": "warn", "no-import-assign": "warn", "no-invalid-regexp": "warn", "no-irregular-whitespace": "warn", "no-loss-of-precision": "warn", "no-new-native-nonconstructor": "warn", "no-nonoctal-decimal-escape": "warn", "no-obj-calls": "warn", "no-self-assign": "warn", "no-setter-return": "warn", "no-shadow-restricted-names": "warn", "no-sparse-arrays": "warn", "no-this-before-super": "warn", "no-unsafe-finally": "warn", "no-unsafe-negation": "warn", "no-unsafe-optional-chaining": "warn", "no-unused-labels": "warn", "no-unused-private-class-members": "warn", "no-unused-vars": "warn", "no-useless-catch": "warn", "no-useless-escape": "warn", "no-useless-rename": "warn", "no-with": "warn", "require-yield": "warn", "use-isnan": "warn", "valid-typeof": "warn", "oxc/bad-array-method-on-arguments": "warn", "oxc/bad-char-at-comparison": "warn", "oxc/bad-comparison-sequence": "warn", "oxc/bad-min-max-func": "warn", "oxc/bad-object-literal-comparison": "warn", "oxc/bad-replace-all-arg": "warn", "oxc/const-comparisons": "warn", "oxc/double-comparisons": "warn", "oxc/erasing-op": "warn", "oxc/missing-throw": "warn", "oxc/number-arg-out-of-range": "warn", "oxc/only-used-in-recursion": "warn", "oxc/uninvoked-array-callback": "warn", "react/jsx-key": "warn", "react/jsx-no-duplicate-props": "warn", "react/jsx-no-target-blank": "warn", "react/jsx-no-undef": "warn", "react/jsx-props-no-spread-multi": "warn", "react/no-children-prop": "warn", "react/no-danger-with-children": "warn", "react/no-direct-mutation-state": "warn", "react/no-find-dom-node": "warn", "react/no-is-mounted": "warn", "react/no-render-return-value": "warn", "react/no-string-refs": "warn", "react/void-dom-elements-no-children": "warn", "typescript/no-duplicate-enum-values": "warn", "typescript/no-extra-non-null-assertion": "warn", "typescript/no-misused-new": "warn", "typescript/no-non-null-asserted-optional-chain": "warn", "typescript/no-this-alias": "warn", "typescript/no-unsafe-declaration-merging": "warn", "typescript/no-useless-empty-export": "warn", "typescript/no-wrapper-object-types": "warn", "typescript/prefer-as-const": "warn", "typescript/triple-slash-reference": "warn", "unicorn/no-await-in-promise-methods": "warn", "unicorn/no-document-cookie": "warn", "unicorn/no-empty-file": "warn", "unicorn/no-invalid-fetch-options": "warn", "unicorn/no-invalid-remove-event-listener": "warn", "unicorn/no-new-array": "warn", "unicorn/no-single-promise-in-promise-methods": "warn", "unicorn/no-thenable": "warn", "unicorn/no-unnecessary-await": "warn", "unicorn/no-useless-fallback-in-spread": "warn", "unicorn/no-useless-length-check": "warn", "unicorn/no-useless-spread": "warn", "unicorn/prefer-set-size": "warn", "unicorn/prefer-string-starts-ends-with": "warn"}, "settings": {"jsx-a11y": {"polymorphicPropName": null, "components": {}}, "react": {"formComponents": [], "linkComponents": []}, "jsdoc": {"ignorePrivate": false, "ignoreInternal": false, "ignoreReplacesDocs": true, "overrideReplacesDocs": true, "augmentsExtendsReplacesDocs": false, "implementsReplacesDocs": false, "exemptDestructuredRootsFromChecks": false, "tagNamePreference": {}}}, "env": {"builtin": true}, "globals": {}}