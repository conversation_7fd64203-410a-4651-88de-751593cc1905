import { FlatCompat } from '@eslint/eslintrc'
import libEslint from '@repo/eslint-config/library.mjs'
import { defineConfig } from 'eslint/config'

const compat = new FlatCompat({
  baseDirectory: import.meta.dirname,
})

export default defineConfig([
  libEslint,
  ...compat.config({
    ignorePatterns: ['frontends/**', 'backends/**', 'packages/**'],
    parser: '@typescript-eslint/parser',
    parserOptions: { project: true },
  }),
])
